#!/usr/bin/env python3
"""
WeatherFlow API Integration for AWOS Generation

Fetches weather data from WeatherFlow API and converts to AWOS format.
"""

import requests
import json
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class WeatherFetcher:
    def __init__(self, station_id: str, token: str):
        self.station_id = station_id
        self.token = token
        self.base_url = "https://swd.weatherflow.com/swd/rest/observations/station"

    def _calculate_density_altitude(self, air_temp_c: float, station_pressure_hpa: float, elevation_m: float) -> int:
        """
        Calculate density altitude using the standard FAA method.
        This method approximates DA and does not account for humidity.
        """
        # --- Step 1: Convert station elevation from meters to feet ---
        elevation_ft = elevation_m * 3.28084

        # --- Step 2: Calculate Pressure Altitude (PA) ---
        # PA is the station elevation corrected for non-standard pressure.
        # Standard pressure at sea level is 1013.25 hPa.
        # The pressure difference is converted to an altitude adjustment.
        # A common approximation is ~27.3 feet for every 1 hPa difference.
        standard_pressure_hpa = 1013.25
        pressure_altitude_ft = elevation_ft + ((standard_pressure_hpa - station_pressure_hpa) * 27.3)

        # --- Step 3: Calculate ISA Standard Temperature for the PA ---
        # ISA standard temp is 15°C at sea level and cools by ~2°C per 1000 ft.
        isa_temp_c = 15.0 - (2.0 * (pressure_altitude_ft / 1000.0))

        # --- Step 4: Calculate Density Altitude (DA) ---
        # DA = PA + [120 * (Outside Air Temp - ISA Temp)]
        temp_deviation = air_temp_c - isa_temp_c
        density_altitude_ft = pressure_altitude_ft + (120 * temp_deviation)

        return round(density_altitude_ft)

    def fetch_full_weather_data(self) -> Optional[Dict]:
        """Fetch current full weather data object from WeatherFlow API."""
        try:
            url = f"{self.base_url}/{self.station_id}"
            params = {"token": self.token}

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()

            if data.get("status", {}).get("status_code") != 0:
                logger.error(f"API error: {data.get('status', {}).get('status_message', 'Unknown error')}")
                return None

            if not data.get("obs"):
                logger.error("No observation data in API response")
                return None

            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch weather data: {e}")
            return None
        except (KeyError, IndexError, json.JSONDecodeError) as e:
            logger.error(f"Failed to parse weather data: {e}")
            return None

    def convert_to_awos_format(self, weather_data: Dict, elevation: float) -> Dict:
        """Convert WeatherFlow data to AWOS format."""
        try:
            # Extract values with defaults
            wind_avg = weather_data.get("wind_avg", 0)
            wind_direction = weather_data.get("wind_direction", 0)
            wind_gust = weather_data.get("wind_gust", 0)
            wind_lull = weather_data.get("wind_lull", 0)
            air_temperature = weather_data.get("air_temperature", 0)
            dew_point = weather_data.get("dew_point", 0)
            sea_level_pressure = weather_data.get("sea_level_pressure", 1013.25)
            station_pressure = weather_data.get("station_pressure", 1013.25)

            # Convert according to specifications
            awos_data = {
                "wind_speed": round(wind_avg),
                "wind_direction": round(wind_direction / 10) * 10,  # Round to nearest 10
                "temperature": round(air_temperature),
                "dew_point": round(dew_point),
                "altimeter": round(sea_level_pressure * 0.02953 * 100),  # mbar to inHg * 100
                "density_altitude": self._calculate_density_altitude(air_temperature, station_pressure, elevation)
            }

            # Handle wind gust logic: only include if (gust - lull) >= 10
            if (wind_gust - wind_lull) >= 10:
                awos_data["wind_gust"] = round(wind_gust)
            else:
                awos_data["wind_gust"] = None

            # Ensure wind direction is in proper format (000-360)
            if awos_data["wind_direction"] == 0:
                awos_data["wind_direction"] = 360
            elif awos_data["wind_direction"] > 360:
                awos_data["wind_direction"] = awos_data["wind_direction"] % 360
                if awos_data["wind_direction"] == 0:
                    awos_data["wind_direction"] = 360

            logger.info(f"Converted weather data: {awos_data}")
            return awos_data

        except (KeyError, TypeError, ValueError) as e:
            logger.error(f"Failed to convert weather data: {e}")
            raise

    def get_awos_data(self) -> Optional[Dict]:
        """Fetch and convert weather data to AWOS format."""
        full_data = self.fetch_full_weather_data()
        if full_data is None:
            return None

        # Extract the observation and elevation to pass to the conversion method
        weather_observation = full_data["obs"][0]
        station_elevation = full_data["elevation"]

        return self.convert_to_awos_format(weather_observation, station_elevation)


def main():
    """Test the weather fetcher."""
    import os

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Test with provided API details
    station_id = "185807"
    token = "04839500-dc95-4fed-8dbf-d02d90a0dd7c"

    fetcher = WeatherFetcher(station_id, token)
    awos_data = fetcher.get_awos_data()

    if awos_data:
        print("AWOS Data:")
        print(json.dumps(awos_data, indent=2))
    else:
        print("Failed to fetch weather data")


if __name__ == "__main__":
    main()