#!/usr/bin/env python3
"""
Test script for HLS WebSocket upload functionality.

This script tests the WebSocket connection and file upload to the VPS.
"""

import asyncio
import json
import logging
import tempfile
import time
from pathlib import Path
import websockets

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_websocket_upload():
    """Test WebSocket upload functionality."""
    
    # Configuration
    vps_url = "wss://awosnew.skytraces.com/hls/ws/185807"  # Replace with your VPS URL
    station_id = "185807"
    
    logger.info(f"Testing WebSocket upload to: {vps_url}")
    
    try:
        # Connect to WebSocket
        logger.info("Connecting to WebSocket...")
        async with websockets.connect(vps_url, ping_interval=20, ping_timeout=10) as websocket:
            logger.info("WebSocket connected successfully!")
            
            # Test 1: Upload a test segment
            await test_segment_upload(websocket)
            
            # Test 2: Upload a test playlist
            await test_playlist_upload(websocket)
            
            logger.info("All tests completed successfully!")
            
    except Exception as e:
        logger.error(f"WebSocket test failed: {e}")
        return False
    
    return True


async def test_segment_upload(websocket):
    """Test uploading a segment file."""
    logger.info("Testing segment upload...")
    
    # Create a test segment file
    test_content = b"Test HLS segment content - " + str(time.time()).encode()
    filename = f"test_segment_{int(time.time())}.ts"
    
    # Prepare metadata
    metadata = {
        'type': 'segment',
        'filename': filename,
        'size': len(test_content),
        'timestamp': time.time()
    }
    
    # Send metadata
    await websocket.send(json.dumps(metadata))
    logger.info(f"Sent metadata: {metadata}")
    
    # Send file data
    await websocket.send(test_content)
    logger.info(f"Sent file data: {len(test_content)} bytes")
    
    # Wait for response
    response_text = await asyncio.wait_for(websocket.recv(), timeout=10.0)
    response = json.loads(response_text)
    
    logger.info(f"Received response: {response}")
    
    if response.get('status') == 'success':
        logger.info("✓ Segment upload test PASSED")
    else:
        logger.error(f"✗ Segment upload test FAILED: {response.get('message')}")
        raise Exception(f"Segment upload failed: {response.get('message')}")


async def test_playlist_upload(websocket):
    """Test uploading a playlist file."""
    logger.info("Testing playlist upload...")
    
    # Create a test playlist
    playlist_content = """#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:2
#EXT-X-MEDIA-SEQUENCE:1
#EXTINF:1.0,
test_segment_1.ts
#EXTINF:1.0,
test_segment_2.ts
""".encode()
    
    filename = "playlist.m3u8"
    
    # Prepare metadata
    metadata = {
        'type': 'playlist',
        'filename': filename,
        'size': len(playlist_content),
        'timestamp': time.time()
    }
    
    # Send metadata
    await websocket.send(json.dumps(metadata))
    logger.info(f"Sent metadata: {metadata}")
    
    # Send file data
    await websocket.send(playlist_content)
    logger.info(f"Sent file data: {len(playlist_content)} bytes")
    
    # Wait for response
    response_text = await asyncio.wait_for(websocket.recv(), timeout=10.0)
    response = json.loads(response_text)
    
    logger.info(f"Received response: {response}")
    
    if response.get('status') == 'success':
        logger.info("✓ Playlist upload test PASSED")
    else:
        logger.error(f"✗ Playlist upload test FAILED: {response.get('message')}")
        raise Exception(f"Playlist upload failed: {response.get('message')}")


async def test_hls_uploader_class():
    """Test the HLSVPSUploader class directly."""
    logger.info("Testing HLSVPSUploader class...")
    
    # Import the uploader (need to set environment variables first)
    import os
    os.environ['HLS_VPS_UPLOAD_URL'] = 'https://awosnew.skytraces.com'
    os.environ['STATION_ID'] = '185807'
    
    from hls_vps_uploader import HLSVPSUploader
    
    # Create uploader instance
    uploader = HLSVPSUploader()
    
    if not uploader.is_enabled():
        logger.error("✗ Uploader is not enabled")
        return False
    
    logger.info("✓ Uploader is enabled")
    
    # Create test files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test segment file
        segment_path = temp_path / "test_segment.ts"
        segment_path.write_bytes(b"Test segment content")
        
        # Test playlist file
        playlist_path = temp_path / "playlist.m3u8"
        playlist_path.write_text("#EXTM3U\n#EXT-X-VERSION:3\n")
        
        # Queue uploads
        uploader.upload_segment(segment_path, "test_segment.ts")
        uploader.upload_playlist(playlist_path)
        
        logger.info("✓ Files queued for upload")
        
        # Wait a bit for uploads to process
        await asyncio.sleep(5)
        
        # Check stats
        stats = uploader.get_stats()
        logger.info(f"Upload stats: {stats}")
        
        # Stop uploader
        uploader.stop()
        
        logger.info("✓ HLSVPSUploader test completed")
    
    return True


async def main():
    """Main test function."""
    logger.info("Starting HLS upload tests...")
    
    try:
        # Test 1: Direct WebSocket connection
        logger.info("\n=== Test 1: Direct WebSocket Connection ===")
        success1 = await test_websocket_upload()
        
        # Test 2: HLSVPSUploader class
        logger.info("\n=== Test 2: HLSVPSUploader Class ===")
        success2 = await test_hls_uploader_class()
        
        if success1 and success2:
            logger.info("\n🎉 All tests PASSED!")
            return 0
        else:
            logger.error("\n❌ Some tests FAILED!")
            return 1
            
    except Exception as e:
        logger.error(f"\n💥 Test suite failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
