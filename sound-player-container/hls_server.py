#!/usr/bin/env python3
"""
Simple HTTP Server for HLS Streaming

This server serves HLS files from the /app/hls directory and provides
a simple web interface for testing HLS streaming functionality.
"""

import os
import logging
import threading
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse

from audio_config import AudioConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HLSRequestHandler(SimpleHTTPRequestHandler):
    """Custom HTTP request handler for HLS files."""
    
    def __init__(self, *args, **kwargs):
        # Set the directory to serve HLS files from
        self.hls_directory = Path(AudioConfig.HLS_OUTPUT_PATH)
        super().__init__(*args, directory=str(self.hls_directory), **kwargs)
    
    def end_headers(self):
        """Add CORS headers for cross-origin requests."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Add HLS-specific headers
        if self.path.endswith('.m3u8'):
            self.send_header('Content-Type', 'application/vnd.apple.mpegurl')
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        elif self.path.endswith('.ts'):
            self.send_header('Content-Type', 'video/mp2t')
            self.send_header('Cache-Control', 'max-age=3600')
        
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests."""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Serve the main HLS player page
        if path == '/' or path == '/player':
            self.serve_player_page()
        # Serve HLS status page
        elif path == '/status':
            self.serve_status_page()
        # Serve HLS files
        else:
            super().do_GET()
    
    def serve_player_page(self):
        """Serve a simple HLS player page with HLS.js support."""
        html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>AWOS HLS Stream Player</title>
    <meta charset="utf-8">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .player-section { margin: 20px 0; }
        .info-section { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .status { margin: 10px 0; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        audio { width: 100%; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .playlist-content { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 11px; max-height: 150px; overflow-y: auto; border: 1px solid #dee2e6; }
        .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
        .stat-item { background: #e9ecef; padding: 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 AWOS HLS Stream Player</h1>
            <p>Live audio streaming from Ridge Landing Airpark</p>
        </div>

        <div class="player-section">
            <h3>Audio Player</h3>
            <div id="playerStatus" class="status warning">Initializing player...</div>
            <audio id="audioPlayer" controls>
                Your browser does not support audio playback.
            </audio>
            <br>
            <button id="loadBtn" onclick="loadStream()">Load Stream</button>
            <button onclick="refreshPlayer()">Refresh</button>
            <button onclick="checkStatus()">Check Status</button>
            <button onclick="toggleAutoRefresh()" id="autoRefreshBtn">Auto-refresh: ON</button>
        </div>

        <div class="info-section">
            <h3>Stream Information</h3>
            <div class="stats">
                <div class="stat-item">Playlist URL: <code>/playlist.m3u8</code></div>
                <div class="stat-item">Segment Duration: 2.0 seconds</div>
                <div class="stat-item">Playlist Size: 6 segments</div>
                <div class="stat-item">Audio Format: AAC, 128kbps</div>
                <div class="stat-item">HLS.js Version: <span id="hlsVersion">Loading...</span></div>
                <div class="stat-item">Browser Support: <span id="browserSupport">Checking...</span></div>
            </div>
        </div>

        <div class="info-section">
            <h3>Live Statistics</h3>
            <div id="liveStats" class="stats">
                <div class="stat-item">Status: <span id="streamStatus">Unknown</span></div>
                <div class="stat-item">Segments: <span id="segmentCount">0</span></div>
                <div class="stat-item">Buffer: <span id="bufferSamples">0</span></div>
                <div class="stat-item">Last Update: <span id="lastUpdate">Never</span></div>
            </div>
        </div>

        <div class="info-section">
            <h3>Current Playlist</h3>
            <button onclick="loadPlaylist()">Refresh Playlist</button>
            <div id="playlistContent" class="playlist-content">Loading playlist...</div>
        </div>

        <div class="info-section">
            <h3>Player Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="playerLog" class="log">Player starting...</div>
        </div>
    </div>

    <script>
        let hls = null;
        let autoRefresh = true;
        let refreshInterval = null;
        let statsInterval = null;

        function log(message) {
            const logDiv = document.getElementById('playerLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('playerStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('playerLog').innerHTML = '';
        }

        function initializePlayer() {
            const audio = document.getElementById('audioPlayer');
            const playlistUrl = '/playlist.m3u8';

            // Check HLS.js support
            if (Hls.isSupported()) {
                document.getElementById('browserSupport').textContent = 'HLS.js supported';
                document.getElementById('hlsVersion').textContent = Hls.version;

                hls = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true,
                    backBufferLength: 30,
                    maxBufferLength: 30,
                    maxMaxBufferLength: 60,
                    liveSyncDurationCount: 3,
                    liveMaxLatencyDurationCount: 5,
                    liveDurationInfinity: true,
                    manifestLoadingTimeOut: 10000,
                    manifestLoadingMaxRetry: 3,
                    manifestLoadingRetryDelay: 1000,
                    levelLoadingTimeOut: 10000,
                    fragLoadingTimeOut: 20000
                });

                hls.loadSource(playlistUrl);
                hls.attachMedia(audio);

                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    log('Manifest parsed successfully');
                    updateStatus('Stream ready - click play to start', 'success');
                    document.getElementById('loadBtn').disabled = false;
                });

                hls.on(Hls.Events.ERROR, function(event, data) {
                    log(`HLS Error: ${data.type} - ${data.details}`);
                    if (data.fatal) {
                        updateStatus(`Fatal error: ${data.details}`, 'error');
                        switch(data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                log('Network error - retrying...');
                                hls.startLoad();
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                log('Media error - recovering...');
                                hls.recoverMediaError();
                                break;
                            default:
                                log('Unrecoverable error');
                                break;
                        }
                    }
                });

                hls.on(Hls.Events.FRAG_LOADED, function(event, data) {
                    log(`Fragment loaded: ${data.frag.url}`);
                });

            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {
                document.getElementById('browserSupport').textContent = 'Native HLS supported';
                document.getElementById('hlsVersion').textContent = 'Native';
                audio.src = playlistUrl;
                updateStatus('Native HLS player ready', 'success');
                log('Using native HLS support');
            } else {
                document.getElementById('browserSupport').textContent = 'Not supported';
                updateStatus('HLS not supported in this browser', 'error');
                log('HLS not supported in this browser');
            }
        }

        function loadStream() {
            const audio = document.getElementById('audioPlayer');
            if (hls) {
                hls.startLoad();
                log('HLS stream loading started');
            }
            audio.play().then(() => {
                log('Audio playback started');
                updateStatus('Playing live stream', 'success');
            }).catch(error => {
                log(`Playback error: ${error.message}`);
                updateStatus('Playback failed - check audio permissions', 'error');
            });
        }

        function refreshPlayer() {
            log('Refreshing player...');
            if (hls) {
                hls.destroy();
            }
            initializePlayer();
        }

        function checkStatus() {
            fetch('/status')
                .then(response => response.text())
                .then(data => {
                    log('Status check completed');
                    console.log('Status:', data);
                    updateLiveStats();
                })
                .catch(error => {
                    log(`Status check error: ${error.message}`);
                });
        }

        function loadPlaylist() {
            fetch('/playlist.m3u8')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('playlistContent').textContent = data;
                    log('Playlist refreshed');
                })
                .catch(error => {
                    document.getElementById('playlistContent').textContent = `Error loading playlist: ${error}`;
                    log(`Playlist error: ${error.message}`);
                });
        }

        function updateLiveStats() {
            fetch('/status')
                .then(response => response.text())
                .then(data => {
                    // Parse the status text to extract stats
                    const lines = data.split('\\n');
                    const stats = {};
                    lines.forEach(line => {
                        if (line.includes(':')) {
                            const [key, value] = line.split(':').map(s => s.trim());
                            stats[key] = value;
                        }
                    });

                    document.getElementById('streamStatus').textContent = stats['HLS Enabled'] || 'Unknown';
                    document.getElementById('segmentCount').textContent = stats['Segment Files'] || '0';
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
                })
                .catch(error => {
                    log(`Stats update error: ${error.message}`);
                });
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const btn = document.getElementById('autoRefreshBtn');
            btn.textContent = `Auto-refresh: ${autoRefresh ? 'ON' : 'OFF'}`;

            if (autoRefresh) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        }

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            if (statsInterval) clearInterval(statsInterval);

            // Refresh playlist every 3 seconds
            refreshInterval = setInterval(loadPlaylist, 3000);
            // Update stats every 5 seconds
            statsInterval = setInterval(updateLiveStats, 5000);

            log('Auto-refresh enabled (playlist: 3s, stats: 5s)');
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
            if (statsInterval) {
                clearInterval(statsInterval);
                statsInterval = null;
            }
            log('Auto-refresh disabled');
        }

        // Initialize on page load
        window.onload = function() {
            log('Page loaded - initializing player');
            initializePlayer();
            loadPlaylist();
            updateLiveStats();
            startAutoRefresh();
        };

        // Cleanup on page unload
        window.onbeforeunload = function() {
            if (hls) {
                hls.destroy();
            }
            stopAutoRefresh();
        };
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_status_page(self):
        """Serve HLS status information."""
        try:
            # Get HLS directory info
            hls_path = Path(AudioConfig.HLS_OUTPUT_PATH)
            
            status_info = {
                'hls_enabled': AudioConfig.ENABLE_HLS_STREAMING,
                'hls_directory': str(hls_path),
                'directory_exists': hls_path.exists(),
                'playlist_exists': (hls_path / 'playlist.m3u8').exists(),
                'segment_files': len(list(hls_path.glob('*.ts'))) if hls_path.exists() else 0,
                'total_files': len(list(hls_path.iterdir())) if hls_path.exists() else 0
            }
            
            # Try to get HLS subscriber stats from the running instance
            try:
                # Import the main application to get the running subscriber
                import main
                if hasattr(main, '_current_player') and main._current_player and main._current_player.hls_subscriber:
                    hls_stats = main._current_player.hls_subscriber.get_stats()
                    status_info['hls_stats'] = hls_stats
                else:
                    status_info['hls_stats'] = 'No active HLS subscriber found'
            except Exception as e:
                status_info['hls_stats_error'] = str(e)
            
            status_text = f"""HLS Streaming Status:
HLS Enabled: {status_info['hls_enabled']}
HLS Directory: {status_info['hls_directory']}
Directory Exists: {status_info['directory_exists']}
Playlist Exists: {status_info['playlist_exists']}
Segment Files: {status_info['segment_files']}
Total Files: {status_info['total_files']}

HLS Stats: {status_info.get('hls_stats', 'Not available')}
"""
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(status_text.encode('utf-8'))
            
        except Exception as e:
            error_text = f"Error getting status: {e}"
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(error_text.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to use our logger."""
        logger.info(f"{self.client_address[0]} - {format % args}")


class HLSServer:
    """HLS HTTP Server for serving streaming files."""
    
    def __init__(self, host='0.0.0.0', port=8080):
        """Initialize the HLS server."""
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
    
    def start(self):
        """Start the HLS server."""
        if self.running:
            logger.warning("HLS server is already running")
            return
        
        try:
            # Create HLS directory if it doesn't exist
            hls_path = Path(AudioConfig.HLS_OUTPUT_PATH)
            hls_path.mkdir(parents=True, exist_ok=True)
            
            # Create HTTP server
            self.server = HTTPServer((self.host, self.port), HLSRequestHandler)
            
            # Start server in background thread
            self.server_thread = threading.Thread(
                target=self.server.serve_forever,
                daemon=True
            )
            self.server_thread.start()
            self.running = True
            
            logger.info(f"HLS server started on http://{self.host}:{self.port}")
            logger.info(f"Player available at: http://{self.host}:{self.port}/player")
            logger.info(f"Playlist URL: http://{self.host}:{self.port}/playlist.m3u8")
            
        except Exception as e:
            logger.error(f"Failed to start HLS server: {e}")
            self.running = False
    
    def stop(self):
        """Stop the HLS server."""
        if not self.running:
            return
        
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)
            
            self.running = False
            logger.info("HLS server stopped")
            
        except Exception as e:
            logger.error(f"Error stopping HLS server: {e}")
    
    def is_running(self):
        """Check if the server is running."""
        return self.running


def main():
    """Main entry point for standalone server."""
    logger.info("Starting HLS Server...")
    
    # Check if HLS is enabled
    if not AudioConfig.ENABLE_HLS_STREAMING:
        logger.warning("HLS streaming is not enabled in configuration")
        logger.info("Set ENABLE_HLS_STREAMING=true to enable HLS streaming")
    
    server = HLSServer()
    
    try:
        server.start()
        
        if server.is_running():
            logger.info("HLS server is running. Press Ctrl+C to stop.")
            
            # Keep the main thread alive
            while server.is_running():
                import time
                time.sleep(1)
        else:
            logger.error("Failed to start HLS server")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Server error: {e}")
        return 1
    finally:
        server.stop()
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
