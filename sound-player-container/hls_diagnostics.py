#!/usr/bin/env python3
"""
HLS Streaming Diagnostics Tool

This script helps diagnose HLS streaming issues by checking:
- Audio device availability
- HLS configuration
- File system permissions
- Audio processing status
- HLS subscriber status
"""

import os
import sys
import logging
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_audio_devices():
    """Check audio device availability."""
    logger.info("=== Audio Device Check ===")
    
    try:
        from audio_device_manager import AudioDeviceManager
        manager = AudioDeviceManager()
        input_device, output_device = manager.detect_devices()
        
        logger.info(f"Input device: {input_device}")
        logger.info(f"Output device: {output_device}")
        
        if input_device is None:
            logger.error("❌ No audio input device detected!")
            return False
        else:
            logger.info("✅ Audio input device available")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error checking audio devices: {e}")
        return False


def check_hls_configuration():
    """Check HLS configuration."""
    logger.info("=== HLS Configuration Check ===")
    
    try:
        from audio_config import AudioConfig
        
        logger.info(f"HLS Enabled: {AudioConfig.ENABLE_HLS_STREAMING}")
        logger.info(f"HLS Output Path: {AudioConfig.HLS_OUTPUT_PATH}")
        logger.info(f"HLS Segment Duration: {AudioConfig.HLS_SEGMENT_DURATION}")
        logger.info(f"HLS Playlist Size: {AudioConfig.HLS_PLAYLIST_SIZE}")
        logger.info(f"HLS Audio Bitrate: {AudioConfig.HLS_AUDIO_BITRATE}")
        logger.info(f"HLS Audio Codec: {AudioConfig.HLS_AUDIO_CODEC}")
        
        if not AudioConfig.ENABLE_HLS_STREAMING:
            logger.error("❌ HLS streaming is disabled!")
            return False
        else:
            logger.info("✅ HLS streaming is enabled")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error checking HLS configuration: {e}")
        return False


def check_hls_directory():
    """Check HLS directory and permissions."""
    logger.info("=== HLS Directory Check ===")
    
    try:
        from audio_config import AudioConfig
        hls_path = Path(AudioConfig.HLS_OUTPUT_PATH)
        
        logger.info(f"HLS directory: {hls_path}")
        logger.info(f"Directory exists: {hls_path.exists()}")
        
        if not hls_path.exists():
            logger.info("Creating HLS directory...")
            hls_path.mkdir(parents=True, exist_ok=True)
        
        # Check write permissions
        test_file = hls_path / "test_write.tmp"
        try:
            test_file.write_text("test")
            test_file.unlink()
            logger.info("✅ HLS directory is writable")
            writable = True
        except Exception as e:
            logger.error(f"❌ HLS directory is not writable: {e}")
            writable = False
        
        # Check existing files
        playlist_file = hls_path / "playlist.m3u8"
        segment_files = list(hls_path.glob("*.ts"))
        
        logger.info(f"Playlist exists: {playlist_file.exists()}")
        logger.info(f"Segment files: {len(segment_files)}")
        
        if segment_files:
            logger.info("Recent segment files:")
            for segment in sorted(segment_files)[-3:]:
                stat = segment.stat()
                logger.info(f"  {segment.name}: {stat.st_size} bytes, modified {time.ctime(stat.st_mtime)}")
        
        return writable
        
    except Exception as e:
        logger.error(f"❌ Error checking HLS directory: {e}")
        return False


def check_ffmpeg():
    """Check FFmpeg availability."""
    logger.info("=== FFmpeg Check ===")
    
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            logger.info(f"✅ FFmpeg available: {version_line}")
            return True
        else:
            logger.error("❌ FFmpeg not working properly")
            return False
            
    except FileNotFoundError:
        logger.error("❌ FFmpeg not found in PATH")
        return False
    except Exception as e:
        logger.error(f"❌ Error checking FFmpeg: {e}")
        return False


def check_python_dependencies():
    """Check required Python dependencies."""
    logger.info("=== Python Dependencies Check ===")
    
    dependencies = [
        'numpy',
        'soundfile',
        'ffmpeg',
        'm3u8',
        'sounddevice',
        'scipy'
    ]
    
    all_available = True
    
    for dep in dependencies:
        try:
            __import__(dep)
            logger.info(f"✅ {dep} available")
        except ImportError:
            logger.error(f"❌ {dep} not available")
            all_available = False
    
    return all_available


def test_hls_subscriber():
    """Test HLS subscriber initialization."""
    logger.info("=== HLS Subscriber Test ===")
    
    try:
        from hls_audio_subscriber import HLSAudioSubscriber
        
        subscriber = HLSAudioSubscriber()
        logger.info("✅ HLS subscriber created successfully")
        
        # Test stats
        stats = subscriber.get_stats()
        logger.info(f"Initial stats: {stats}")
        
        # Test on_start
        subscriber.on_start()
        logger.info("✅ HLS subscriber started")
        
        # Check stats after start
        stats = subscriber.get_stats()
        logger.info(f"Stats after start: {stats}")
        
        if stats['is_streaming']:
            logger.info("✅ HLS subscriber is streaming")
        else:
            logger.warning("⚠️ HLS subscriber not streaming yet")
        
        # Test on_stop
        subscriber.on_stop()
        logger.info("✅ HLS subscriber stopped")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing HLS subscriber: {e}")
        return False


def test_audio_event_manager():
    """Test AudioEventManager integration."""
    logger.info("=== AudioEventManager Test ===")
    
    try:
        from audio_event_manager import AudioEventManager
        from audio_config import AudioConfig
        from hls_audio_subscriber import HLSAudioSubscriber
        from audio_device_manager import AudioDeviceManager
        
        # Get audio device
        manager = AudioDeviceManager()
        input_device, _ = manager.detect_devices()
        
        if not input_device:
            logger.error("❌ No audio input device for testing")
            return False
        
        # Create AudioEventManager
        audio_manager = AudioEventManager(
            sample_rate=AudioConfig.SAMPLE_RATE,
            channels=AudioConfig.CHANNELS,
            chunk_size=AudioConfig.CHUNK_SIZE,
            freq_min_hz=AudioConfig.FREQ_MIN_HZ,
            freq_max_hz=AudioConfig.FREQ_MAX_HZ
        )
        
        # Create HLS subscriber
        hls_subscriber = HLSAudioSubscriber()
        
        # Add subscriber
        audio_manager.add_subscriber(hls_subscriber)
        audio_manager.set_input_device(input_device)
        
        logger.info("✅ AudioEventManager and HLS subscriber configured")
        
        # Start audio processing
        if audio_manager.start():
            logger.info("✅ Audio processing started")
            
            # Wait a bit and check stats
            time.sleep(3)
            stats = hls_subscriber.get_stats()
            logger.info(f"HLS stats after 3 seconds: {stats}")
            
            if stats['is_streaming']:
                logger.info("✅ HLS streaming is active!")
            else:
                logger.warning("⚠️ HLS streaming not active")
            
            # Stop
            audio_manager.stop()
            logger.info("✅ Audio processing stopped")
            
            return True
        else:
            logger.error("❌ Failed to start audio processing")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing AudioEventManager: {e}")
        return False


def main():
    """Run all diagnostic checks."""
    logger.info("🔍 Starting HLS Streaming Diagnostics")
    logger.info("=" * 50)
    
    checks = [
        ("Python Dependencies", check_python_dependencies),
        ("Audio Devices", check_audio_devices),
        ("HLS Configuration", check_hls_configuration),
        ("HLS Directory", check_hls_directory),
        ("FFmpeg", check_ffmpeg),
        ("HLS Subscriber", test_hls_subscriber),
        ("AudioEventManager Integration", test_audio_event_manager),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        logger.info(f"\n--- {check_name} ---")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            logger.error(f"❌ {check_name} failed with exception: {e}")
            results.append((check_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 DIAGNOSTIC SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{check_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        logger.info("🎉 All diagnostics passed! HLS streaming should work.")
    else:
        logger.warning(f"⚠️ {total - passed} issues found. Please address them for proper HLS streaming.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
