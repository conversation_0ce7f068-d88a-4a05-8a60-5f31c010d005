#!/usr/bin/env python3
"""
HLS Streaming Demo

This script demonstrates how to use the HLS streaming functionality
with the AudioEventManager. It can be used to test the streaming
setup and verify that segments and playlists are being generated correctly.
"""

import logging
import time
import signal
import sys
from pathlib import Path

from audio_event_manager import AudioEventManager
from hls_audio_subscriber import HLSAudioSubscriber
from audio_device_manager import AudioDeviceManager
from audio_config import AudioConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HLSStreamingDemo:
    """Demo application for HLS streaming."""
    
    def __init__(self):
        """Initialize the demo."""
        self.audio_event_manager = None
        self.hls_subscriber = None
        self.running = False
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
    
    def setup(self):
        """Set up the demo environment."""
        logger.info("Setting up HLS streaming demo...")
        
        # Enable HLS streaming
        AudioConfig.ENABLE_HLS_STREAMING = True
        
        # Log configuration
        logger.info("HLS Configuration:")
        logger.info(f"  Enabled: {AudioConfig.ENABLE_HLS_STREAMING}")
        logger.info(f"  Segment Duration: {AudioConfig.HLS_SEGMENT_DURATION}s")
        logger.info(f"  Playlist Size: {AudioConfig.HLS_PLAYLIST_SIZE}")
        logger.info(f"  Output Path: {AudioConfig.HLS_OUTPUT_PATH}")
        logger.info(f"  Audio Bitrate: {AudioConfig.HLS_AUDIO_BITRATE}kbps")
        logger.info(f"  Audio Codec: {AudioConfig.HLS_AUDIO_CODEC}")
        
        # Create output directory
        output_path = Path(AudioConfig.HLS_OUTPUT_PATH)
        output_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"HLS output directory: {output_path}")
        
        # Detect audio devices
        audio_manager = AudioDeviceManager()
        input_device, output_device = audio_manager.detect_devices()
        
        if input_device:
            logger.info(f"Using audio input device: {input_device}")
        else:
            logger.warning("No audio input device detected")
            return False
        
        # Initialize AudioEventManager
        self.audio_event_manager = AudioEventManager(
            sample_rate=AudioConfig.SAMPLE_RATE,
            channels=AudioConfig.CHANNELS,
            chunk_size=AudioConfig.CHUNK_SIZE,
            freq_min_hz=AudioConfig.FREQ_MIN_HZ,
            freq_max_hz=AudioConfig.FREQ_MAX_HZ
        )
        
        # Set input device
        self.audio_event_manager.set_input_device(input_device)
        
        # Initialize HLS subscriber
        try:
            self.hls_subscriber = HLSAudioSubscriber()
            self.audio_event_manager.add_subscriber(self.hls_subscriber)
            logger.info("HLS subscriber initialized and registered")
        except Exception as e:
            logger.error(f"Failed to initialize HLS subscriber: {e}")
            return False
        
        return True
    
    def start(self):
        """Start the HLS streaming demo."""
        if not self.setup():
            logger.error("Failed to set up demo environment")
            return False
        
        logger.info("Starting HLS streaming demo...")
        
        # Start audio processing
        if not self.audio_event_manager.start():
            logger.error("Failed to start audio processing")
            return False
        
        self.running = True
        logger.info("HLS streaming demo started successfully")
        logger.info("Audio is being captured and converted to HLS segments")
        logger.info(f"Check {AudioConfig.HLS_OUTPUT_PATH} for generated files")
        logger.info("Press Ctrl+C to stop")
        
        return True
    
    def stop(self):
        """Stop the HLS streaming demo."""
        if not self.running:
            return
        
        logger.info("Stopping HLS streaming demo...")
        self.running = False
        
        # Stop audio processing
        if self.audio_event_manager:
            self.audio_event_manager.stop()
        
        logger.info("HLS streaming demo stopped")
    
    def run(self):
        """Run the demo."""
        if not self.start():
            return 1
        
        try:
            # Main loop - just wait and periodically show stats
            while self.running:
                time.sleep(10)  # Update every 10 seconds
                
                if self.hls_subscriber:
                    stats = self.hls_subscriber.get_stats()
                    logger.info("HLS Streaming Stats:")
                    logger.info(f"  Streaming: {stats['is_streaming']}")
                    logger.info(f"  Segments Created: {stats['segment_counter']}")
                    logger.info(f"  Playlist Segments: {stats['playlist_segments']}")
                    logger.info(f"  Buffer Samples: {stats['current_buffer_samples']}")
                    logger.info(f"  Upload Queue: {stats['upload_queue_size']}")
                    
                    # Show VPS upload stats
                    vps_stats = stats['vps_uploader']
                    logger.info(f"  VPS Uploads - Attempted: {vps_stats['uploads_attempted']}, "
                              f"Successful: {vps_stats['uploads_successful']}, "
                              f"Failed: {vps_stats['uploads_failed']}")
        
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        
        finally:
            self.stop()
        
        return 0
    
    def show_playlist_info(self):
        """Show information about the generated playlist."""
        if not self.hls_subscriber:
            return
        
        playlist_path = Path(self.hls_subscriber.get_playlist_url())
        if playlist_path.exists():
            logger.info(f"Playlist file: {playlist_path}")
            
            # Show playlist content
            with open(playlist_path, 'r') as f:
                content = f.read()
            
            logger.info("Playlist content:")
            for line in content.split('\n'):
                if line.strip():
                    logger.info(f"  {line}")
        else:
            logger.info("Playlist file not yet created")


def main():
    """Main entry point."""
    logger.info("HLS Streaming Demo")
    logger.info("=" * 50)
    
    # Check if FFmpeg is available
    import shutil
    if not shutil.which('ffmpeg'):
        logger.error("FFmpeg not found in PATH. Please install FFmpeg to use HLS streaming.")
        return 1
    
    demo = HLSStreamingDemo()
    return demo.run()


if __name__ == "__main__":
    sys.exit(main())
