# AWOS Recording Implementation

## Overview

This implementation enables the system to record the AWOS audio during transmission while temporarily disabling microphone click detection. The solution elegantly reuses the existing recording infrastructure by injecting AWOS audio chunks into the processing pipeline.

## Key Features

1. **AWOS Audio Recording**: Records the actual AWOS transmission instead of microphone input
2. **Click Detection Suspension**: Temporarily disables click detection during AWOS playback to prevent false triggers
3. **Relay Control Integration**: Coordinates relay engagement with audio recording
4. **DRY Implementation**: Reuses existing recording logic without duplication

## Architecture Changes

### 1. AudioEventManager Enhancements

**File**: `audio_event_manager.py`

- Added `inject_awos_audio_chunks()` method to feed AWOS audio into the processing pipeline
- Added suspension mechanism to skip microphone input during AWOS injection
- Handles sample rate conversion and audio format compatibility
- Thread-safe implementation with proper locking

**Key Methods**:
- `inject_awos_audio_chunks(audio_file_path)`: Reads AWOS file and injects chunks
- Modified `_audio_callback()`: Skips microphone input during injection

### 2. AWOS Controller Suspension

**File**: `awos_controller.py`

- Added click detection suspension mechanism
- Thread-safe suspension/resumption with proper locking
- Prevents false click detection from AWOS audio

**Key Methods**:
- `suspend_click_detection()`: Disables click processing during AWOS
- `resume_click_detection()`: Re-enables click processing after AWOS
- Modified `on_signal_detected()`: Checks suspension state before processing

### 3. Integrated Playback Control

**File**: `weather_sound_player.py`

- Enhanced `play_weather_report()` method with coordinated control
- Proper sequencing of relay, suspension, injection, and cleanup
- Parallel execution of audio injection and speaker playback

**Execution Sequence**:
1. Engage relay
2. Suspend click detection
3. Start AWOS audio injection (parallel thread)
4. Play AWOS through speakers
5. Wait for completion
6. Resume click detection
7. Disengage relay

## Technical Details

### Audio Processing Flow

```
AWOS File → AudioEventManager.inject_awos_audio_chunks()
    ↓
Audio Chunks → Processing Queue
    ↓
UnifiedSignalDetector → Signal Detection
    ↓
RecordingController → WebM Recording
    ↓
S3UploadManager → Cloud Storage
```

### Sample Rate Handling

- System operates at 48kHz for optimal signal detection
- AWOS files are typically 44.1kHz from audio components
- Automatic resampling using scipy.signal.resample when needed
- Maintains audio quality and timing accuracy

### Thread Safety

- All suspension mechanisms use threading.Lock()
- AWOS injection runs in separate daemon thread
- Proper cleanup ensures no resource leaks
- Graceful handling of interruptions

## Dependencies Added

- `soundfile`: For reading AWOS audio files with proper format handling
- Added to `requirements.txt`

## Testing

A comprehensive test suite is provided in `test_awos_recording.py`:

- **AudioEventManager Test**: Validates AWOS chunk injection
- **AWOSController Test**: Verifies suspension/resumption logic  
- **GpioController Test**: Tests relay control (mocked for safety)

Run tests with:
```bash
python test_awos_recording.py
```

## Benefits

1. **Elegant Solution**: Reuses existing recording infrastructure
2. **No Duplication**: Avoids duplicating recording logic
3. **Automatic Recording**: AWOS audio automatically triggers recording due to power levels
4. **Clean Integration**: Minimal changes to existing codebase
5. **Thread Safe**: Proper concurrency handling
6. **Resource Efficient**: No continuous monitoring overhead

## Usage

The implementation is transparent to users. After 3 microphone clicks:

1. System prepares AWOS audio
2. Relay engages
3. Click detection suspends
4. AWOS plays through speakers AND gets recorded
5. Recording saved to S3 with proper timestamps
6. Click detection resumes
7. Relay disengages
8. System returns to normal microphone monitoring

## File Structure

```
sound-player-container/
├── audio_event_manager.py      # Enhanced with AWOS injection
├── awos_controller.py          # Enhanced with suspension
├── weather_sound_player.py     # Enhanced with coordination
├── requirements.txt            # Added soundfile dependency
├── test_awos_recording.py      # Test suite
└── AWOS_RECORDING_IMPLEMENTATION.md  # This documentation
```

## Error Handling

- Graceful fallback if AWOS injection fails
- Proper cleanup in all error scenarios
- Relay always disengages even on failures
- Click detection always resumes after AWOS
- Comprehensive logging for debugging

## Performance Impact

- Minimal overhead when not playing AWOS
- Parallel execution prevents blocking
- Efficient memory usage with streaming chunks
- No impact on normal microphone monitoring
