# AWOS System Testing Guide

This guide provides comprehensive testing procedures for the AWOS system with Low Latency HLS streaming support.

## Prerequisites

1. **Docker Environment**: Ensure Docker is installed and running
2. **Audio Hardware**: Microphone and speakers connected to Raspberry Pi
3. **Network Access**: Internet connection for weather data and audio components
4. **GPIO Access**: Proper GPIO permissions for relay control

## Quick Start Testing

### 1. Build and Run Container

```bash
# Build the container (if needed)
docker build -t better-awos-relay .

# Run with HLS streaming enabled
docker run -d --name better-awos-relay \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e STATION_ID=185807 \
  -e TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c \
  -e ENABLE_HLS_STREAMING=true \
  -e HLS_SEGMENT_DURATION=2.0 \
  -e HLS_PLAYLIST_SIZE=6 \
  -p 8080:8080 \
  better-awos-relay
```

### 2. Monitor Startup

```bash
# Watch container logs
docker logs -f better-awos-relay

# Look for these key messages:
# ✓ All Python dependencies available
# ✓ All unified audio system modules available
# ✓ Audio components ready
# ✓ Using audio input device: [device]
# ✓ Using audio output device: [device]
# HLS subscriber initialized and registered
# Audio processing started
```

## Core System Testing

### 1. Audio System Verification

**Check Audio Devices:**
```bash
docker exec better-awos-relay python -c "
from audio_device_manager import AudioDeviceManager
manager = AudioDeviceManager()
input_dev, output_dev = manager.detect_devices()
print(f'Input: {input_dev}')
print(f'Output: {output_dev}')
"
```

**Expected Output:**
- Input device should be detected (e.g., USB microphone)
- Output device should be detected (e.g., USB audio or built-in)

### 2. Configuration Validation

**Check Configuration:**
```bash
docker exec better-awos-relay python -c "
from audio_config import AudioConfig
AudioConfig.log_configuration()
AudioConfig.validate_configuration()
"
```

**Expected Output:**
- All configuration values displayed
- No validation errors
- HLS streaming enabled if configured

### 3. Weather Data Testing

**Test Weather Fetching:**
```bash
docker exec better-awos-relay python -c "
from weather_fetcher import WeatherFetcher
fetcher = WeatherFetcher('185807', '04839500-dc95-4fed-8dbf-d02d90a0dd7c')
data = fetcher.get_awos_data()
print('Weather data:', data is not None)
"
```

**Expected Output:**
- Weather data successfully fetched
- No API errors

## Low Latency HLS Streaming Testing

### 1. HLS Diagnostics (Recommended First Step)

**Run HLS Diagnostics:**
```bash
docker exec hlswos python /app/hls_diagnostics.py
```

**Expected Output:**
```
🔍 Starting HLS Streaming Diagnostics
✅ Python Dependencies: PASS
✅ Audio Devices: PASS
✅ HLS Configuration: PASS
✅ HLS Directory: PASS
✅ FFmpeg: PASS
✅ HLS Subscriber: PASS
✅ AudioEventManager Integration: PASS
🎉 All diagnostics passed! HLS streaming should work.
```

### 2. HLS Functionality Test

**Run HLS Test Suite:**
```bash
docker exec hlswos python /app/test_hls_streaming.py
```

**Expected Output:**
```
✓ HLS subscriber initialization test passed
✓ Audio chunk processing test passed
✓ M3U8 playlist generation test passed
✓ AudioEventManager integration test passed
✓ VPS uploader interface test passed
✓ Statistics collection test passed
🎉 All tests passed!
```

### 3. HLS File Generation

**Check HLS Files:**
```bash
# List HLS directory contents
docker exec hlswos ls -la /app/hls/

# Expected files:
# playlist.m3u8 - Main playlist file
# segment_*.ts  - Audio segment files
```

**View Playlist Content:**
```bash
docker exec hlswos cat /app/hls/playlist.m3u8
```

**Expected Playlist Format:**
```
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:3
#EXT-X-MEDIA-SEQUENCE:1
#EXTINF:2.000000,
segment_000001.ts
#EXTINF:2.000000,
segment_000002.ts
...
```

### 3. Low Latency HLS Features

**Test LL-HLS Playlist:**
```bash
# Check for LL-HLS specific tags
curl -s http://localhost:8080/playlist.m3u8 | grep -E "(EXT-X-PART|EXT-X-PRELOAD|EXT-X-SERVER-CONTROL)"
```

**Expected LL-HLS Tags:**
```
#EXT-X-PART-INF:PART-TARGET=0.330
#EXT-X-SERVER-CONTROL:CAN-BLOCK-RELOAD=YES,PART-HOLD-BACK=1.000,CAN-SKIP-UNTIL=12.0
#EXT-X-PART:DURATION=0.330000,URI="part?msn=1&part=0"
#EXT-X-PRELOAD-HINT:TYPE=PART,URI="part?msn=1&part=1"
```

**Test Partial Segments:**
```bash
# Test partial segment access
curl -s "http://localhost:8080/part?msn=1&part=0" | wc -c
```

**Monitor Low Latency Performance:**
```bash
# Check latency statistics
curl -s http://localhost:8080/status | jq '.part_duration, .part_hold_back, .can_block_reload'
```

### 4. Real-time HLS Generation

**Monitor HLS Generation:**
```bash
# Watch HLS directory for new files (not used in LL-HLS - in-memory)
docker logs -f hlswos | grep -E "(partial|segment|LL-HLS)"

# Monitor LL-HLS statistics
curl -s http://localhost:8080/status | jq '.'
```

## Click Detection Testing

### 1. Manual Click Testing

**Generate Test Clicks:**
- Make 3 quick clicks near the microphone
- Wait for AWOS playback to trigger
- Make 4 quick clicks for radio check

**Expected Behavior:**
- 3 clicks → AWOS weather report plays
- 4 clicks → Radio check message logged
- GPIO relay engages during AWOS playback

### 2. Click Detection Monitoring

**Monitor Click Events:**
```bash
docker logs -f better-awos-relay | grep -E "(click|AWOS|Radio)"
```

**Expected Log Messages:**
```
Click detected: duration=0.25s, power=15.2
AWOS playback triggered by click detection!
✓ AWOS report played successfully
🔊 RADIO CHECK REQUESTED
```

## Recording System Testing

### 1. Recording Functionality

**Check Recording Directory:**
```bash
docker exec better-awos-relay ls -la /app/recordings/
```

**Expected Output:**
- Recording files created during AWOS playback
- Files named with timestamps
- WAV format audio files

### 2. S3 Upload Testing

**Monitor S3 Uploads:**
```bash
docker logs -f better-awos-relay | grep -E "(S3|upload)"
```

**Expected Output:**
- Successful S3 uploads logged
- No authentication errors
- Upload statistics displayed

## Troubleshooting

### Common Issues and Solutions

**1. No Audio Input Device**
```
Error: No audio input device detected
Solution: Check USB microphone connection, verify /dev permissions
```

**2. FFmpeg Not Found**
```
Error: FFmpeg not found in PATH
Solution: Rebuild container, FFmpeg should be installed in Dockerfile
```

**3. HLS Files Not Created**
```
Check: ENABLE_HLS_STREAMING=true
Check: Audio input is working
Check: /app/hls directory permissions
```

**4. Click Detection Not Working**
```
Check: Audio input levels
Check: SIGNAL_THRESHOLD_HIGH/LOW settings
Check: Microphone positioning
```

**5. Weather Data Fetch Fails**
```
Check: Network connectivity
Check: STATION_ID and TOKEN values
Check: API endpoint accessibility
```

### Debug Commands

**Enter Container for Debugging:**
```bash
docker exec -it better-awos-relay /bin/bash
```

**Check System Resources:**
```bash
docker exec better-awos-relay top
docker exec better-awos-relay df -h
docker exec better-awos-relay free -h
```

**Test Audio Devices:**
```bash
docker exec better-awos-relay aplay -l
docker exec better-awos-relay arecord -l
```

**Check Network:**
```bash
docker exec better-awos-relay ping -c 3 8.8.8.8
docker exec better-awos-relay curl -I https://api.weather.gov
```

## Performance Monitoring

### System Metrics

**CPU and Memory Usage:**
```bash
docker stats better-awos-relay
```

**HLS Performance:**
```bash
docker exec better-awos-relay python -c "
from hls_audio_subscriber import HLSAudioSubscriber
subscriber = HLSAudioSubscriber()
stats = subscriber.get_stats()
print('HLS Stats:', stats)
"
```

### Log Analysis

**Key Log Patterns to Monitor:**
- `✓` - Successful operations
- `✗` - Failed operations  
- `⚠` - Warnings
- `HLS` - HLS streaming events
- `AWOS` - Weather report events
- `Click` - Click detection events

**Log Filtering:**
```bash
# HLS-specific logs
docker logs better-awos-relay | grep HLS

# Error logs only
docker logs better-awos-relay | grep -E "(ERROR|✗|Failed)"

# Success logs only
docker logs better-awos-relay | grep -E "(✓|SUCCESS|successful)"
```

## Acceptance Criteria

### System Ready Checklist

- [ ] Container starts without errors
- [ ] Audio devices detected and configured
- [ ] Weather data fetching works
- [ ] Click detection responds to input
- [ ] AWOS playback functions correctly
- [ ] Recording system captures audio
- [ ] S3 uploads work (if enabled)
- [ ] HLS streaming generates segments and playlists
- [ ] GPIO relay control functions
- [ ] No memory leaks or resource issues

### HLS Streaming Checklist

- [ ] HLS subscriber initializes successfully
- [ ] Audio chunks are aggregated into segments
- [ ] M3U8 playlist is generated and updated
- [ ] Segment files are created in correct format
- [ ] Rolling playlist maintains correct size
- [ ] VPS upload interface is ready (mock mode)
- [ ] Statistics are collected and accessible
- [ ] No audio gaps or overlaps in segments

## Next Steps

After successful testing:

1. **Production Deployment**: Deploy to production environment
2. **VPS Upload Implementation**: Replace mock VPS upload with real HTTP uploads
3. **Monitoring Setup**: Implement production monitoring and alerting
4. **Performance Tuning**: Optimize based on real-world usage patterns
5. **Documentation**: Update operational documentation
