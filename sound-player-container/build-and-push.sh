#!/bin/bash

# Build and push script for AWOS system container
# Usage: ./build-and-push.sh [your-github-username]

set -e

# Configuration
GITHUB_USERNAME=${1:-"devtomsuys"}
IMAGE_NAME="hlswos"
REGISTRY="ghcr.io"
FULL_IMAGE_NAME="${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}"

echo "Setting up buildx builder..."
docker buildx create --name multiarch --use --bootstrap 2>/dev/null || docker buildx use multiarch

echo "Building and pushing AWOS System Docker image for ARM64 (Raspberry Pi 5)..."

# Build and push for ARM64 architecture (Raspberry Pi 5) in one step
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
docker buildx build \
  --platform linux/arm64 \
  --push \
  -t "${FULL_IMAGE_NAME}:latest" \
  -t "${FULL_IMAGE_NAME}:${TIMESTAMP}" \
  .

echo "Pushing to GitHub Container Registry..."
echo "Make sure you're logged in with: docker login ghcr.io -u ${GITHUB_USERNAME}"

echo "Build and push completed!"
echo "Image available at: ${FULL_IMAGE_NAME}:latest"
echo "Tagged version: ${FULL_IMAGE_NAME}:${TIMESTAMP}"
