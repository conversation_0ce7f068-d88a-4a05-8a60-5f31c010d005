"""
AWOS Audio Components Definition

This module defines all the audio components that need to be pre-downloaded
for offline AWOS text-to-speech generation.
"""

import os

# Get configurable airport advisory from environment variable
DEFAULT_AIRPORT_ADVISORY = "Ridge Landing Airpark automated advisory"
AIRPORT_ADVISORY = os.getenv("AIRPORT_ADVISORY", DEFAULT_AIRPORT_ADVISORY)

# Static phrases that appear in AWOS messages
STATIC_PHRASES = [
    AIRPORT_ADVISORY,
    "Wind",
    "at",
    "gusting",
    "Temperature",
    "Dew Point",
    "Altimeter",
    "Density altitude",
    "thousand",
    "hundred",
    "calm",
    "variable",
    "between",
    "and",
    "minus"
]

# Individual digits (0-9) - using aviation phonetic pronunciation
DIGITS = [
    "zero",
    "one", 
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "niner"  # Aviation standard for "nine"
]


# Complete list of all components to download
ALL_COMPONENTS = STATIC_PHRASES + DIGITS

def get_filename(text):
    """
    Convert text to a safe filename for audio storage.

    Args:
        text (str): The text to convert

    Returns:
        str: Safe filename without extension
    """
    # Replace spaces with underscores and remove special characters
    filename = text.lower().replace(" ", "_").replace(".", "")
    # Remove any other special characters that might cause issues
    safe_chars = "abcdefghijklmnopqrstuvwxyz0123456789_"
    filename = "".join(c for c in filename if c in safe_chars)
    return filename

# Pre-compute the audio mapping for better performance
_AUDIO_MAPPING = {component: get_filename(component) + ".wav" for component in ALL_COMPONENTS}

def get_audio_mapping():
    """
    Get a mapping of text to audio filenames.

    Returns:
        dict: Mapping of original text to audio filename
    """
    return _AUDIO_MAPPING.copy()  # Return a copy to prevent modification
