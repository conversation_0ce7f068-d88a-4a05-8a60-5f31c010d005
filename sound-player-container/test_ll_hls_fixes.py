#!/usr/bin/env python3
"""
Test script to verify LL-HLS fixes work correctly.
"""

import sys
import time
import numpy as np
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from audio_config import AudioConfig
from ll_hls_audio_subscriber import LowLatencyHLSAudioSubscriber
from audio_event_manager import AudioEvent

def test_ll_hls_subscriber():
    """Test that the LL-HLS subscriber can process audio events without errors."""
    print("🧪 Testing LL-HLS Audio Subscriber...")
    
    try:
        # Create subscriber
        subscriber = LowLatencyHLSAudioSubscriber()
        print("✅ LL-HLS subscriber created successfully")
        
        # Test on_start
        subscriber.on_start()
        print("✅ on_start() called successfully")
        
        # Create test audio event
        chunk = np.random.randint(-1000, 1000, 1024, dtype=np.int16)
        event = AudioEvent(
            chunk=chunk,
            timestamp=time.time(),
            sample_rate=48000,
            channels=1,
            power_in_range=1000.0
        )
        print("✅ Test AudioEvent created")
        
        # Test audio chunk processing
        subscriber.on_audio_chunk(event)
        print("✅ on_audio_chunk() processed successfully")
        
        # Test stats
        stats = subscriber.get_stats()
        print(f"✅ Stats retrieved: {stats}")
        
        # Test on_stop
        subscriber.on_stop()
        print("✅ on_stop() called successfully")
        
        print("🎉 All LL-HLS subscriber tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ LL-HLS subscriber test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ll_hls_server_import():
    """Test that the LL-HLS server can be imported without errors."""
    print("🧪 Testing LL-HLS Server Import...")
    
    try:
        from ll_hls_server import LowLatencyHLSServer
        print("✅ LL-HLS server imported successfully")
        
        # Create a mock subscriber for testing
        subscriber = LowLatencyHLSAudioSubscriber()
        
        # Create server instance
        server = LowLatencyHLSServer(subscriber, port=8081)
        print("✅ LL-HLS server created successfully")
        
        print("🎉 LL-HLS server import test passed!")
        return True
        
    except Exception as e:
        print(f"❌ LL-HLS server import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting LL-HLS Fix Validation Tests")
    print("=" * 50)
    
    tests = [
        test_ll_hls_subscriber,
        test_ll_hls_server_import,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! LL-HLS fixes are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
