#!/usr/bin/env python3
"""
Audio Device Manager

Handles automatic detection and configuration of audio input/output devices
for the AWOS system. Provides robust device detection with fallbacks.
"""

import subprocess
import re
import logging
import sounddevice as sd

logger = logging.getLogger(__name__)


class AudioDeviceManager:
    """Manages audio device detection and configuration."""
    
    def __init__(self):
        self.input_device = None
        self.output_device = None
    
    def detect_devices(self):
        """
        Comprehensive audio device detection for both input and output.
        Returns a tuple of (input_device, output_device) with the best available devices.
        """
        logger.info("Detecting optimal audio configuration...")
        
        # Try ALSA detection first
        try:
            self.input_device = self._detect_alsa_input_device()
            self.output_device = self._detect_alsa_output_device()
        except Exception as e:
            logger.warning(f"ALSA device detection failed: {e}")
        
        # Fallback to sounddevice detection if ALSA failed
        if self.input_device is None:
            self.input_device = self._detect_sounddevice_input()
        
        if self.output_device is None:
            self.output_device = self._detect_fallback_output()
        
        return self.input_device, self.output_device
    
    def _detect_alsa_input_device(self):
        """Detect input device using ALSA commands."""
        try:
            result = subprocess.run(['arecord', '-l'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return self._parse_alsa_devices(result.stdout, device_type='input')
        except Exception as e:
            logger.warning(f"ALSA input detection failed: {e}")
        return None
    
    def _detect_alsa_output_device(self):
        """Detect output device using ALSA commands."""
        try:
            result = subprocess.run(['aplay', '-l'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                device = self._parse_alsa_devices(result.stdout, device_type='output')
                if device:
                    # Test and return working device variant
                    return self._test_output_device_variants(device)
        except Exception as e:
            logger.warning(f"ALSA output detection failed: {e}")
        return None
    
    def _parse_alsa_devices(self, alsa_output, device_type='input'):
        """Parse ALSA device list output to find the best device."""
        lines = alsa_output.split('\n')
        devices = []
        
        for line in lines:
            # Look for device lines: "card X: DeviceName [Device Description], device Y: ..."
            match = re.search(r'card (\d+): ([^,]+).*device (\d+):', line)
            if match:
                card_num = match.group(1)
                device_name = match.group(2).strip()
                device_num = match.group(3)
                
                # Create device identifier
                hw_device = f"hw:{card_num},{device_num}"
                
                # Priority scoring
                priority = self._calculate_device_priority(line, device_type)
                
                # Create descriptive name
                display_name = self._create_display_name(device_name, line)
                
                devices.append({
                    'device': hw_device,
                    'name': display_name,
                    'card': card_num,
                    'priority': priority,
                    'full_line': line
                })
        
        if devices:
            # Sort by priority (highest first)
            devices.sort(key=lambda x: x['priority'], reverse=True)
            best_device = devices[0]
            logger.info(f"Selected {device_type} device: {best_device['device']} - {best_device['name']}")
            return best_device['device']
        
        logger.warning(f"No {device_type} devices found in ALSA output")
        return None
    
    def _calculate_device_priority(self, line, device_type):
        """Calculate priority score for a device based on its characteristics."""
        full_line_lower = line.lower()
        priority = 0
        
        # USB devices get highest priority
        if 'usb' in full_line_lower:
            priority += 100
        if 'audio' in full_line_lower:
            priority += 50
        if 'sound' in full_line_lower:
            priority += 25
        
        # For output devices, deprioritize HDMI (often causes issues)
        if device_type == 'output' and ('hdmi' in full_line_lower or 'vc4' in full_line_lower):
            priority -= 50
        
        return priority
    
    def _create_display_name(self, device_name, line):
        """Create a descriptive display name for the device."""
        display_name = device_name
        if '[' in line and ']' in line:
            bracket_match = re.search(r'\[([^\]]+)\]', line)
            if bracket_match:
                bracket_content = bracket_match.group(1)
                if bracket_content.lower() != device_name.lower():
                    display_name = f"{device_name} [{bracket_content}]"
        return display_name
    
    def _test_output_device_variants(self, hw_device):
        """Test both hw and plughw variants of a device, return the working one."""
        if hw_device.startswith('hw:'):
            card_device = hw_device[3:]  # Remove 'hw:' prefix
            plughw_device = f"plughw:{card_device}"
            
            # Try plughw first (better format conversion support)
            if self._test_output_device(plughw_device):
                return plughw_device
            
            # Try hw version
            if self._test_output_device(hw_device):
                return hw_device
            
            # If testing fails, prefer plughw for better compatibility
            return plughw_device
        else:
            # For non-hw devices, test as-is or return anyway
            if self._test_output_device(hw_device):
                return hw_device
            return hw_device
    
    def _test_output_device(self, device):
        """Test if an output device works by playing silence briefly."""
        try:
            cmd = ['aplay', '-D', device, '--duration=0.1', '/dev/zero']
            result = subprocess.run(cmd, capture_output=True, timeout=2)
            return result.returncode == 0
        except Exception:
            return False
    
    def _detect_sounddevice_input(self):
        """Fallback input device detection using sounddevice."""
        logger.info("Using sounddevice fallback for input device detection...")
        
        try:
            devices = sd.query_devices()
            candidates = []
            
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0 and device['hostapi'] == 0:  # ALSA on Linux
                    priority = 0
                    device_name_lower = device['name'].lower()
                    if 'usb' in device_name_lower:
                        priority += 100
                    if 'audio' in device_name_lower:
                        priority += 50
                    
                    candidates.append((i, device, priority))
            
            if candidates:
                candidates.sort(key=lambda x: x[2], reverse=True)
                device_index, device_info, _ = candidates[0]
                logger.info(f"Selected input device: {device_index} - {device_info['name']}")
                return device_index
            else:
                logger.warning("No suitable audio input devices found!")
                return None
        
        except Exception as e:
            logger.error(f"Error detecting input devices with sounddevice: {e}")
            return None
    
    def _detect_fallback_output(self):
        """Fallback output device detection with comprehensive testing."""
        logger.info("Using fallback method for output device detection...")
        
        # Test configurations in order of preference
        test_configs = [
            {'device': 'plughw:0,0', 'desc': 'Hardware device with format conversion (card 0)'},
            {'device': 'hw:0,0', 'desc': 'Direct hardware device (card 0)'},
            {'device': 'default', 'desc': 'Default ALSA device'},
            {'device': 'plughw:1,0', 'desc': 'Hardware device with format conversion (card 1)'},
            {'device': 'hw:1,0', 'desc': 'Direct hardware device (card 1)'},
            {'device': 'pulse', 'desc': 'PulseAudio device'}
        ]
        
        for config in test_configs:
            if self._test_output_device(config['device']):
                logger.info(f"✓ Using audio device: {config['desc']} ({config['device']})")
                return config['device']
        
        # Final fallback
        logger.warning("No optimal audio device found, using default")
        return 'default'


# Legacy function for backward compatibility
def detect_audio_devices():
    """Legacy function - use AudioDeviceManager instead."""
    manager = AudioDeviceManager()
    return manager.detect_devices()


def detect_usb_audio_device():
    """Legacy function - use AudioDeviceManager instead."""
    manager = AudioDeviceManager()
    input_device, _ = manager.detect_devices()
    return input_device
