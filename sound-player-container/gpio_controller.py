#!/usr/bin/env python3
"""
GPIO Controller for relay control during AWOS transmissions.
"""

import gpiod
import gpiod.line
import os
import logging
import threading

logger = logging.getLogger(__name__)

class GpioController:
    """Manages relay control for AWOS transmissions."""

    def __init__(self):
        # --- Configuration ---
        self.gpio_chip = os.getenv('GPIO_CHIP', 'gpiochip4')
        self.gpio_pin = int(os.environ.get('GPIO_PIN', '18'))
        self.lines = None
        self.lock = threading.Lock()
        self._initialized = False

    def start(self):
        """Initialize the GPIO controller and set relay to inactive state."""
        logger.info(f"Initializing GPIO controller on chip '{self.gpio_chip}', pin {self.gpio_pin}...")
        try:
            with self.lock:
                self.lines = gpiod.request_lines(
                    f'/dev/{self.gpio_chip}',
                    consumer='sound-player-relay',
                    config={
                        self.gpio_pin: gpiod.LineSettings(
                            direction=gpiod.line.Direction.OUTPUT,
                            output_value=gpiod.line.Value.INACTIVE
                        )
                    }
                )
                self._initialized = True
                logger.info("GPIO controller initialized successfully - relay set to INACTIVE")
        except FileNotFoundError:
            logger.error(f"GPIO chip '/dev/{self.gpio_chip}' not found. Ensure the container is run with the correct '--device' flag.")
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred initializing GPIO controller: {e}")
            raise

    def stop(self):
        """Stop the GPIO controller and release resources."""
        logger.info("Stopping GPIO controller...")
        try:
            with self.lock:
                if self.lines:
                    # Ensure relay is inactive before closing
                    self.lines.set_values({self.gpio_pin: gpiod.line.Value.INACTIVE})
                    self.lines.close()
                    self.lines = None
                    self._initialized = False
                    logger.info("GPIO controller stopped - relay set to INACTIVE")
        except Exception as e:
            logger.error(f"Error stopping GPIO controller: {e}")

    def engage_relay(self):
        """Engage the relay (set to ACTIVE state)."""
        try:
            with self.lock:
                if not self._initialized or not self.lines:
                    logger.error("GPIO controller not initialized - cannot engage relay")
                    return False

                self.lines.set_values({self.gpio_pin: gpiod.line.Value.ACTIVE})
                logger.info(f"Relay ENGAGED (pin {self.gpio_pin} set to HIGH)")
                return True
        except Exception as e:
            logger.error(f"Error engaging relay: {e}")
            return False

    def disengage_relay(self):
        """Disengage the relay (set to INACTIVE state)."""
        try:
            with self.lock:
                if not self._initialized or not self.lines:
                    logger.error("GPIO controller not initialized - cannot disengage relay")
                    return False

                self.lines.set_values({self.gpio_pin: gpiod.line.Value.INACTIVE})
                logger.info(f"Relay DISENGAGED (pin {self.gpio_pin} set to LOW)")
                return True
        except Exception as e:
            logger.error(f"Error disengaging relay: {e}")
            return False

    def is_initialized(self):
        """Check if GPIO controller is properly initialized."""
        with self.lock:
            return self._initialized and self.lines is not None