#!/usr/bin/env python3
"""
Test script to validate AWOS recording functionality.
This script tests the key components without requiring full system setup.
"""

import os
import sys
import time
import logging
import tempfile
import numpy as np
import soundfile as sf
from unittest.mock import Mock, patch

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from audio_event_manager import AudioEventManager
from awos_controller import AWOSController
from gpio_controller import GpioController

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_audio_file(duration_seconds=2.0, sample_rate=48000):
    """Create a test audio file with a simple tone."""
    # Generate a 1kHz sine wave
    t = np.linspace(0, duration_seconds, int(sample_rate * duration_seconds), False)
    frequency = 1000  # 1kHz tone
    audio_data = (np.sin(2 * np.pi * frequency * t) * 32767).astype(np.int16)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    sf.write(temp_file.name, audio_data, sample_rate)
    temp_file.close()
    
    return temp_file.name

def test_audio_event_manager():
    """Test AudioEventManager AWOS injection functionality."""
    logger.info("Testing AudioEventManager AWOS injection...")
    
    # Create test audio file
    test_file = create_test_audio_file(duration_seconds=1.0)
    
    try:
        # Create AudioEventManager
        manager = AudioEventManager(
            sample_rate=48000,
            channels=1,
            chunk_size=1024,
            freq_min_hz=200,
            freq_max_hz=3500
        )
        
        # Mock subscriber to capture events
        class TestSubscriber:
            def __init__(self):
                self.events = []
            
            def on_audio_chunk(self, event):
                self.events.append(event)
            
            def on_start(self):
                pass
            
            def on_stop(self):
                pass
        
        subscriber = TestSubscriber()
        manager.add_subscriber(subscriber)
        
        # Start processing thread (without actual audio stream)
        manager._stop_event.clear()
        import threading
        processing_thread = threading.Thread(target=manager._process_audio_chunks, daemon=True)
        processing_thread.start()
        
        # Test AWOS injection
        manager.inject_awos_audio_chunks(test_file)
        
        # Wait a bit for processing
        time.sleep(0.5)
        
        # Stop processing
        manager._stop_event.set()
        processing_thread.join(timeout=2.0)
        
        # Validate results
        if len(subscriber.events) > 0:
            logger.info(f"✓ AWOS injection successful: {len(subscriber.events)} audio events processed")
            logger.info(f"  Sample event: power={subscriber.events[0].power_in_range:.2f}, sample_rate={subscriber.events[0].sample_rate}")
            return True
        else:
            logger.error("✗ No audio events received from AWOS injection")
            return False
            
    except Exception as e:
        logger.error(f"✗ AudioEventManager test failed: {e}")
        return False
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)

def test_awos_controller():
    """Test AWOSController suspension functionality."""
    logger.info("Testing AWOSController click detection suspension...")
    
    try:
        # Create AWOS controller
        controller = AWOSController(
            click_min_duration=0.1,
            click_max_duration=0.6,
            click_cooldown=1.0,
            awos_click_count=3
        )
        
        # Mock signal event
        from unified_signal_detector import SignalEvent
        test_signal = SignalEvent(
            start_time=time.time(),
            end_time=time.time() + 0.2,
            duration=0.2,
            max_power=15.0,
            avg_power=12.0
        )
        
        # Test normal operation
        controller.on_signal_detected(test_signal)
        if controller.pending_click_sequence == 1:
            logger.info("✓ Normal click detection working")
        else:
            logger.error("✗ Normal click detection failed")
            return False
        
        # Test suspension
        controller.suspend_click_detection()
        initial_count = controller.pending_click_sequence
        controller.on_signal_detected(test_signal)
        
        if controller.pending_click_sequence == initial_count:
            logger.info("✓ Click detection suspension working")
        else:
            logger.error("✗ Click detection suspension failed")
            return False
        
        # Test resumption
        controller.resume_click_detection()
        controller.on_signal_detected(test_signal)
        
        if controller.pending_click_sequence == initial_count + 1:
            logger.info("✓ Click detection resumption working")
            return True
        else:
            logger.error("✗ Click detection resumption failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ AWOSController test failed: {e}")
        return False

def test_gpio_controller():
    """Test GpioController functionality (mocked)."""
    logger.info("Testing GpioController relay control...")
    
    try:
        # Mock the gpiod module since we're not on actual hardware
        with patch('gpio_controller.gpiod') as mock_gpiod:
            # Mock the request_lines context manager
            mock_lines = Mock()
            mock_gpiod.request_lines.return_value.__enter__.return_value = mock_lines
            mock_gpiod.request_lines.return_value.__exit__.return_value = None
            
            # Create GPIO controller
            controller = GpioController()
            
            # Test initialization
            controller.start()
            if controller.is_initialized():
                logger.info("✓ GPIO controller initialization working")
            else:
                logger.error("✗ GPIO controller initialization failed")
                return False
            
            # Test relay engagement
            if controller.engage_relay():
                logger.info("✓ Relay engagement working")
            else:
                logger.error("✗ Relay engagement failed")
                return False
            
            # Test relay disengagement
            if controller.disengage_relay():
                logger.info("✓ Relay disengagement working")
            else:
                logger.error("✗ Relay disengagement failed")
                return False
            
            # Test cleanup
            controller.stop()
            if not controller.is_initialized():
                logger.info("✓ GPIO controller cleanup working")
                return True
            else:
                logger.error("✗ GPIO controller cleanup failed")
                return False
                
    except Exception as e:
        logger.error(f"✗ GpioController test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting AWOS recording functionality tests...")
    
    tests = [
        ("AudioEventManager", test_audio_event_manager),
        ("AWOSController", test_awos_controller),
        ("GpioController", test_gpio_controller),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        result = test_func()
        results.append((test_name, result))
        logger.info(f"--- {test_name} {'PASSED' if result else 'FAILED'} ---\n")
    
    # Summary
    logger.info("=== TEST SUMMARY ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! AWOS recording functionality is ready.")
        return True
    else:
        logger.error("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
