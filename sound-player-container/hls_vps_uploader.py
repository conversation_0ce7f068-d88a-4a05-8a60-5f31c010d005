#!/usr/bin/env python3
"""
HLS VPS Uploader - WebSocket-based uploader for HLS segments and playlists to VPS

This module provides a robust WebSocket interface for uploading HLS streaming files
to a VPS server in real-time with automatic reconnection and error handling.
"""

import logging
import time
import json
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any
import threading
import queue
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException
from websockets.protocol import State

from audio_config import AudioConfig

logger = logging.getLogger(__name__)


class HLSVPSUploader:
    """
    WebSocket-based VPS uploader for HLS streaming files.

    This class handles uploading HLS segments and playlist files to a VPS server
    using WebSocket connections for real-time, robust file transfer with automatic
    reconnection and error handling.
    """

    def __init__(self):
        """Initialize the WebSocket VPS uploader."""
        self.upload_url = AudioConfig.HLS_VPS_UPLOAD_URL
        self.station_id = AudioConfig.STATION_ID
        self.enabled = bool(self.upload_url)

        # Convert HTTP URL to WebSocket URL
        if self.enabled:
            if self.upload_url.startswith('http://'):
                self.ws_url = self.upload_url.replace('http://', 'ws://') + f'/hls/ws/{self.station_id}'
            elif self.upload_url.startswith('https://'):
                self.ws_url = self.upload_url.replace('https://', 'wss://') + f'/hls/ws/{self.station_id}'
            else:
                self.ws_url = f'ws://{self.upload_url}/hls/ws/{self.station_id}'
        else:
            self.ws_url = None

        # Upload queue and worker thread
        self._upload_queue = queue.Queue()
        self._worker_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._websocket = None
        self._connection_lock = threading.Lock()
        self._reconnect_delay = 1.0  # Start with 1 second delay
        self._max_reconnect_delay = 60.0  # Max 60 seconds

        self._stats = {
            'uploads_attempted': 0,
            'uploads_successful': 0,
            'uploads_failed': 0,
            'connection_attempts': 0,
            'last_upload_time': None,
            'last_error': None,
            'connected': False
        }
        self._stats_lock = threading.Lock()

        if self.enabled:
            logger.info(f"HLS WebSocket Uploader initialized for: {self.ws_url}")
            self._start_worker()
        else:
            logger.info("HLS VPS Uploader disabled (no upload URL configured)")
    
    def _start_worker(self) -> None:
        """Start the background upload worker thread."""
        self._stop_event.clear()
        self._worker_thread = threading.Thread(target=self._upload_worker, daemon=True)
        self._worker_thread.start()
        logger.info("WebSocket upload worker thread started")

    def stop(self) -> None:
        """Stop the uploader and wait for pending uploads."""
        if not self.enabled:
            return

        logger.info("Stopping HLS WebSocket uploader...")
        self._stop_event.set()

        # Close WebSocket connection
        with self._connection_lock:
            if self._websocket:
                # Just mark as None, the connection will be cleaned up
                # when the worker thread stops
                self._websocket = None

        if self._worker_thread and self._worker_thread.is_alive():
            self._worker_thread.join(timeout=10.0)

        logger.info("HLS WebSocket uploader stopped")
    
    def upload_segment(self, segment_path: Path, segment_filename: str) -> None:
        """
        Queue a segment file for upload.

        Args:
            segment_path: Local path to the segment file
            segment_filename: Filename to use on the server
        """
        if not self.enabled:
            return

        upload_task = {
            'type': 'segment',
            'local_path': segment_path,
            'remote_filename': segment_filename,
            'timestamp': time.time()
        }

        self._upload_queue.put(upload_task)
        logger.debug(f"Queued segment upload: {segment_filename}")

    def upload_playlist(self, playlist_path: Path) -> None:
        """
        Queue a playlist file for upload.

        Args:
            playlist_path: Local path to the playlist file
        """
        if not self.enabled:
            return

        upload_task = {
            'type': 'playlist',
            'local_path': playlist_path,
            'remote_filename': 'playlist.m3u8',
            'timestamp': time.time()
        }

        self._upload_queue.put(upload_task)
        logger.debug("Queued playlist upload")
    
    def _upload_worker(self) -> None:
        """Background worker thread for processing WebSocket uploads."""
        logger.info("WebSocket upload worker started")

        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            loop.run_until_complete(self._async_upload_worker())
        finally:
            loop.close()

        logger.info("WebSocket upload worker stopped")

    async def _async_upload_worker(self) -> None:
        """Async worker loop for processing WebSocket uploads."""
        while not self._stop_event.is_set():
            try:
                # Get upload task with timeout (non-blocking)
                try:
                    upload_task = self._upload_queue.get_nowait()
                except queue.Empty:
                    # No tasks available, sleep briefly and continue
                    await asyncio.sleep(0.1)
                    continue

                # Process the upload
                success = await self._process_upload_async(upload_task)

                # Update statistics
                with self._stats_lock:
                    self._stats['uploads_attempted'] += 1
                    if success:
                        self._stats['uploads_successful'] += 1
                        self._stats['last_upload_time'] = time.time()
                        self._reconnect_delay = 1.0  # Reset reconnect delay on success
                    else:
                        self._stats['uploads_failed'] += 1

                # Mark task as done
                self._upload_queue.task_done()

            except Exception as e:
                logger.error(f"Error in WebSocket upload worker: {e}")
                with self._stats_lock:
                    self._stats['last_error'] = str(e)
                # Sleep briefly before retrying
                await asyncio.sleep(1.0)
    
    async def _process_upload_async(self, upload_task: Dict[str, Any]) -> bool:
        """
        Process a single upload task via WebSocket (async version).

        Args:
            upload_task: Upload task dictionary

        Returns:
            True if upload was successful, False otherwise
        """
        try:
            local_path = upload_task['local_path']
            remote_filename = upload_task['remote_filename']
            upload_type = upload_task['type']

            # Check if file exists
            if not local_path.exists():
                logger.warning(f"Upload file not found: {local_path}")
                return False

            # WebSocket upload implementation
            success = await self._websocket_upload_file(local_path, remote_filename, upload_type)

            if success:
                logger.debug(f"Successfully uploaded {upload_type}: {remote_filename}")
            else:
                logger.warning(f"Failed to upload {upload_type}: {remote_filename}")

            return success

        except Exception as e:
            logger.error(f"Error processing WebSocket upload: {e}")
            return False
    
    async def _websocket_upload_file(self, local_path: Path, remote_filename: str, upload_type: str) -> bool:
        """
        Upload file via WebSocket connection.

        Args:
            local_path: Local file path
            remote_filename: Remote filename
            upload_type: Type of upload ('segment' or 'playlist')

        Returns:
            True if upload was successful, False otherwise
        """
        try:
            # Ensure WebSocket connection
            if not await self._ensure_connection():
                return False

            # Read file data
            with open(local_path, 'rb') as f:
                file_data = f.read()

            file_size = len(file_data)

            # Prepare metadata
            metadata = {
                'type': upload_type,
                'filename': remote_filename,
                'size': file_size,
                'timestamp': time.time()
            }

            with self._connection_lock:
                if not self._websocket:
                    return False

                # Send metadata
                await self._websocket.send(json.dumps(metadata))

                # Send file data
                await self._websocket.send(file_data)

                # Wait for confirmation
                response_text = await asyncio.wait_for(self._websocket.recv(), timeout=30.0)
                response = json.loads(response_text)

                if response.get('status') == 'success':
                    logger.debug(f"WebSocket upload successful: {remote_filename} ({file_size} bytes)")
                    return True
                else:
                    logger.error(f"WebSocket upload failed: {response.get('message', 'Unknown error')}")
                    return False

        except asyncio.TimeoutError:
            logger.error(f"WebSocket upload timeout for {remote_filename}")
            await self._handle_connection_error()
            return False
        except (ConnectionClosed, WebSocketException) as e:
            logger.error(f"WebSocket connection error during upload: {e}")
            await self._handle_connection_error()
            return False
        except Exception as e:
            logger.error(f"Unexpected error during WebSocket upload: {e}")
            return False

    async def _ensure_connection(self) -> bool:
        """
        Ensure WebSocket connection is established.

        Returns:
            True if connection is ready, False otherwise
        """
        with self._connection_lock:
            if self._websocket and self._websocket.state == State.OPEN:
                return True

        # Need to establish connection
        return await self._connect()

    async def _connect(self) -> bool:
        """
        Establish WebSocket connection with retry logic.

        Returns:
            True if connection was established, False otherwise
        """
        with self._stats_lock:
            self._stats['connection_attempts'] += 1

        try:
            logger.info(f"Connecting to WebSocket: {self.ws_url}")

            # Connect with timeout
            websocket = await asyncio.wait_for(
                websockets.connect(self.ws_url, ping_interval=20, ping_timeout=10),
                timeout=10.0
            )

            with self._connection_lock:
                self._websocket = websocket

            with self._stats_lock:
                self._stats['connected'] = True

            logger.info("WebSocket connection established")
            return True

        except asyncio.TimeoutError:
            logger.error("WebSocket connection timeout")
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")

        with self._stats_lock:
            self._stats['connected'] = False
            self._stats['last_error'] = f"Connection failed: {str(e) if 'e' in locals() else 'timeout'}"

        return False

    async def _handle_connection_error(self) -> None:
        """Handle WebSocket connection errors and prepare for reconnection."""
        with self._connection_lock:
            if self._websocket:
                try:
                    await self._websocket.close()
                except:
                    pass
                self._websocket = None

        with self._stats_lock:
            self._stats['connected'] = False

        # Exponential backoff for reconnection
        self._reconnect_delay = min(self._reconnect_delay * 2, self._max_reconnect_delay)
        logger.warning(f"WebSocket connection lost. Will retry in {self._reconnect_delay} seconds")

        # Wait before next connection attempt
        await asyncio.sleep(self._reconnect_delay)

    def get_stats(self) -> Dict[str, Any]:
        """Get upload statistics."""
        with self._stats_lock:
            return self._stats.copy()

    def get_queue_size(self) -> int:
        """Get the current upload queue size."""
        return self._upload_queue.qsize()

    def is_enabled(self) -> bool:
        """Check if uploader is enabled."""
        return self.enabled

    def is_connected(self) -> bool:
        """Check if WebSocket is currently connected."""
        with self._stats_lock:
            return self._stats['connected']


