#!/usr/bin/env python3
"""
Unified Signal Detector - Consolidated signal detection for recording and AWOS

Replaces duplicate logic from AudioRecorder and ClickPatternDetector.
Uses hysteresis thresholding and only emits events for signals ≥ CLICK_MIN_DURATION.
"""

import logging
import time
from dataclasses import dataclass
from typing import List, Optional

from audio_event_manager import AudioEventSubscriber, AudioEvent

logger = logging.getLogger(__name__)


@dataclass
class SignalEvent:
    """Signal event data."""
    start_time: float
    end_time: float
    duration: float
    max_power: float
    avg_power: float


class UnifiedSignalDetector(AudioEventSubscriber):
    """Consolidated signal detector - replaces AudioRecorder and ClickPatternDetector logic."""

    def __init__(self, signal_threshold_high: float, signal_threshold_low: float, click_min_duration: float):
        self.signal_threshold_high = signal_threshold_high
        self.signal_threshold_low = signal_threshold_low
        self.click_min_duration = click_min_duration

        # State
        self.in_signal = False
        self.signal_start_time: Optional[float] = None
        self.power_accumulator: List[float] = []
        self._handlers = []

        logger.info(f"UnifiedSignalDetector: {signal_threshold_high}/{signal_threshold_low}, min: {click_min_duration}s")

    def add_handler(self, handler) -> None:
        """Add signal event handler."""
        self._handlers.append(handler)

    def on_start(self) -> None:
        """Start signal detection."""
        for handler in self._handlers:
            if hasattr(handler, 'on_start'):
                handler.on_start()

    def on_stop(self) -> None:
        """Stop signal detection."""
        if self.in_signal:
            self._finalize_signal(time.time())
        for handler in self._handlers:
            if hasattr(handler, 'on_stop'):
                handler.on_stop()

    def on_audio_chunk(self, event: AudioEvent) -> None:
        """Process audio chunk."""
        self._process_chunk(event.power_in_range, event.timestamp)
    
    def _process_chunk(self, power: float, timestamp: float) -> None:
        """Hysteresis signal detection."""
        if not self.in_signal and power > self.signal_threshold_high:
            # Signal start
            self.in_signal = True
            self.signal_start_time = timestamp
            self.power_accumulator = [power]
        elif self.in_signal and power < self.signal_threshold_low:
            # Signal end
            self._finalize_signal(timestamp)
        elif self.in_signal:
            # Continue signal
            self.power_accumulator.append(power)

    def _finalize_signal(self, end_time: float) -> None:
        """Finalize signal and notify handlers if duration ≥ min."""
        if not self.signal_start_time:
            return

        duration = end_time - self.signal_start_time
        self.in_signal = False

        # Only emit events for signals ≥ CLICK_MIN_DURATION (key requirement)
        if duration >= self.click_min_duration:
            max_power = max(self.power_accumulator) if self.power_accumulator else 0.0
            avg_power = sum(self.power_accumulator) / len(self.power_accumulator) if self.power_accumulator else 0.0

            # Log signal details on single line
            logger.info(f"Signal: {duration:.3f}s @ {self.signal_start_time:.3f}s | Max: {max_power:.0f} | Avg: {avg_power:.0f}")

            signal_event = SignalEvent(
                start_time=self.signal_start_time,
                end_time=end_time,
                duration=duration,
                max_power=max_power,
                avg_power=avg_power
            )

            # Notify all handlers
            for handler in self._handlers:
                handler.on_signal_detected(signal_event)
        else:
            # Log ignored short signals
            logger.debug(f"Signal ignored: {duration:.3f}s < {self.click_min_duration}s")

        # Reset
        self.signal_start_time = None
        self.power_accumulator = []
