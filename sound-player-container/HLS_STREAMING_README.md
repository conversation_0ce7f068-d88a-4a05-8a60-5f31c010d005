# HLS Streaming Implementation

This document describes the HLS (HTTP Live Streaming) implementation for real-time audio streaming from the Raspberry Pi container.

## Overview

The HLS streaming system integrates with the existing AudioEventManager to capture real-time audio and convert it into HLS-compatible segments and playlists. This enables live audio streaming that can be accessed via web browsers and media players.

## Architecture

### Components

1. **HLSAudioSubscriber** (`hls_audio_subscriber.py`)
   - Subscribes to AudioEventManager for real-time audio chunks
   - Aggregates 1024-sample chunks into 1-2 second segments
   - Converts audio to HLS format using FFmpeg
   - Generates and maintains M3U8 playlists

2. **HLSVPSUploader** (`hls_vps_uploader.py`)
   - Handles uploading HLS segments and playlists to VPS
   - Currently implements mock functionality
   - Provides interface for easy HTTP upload implementation

3. **Configuration** (in `audio_config.py`)
   - HLS-specific configuration options
   - Environment variable support

### Integration

The HLS subscriber integrates seamlessly with the existing audio pipeline:

```
AudioEventManager → HLSAudioSubscriber → HLS Segments + M3U8 → VPS Upload
                 ↘ UnifiedSignalDetector
                 ↘ RecordingController
```

## Configuration

### Environment Variables

```bash
# Enable HLS streaming
ENABLE_HLS_STREAMING=true

# Segment configuration
HLS_SEGMENT_DURATION=2.0          # Segment duration in seconds
HLS_PLAYLIST_SIZE=6               # Number of segments in playlist
HLS_OUTPUT_PATH=/app/hls          # Local output directory

# Audio encoding
HLS_AUDIO_BITRATE=128             # Audio bitrate in kbps
HLS_AUDIO_CODEC=aac               # Audio codec (aac recommended)

# VPS upload (for future implementation)
HLS_VPS_UPLOAD_URL=               # VPS endpoint URL
HLS_VPS_AUTH_TOKEN=               # Authentication token
```

### Default Configuration

- **Segment Duration**: 2.0 seconds (good balance between latency and efficiency)
- **Playlist Size**: 6 segments (12 seconds of audio buffer)
- **Audio Bitrate**: 128 kbps (good quality for voice)
- **Audio Codec**: AAC (widely supported)

## Usage

### Enabling HLS Streaming

1. Set environment variable:
   ```bash
   export ENABLE_HLS_STREAMING=true
   ```

2. Start the main application:
   ```bash
   python weather_sound_player.py
   ```

3. HLS files will be generated in `/app/hls/` (or configured path):
   - `playlist.m3u8` - Main playlist file
   - `segment_000001.ts`, `segment_000002.ts`, etc. - Audio segments

### Testing

Run the test suite:
```bash
python test_hls_streaming.py
```

Run the demo application:
```bash
python hls_streaming_demo.py
```

### Accessing the Stream

Once running, the HLS stream can be accessed via:
- **Playlist URL**: `http://your-vps/path/to/playlist.m3u8`
- **Local testing**: `file:///app/hls/playlist.m3u8`

## Technical Details

### Audio Processing

1. **Input**: 1024-sample chunks at 48kHz from AudioEventManager
2. **Aggregation**: Chunks are buffered until reaching segment duration
3. **Conversion**: Audio is converted to AAC using FFmpeg
4. **Segmentation**: Each segment is saved as MPEG-TS file (.ts)
5. **Playlist**: M3U8 playlist is updated with new segments

### Segment Management

- Segments are created every 2 seconds (configurable)
- Old segments are automatically removed to maintain playlist size
- Playlist uses rolling window approach for live streaming
- Media sequence numbers ensure proper playback order

### Threading

- Main audio processing runs in AudioEventManager thread
- Segment creation runs in HLS subscriber's main thread
- Background segment processor handles timeouts
- VPS upload runs in separate worker thread

## VPS Upload Implementation

### Current Status

The VPS upload functionality is currently implemented as a mock interface. To implement actual uploads:

1. **Uncomment HTTP code** in `hls_vps_uploader.py`
2. **Configure VPS endpoint** via environment variables
3. **Implement authentication** as needed

### Example Implementation

```python
def _real_upload_file(self, local_path, remote_filename, upload_type):
    """Real HTTP upload implementation."""
    with open(local_path, 'rb') as f:
        files = {'file': (remote_filename, f, 'application/octet-stream')}
        headers = {'Authorization': f'Bearer {self.auth_token}'}
        
        response = requests.post(
            f"{self.upload_url}/hls/upload",
            files=files,
            headers=headers,
            timeout=30
        )
        
        return response.status_code == 200
```

## Monitoring and Statistics

### Available Statistics

```python
stats = hls_subscriber.get_stats()
# Returns:
{
    'is_streaming': bool,
    'segment_counter': int,
    'playlist_segments': int,
    'current_buffer_samples': int,
    'sample_rate': int,
    'channels': int,
    'output_path': str,
    'vps_uploader': {...},
    'upload_queue_size': int
}
```

### Logging

The system provides detailed logging at various levels:
- **INFO**: Startup, configuration, segment creation
- **DEBUG**: Detailed processing information
- **ERROR**: Failures and exceptions

## Dependencies

### Required Packages

- `ffmpeg-python>=0.2.0` - FFmpeg Python bindings
- `m3u8>=3.5.0` - M3U8 playlist manipulation
- `soundfile` - Audio file I/O (already included)

### System Requirements

- **FFmpeg**: Must be installed on the system
- **Storage**: Adequate space for segment files
- **Network**: For VPS uploads (when implemented)

## Troubleshooting

### Common Issues

1. **FFmpeg not found**
   ```
   Error: FFmpeg not found in PATH
   Solution: Install FFmpeg on the system
   ```

2. **Permission denied on output directory**
   ```
   Error: Permission denied: /app/hls
   Solution: Ensure directory is writable
   ```

3. **Segments not created**
   ```
   Check: Audio input device is working
   Check: ENABLE_HLS_STREAMING=true
   Check: Sufficient audio input
   ```

### Debug Mode

Enable debug logging:
```python
import logging
logging.getLogger('hls_audio_subscriber').setLevel(logging.DEBUG)
logging.getLogger('hls_vps_uploader').setLevel(logging.DEBUG)
```

## Future Enhancements

1. **Adaptive Bitrate**: Multiple quality streams
2. **Video Support**: Add video streaming capability
3. **CDN Integration**: Direct upload to CDN services
4. **Encryption**: HLS encryption for secure streams
5. **Analytics**: Detailed streaming analytics

## Performance Considerations

- **CPU Usage**: FFmpeg encoding requires CPU resources
- **Storage**: Segments consume disk space (managed automatically)
- **Network**: Upload bandwidth affects VPS sync
- **Latency**: ~6-12 seconds end-to-end latency (configurable)

## Security Notes

- **Authentication**: Implement proper VPS authentication
- **HTTPS**: Use HTTPS for VPS uploads
- **Access Control**: Secure playlist access as needed
- **Token Management**: Rotate authentication tokens regularly
