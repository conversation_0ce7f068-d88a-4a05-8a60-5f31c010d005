#!/usr/bin/env python3
"""
S3 Upload Manager - Robust cloud storage upload with retry logic

This module provides a robust S3 upload manager with infinite retry logic,
exponential backoff, and proper error handling for uploading .opus files
to Cloudflare R2 storage.
"""

import logging
import os
import time
import threading
import queue
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Dict, Any
import zoneinfo

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from botocore.config import Config

logger = logging.getLogger(__name__)


class S3UploadManager:
    """
    Manages S3 uploads with robust retry logic and error handling.
    
    Features:
    - Infinite retry with exponential backoff
    - Thread-safe upload queue
    - Configurable retry parameters
    - Proper error logging and monitoring
    - Support for Cloudflare R2 and AWS S3
    """
    
    def __init__(self,
                 access_key_id: str,
                 secret_access_key: str,
                 endpoint_url: str,
                 bucket_name: str,
                 station_name: str,
                 station_timezone: str = 'UTC',
                 region_name: str = 'auto',
                 max_retry_delay: float = 300.0,  # 5 minutes max delay
                 initial_retry_delay: float = 1.0,
                 retry_multiplier: float = 2.0,
                 max_concurrent_uploads: int = 3):
        """
        Initialize the S3 Upload Manager.
        
        Args:
            access_key_id: S3 access key ID
            secret_access_key: S3 secret access key
            endpoint_url: S3 endpoint URL (for Cloudflare R2)
            bucket_name: Target S3 bucket name
            station_name: Station name for folder structure
            station_timezone: Timezone for date-based folder structure
            region_name: AWS region (use 'auto' for Cloudflare R2)
            max_retry_delay: Maximum delay between retries (seconds)
            initial_retry_delay: Initial delay between retries (seconds)
            retry_multiplier: Multiplier for exponential backoff
            max_concurrent_uploads: Maximum concurrent upload threads
        """
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self.endpoint_url = endpoint_url
        self.bucket_name = bucket_name
        self.station_name = station_name
        self.region_name = region_name
        
        # Retry configuration
        self.max_retry_delay = max_retry_delay
        self.initial_retry_delay = initial_retry_delay
        self.retry_multiplier = retry_multiplier
        
        # Timezone handling
        try:
            self.timezone = zoneinfo.ZoneInfo(station_timezone)
        except (zoneinfo.ZoneInfoNotFoundError, ValueError) as e:
            logger.warning(f"Invalid timezone '{station_timezone}': {e}, using UTC")
            self.timezone = timezone.utc
        
        # Threading and queue management
        self.upload_queue = queue.Queue()
        self.max_concurrent_uploads = max_concurrent_uploads
        self.upload_threads = []
        self.shutdown_event = threading.Event()
        self.stats_lock = threading.Lock()
        
        # Upload statistics
        self.stats = {
            'total_queued': 0,
            'total_uploaded': 0,
            'total_failed': 0,
            'current_queue_size': 0,
            'active_uploads': 0,
            'last_upload_time': None,
            'start_time': time.time()
        }
        
        # Initialize S3 client
        self.s3_client = None
        self._initialize_s3_client()

        # Validate S3 configuration
        self._validate_s3_configuration()

        # Start upload worker threads
        self._start_upload_workers()
    
    def _initialize_s3_client(self) -> None:
        """Initialize the S3 client with proper configuration."""
        try:
            # Configure boto3 to disable built-in retries (we handle retries ourselves)
            config = Config(
                retries={'max_attempts': 1},  # Disable boto3 retries
                max_pool_connections=50
            )

            self.s3_client = boto3.client(
                's3',
                endpoint_url=self.endpoint_url,
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region_name,
                config=config
            )
            logger.info(f"S3 client initialized for endpoint: {self.endpoint_url}")

        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            raise

    def _validate_s3_configuration(self) -> None:
        """Validate S3 configuration by testing bucket access."""
        try:
            # Test bucket access with a simple list operation
            self.s3_client.list_objects_v2(Bucket=self.bucket_name, MaxKeys=1)
            logger.info(f"S3 configuration validated - bucket '{self.bucket_name}' is accessible")

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                logger.error(f"S3 bucket '{self.bucket_name}' does not exist")
                raise Exception(f"S3 bucket '{self.bucket_name}' does not exist")
            elif error_code == 'AccessDenied':
                logger.error(f"Access denied to S3 bucket '{self.bucket_name}'")
                raise Exception(f"Access denied to S3 bucket '{self.bucket_name}'")
            else:
                logger.warning(f"S3 configuration test failed ({error_code}): {e}")
                # Don't raise here - some providers may not support list operations

        except Exception as e:
            logger.warning(f"S3 configuration validation failed: {e}")
            # Don't raise here - uploads may still work
    
    def _start_upload_workers(self) -> None:
        """Start the upload worker threads."""
        for i in range(self.max_concurrent_uploads):
            thread = threading.Thread(
                target=self._upload_worker,
                name=f"S3Upload-{i+1}",
                daemon=True
            )
            thread.start()
            self.upload_threads.append(thread)
        
        logger.info(f"Started {self.max_concurrent_uploads} S3 upload worker threads")
    
    def _upload_worker(self) -> None:
        """Worker thread for processing upload queue."""
        while not self.shutdown_event.is_set():
            try:
                # Get upload task with timeout
                upload_task = self.upload_queue.get(timeout=1.0)
                
                if upload_task is None:  # Shutdown signal
                    break
                
                # Update stats
                with self.stats_lock:
                    self.stats['active_uploads'] += 1
                    self.stats['current_queue_size'] = self.upload_queue.qsize()
                
                # Process the upload
                self._process_upload_with_retry(upload_task)
                
                # Mark task as done
                self.upload_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Upload worker error: {e}")
            finally:
                # Update stats
                with self.stats_lock:
                    self.stats['active_uploads'] = max(0, self.stats['active_uploads'] - 1)
    
    def _process_upload_with_retry(self, upload_task: Dict[str, Any]) -> None:
        """Process an upload task with infinite retry logic."""
        file_path = Path(upload_task['file_path'])
        s3_key = upload_task['s3_key']
        retry_count = 0
        delay = self.initial_retry_delay

        while not self.shutdown_event.is_set():
            try:
                # Check if file still exists before attempting upload
                if not file_path.exists():
                    logger.warning(f"File no longer exists, skipping upload: {file_path}")
                    return

                # Attempt the upload
                self._upload_file(file_path, s3_key)

                # Success!
                with self.stats_lock:
                    self.stats['total_uploaded'] += 1
                    self.stats['last_upload_time'] = time.time()

                logger.info(f"Successfully uploaded: {s3_key} (attempts: {retry_count + 1})")
                return

            except Exception as e:
                retry_count += 1

                # Don't increment failed count for file not found errors
                if "File not found" not in str(e):
                    with self.stats_lock:
                        self.stats['total_failed'] += 1

                # Log different levels based on retry count
                if retry_count <= 3:
                    logger.warning(f"Upload failed (attempt {retry_count}): {s3_key} - {e}")
                elif retry_count <= 10:
                    logger.error(f"Upload failed (attempt {retry_count}): {s3_key} - {e}")
                else:
                    # After 10 attempts, log less frequently to avoid spam
                    if retry_count % 10 == 0:
                        logger.error(f"Upload still failing (attempt {retry_count}): {s3_key} - {e}")

                # Wait before retry with exponential backoff
                if not self.shutdown_event.wait(delay):
                    # Calculate next delay
                    delay = min(delay * self.retry_multiplier, self.max_retry_delay)
                else:
                    # Shutdown requested
                    break
    
    def _upload_file(self, file_path: Path, s3_key: str) -> None:
        """Upload a single file to S3."""
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        try:
            file_size = file_path.stat().st_size
            logger.debug(f"Uploading {file_path.name} ({file_size} bytes) to {s3_key}")

            with open(file_path, 'rb') as f:
                # Determine content type based on file extension
                content_type = 'video/webm' if file_path.suffix.lower() == '.webm' else 'audio/opus'

                self.s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=f,
                    ContentType=content_type,
                    Metadata={
                        'original-filename': file_path.name,
                        'upload-timestamp': str(int(time.time())),
                        'file-size': str(file_size)
                    }
                )

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                raise Exception(f"Bucket '{self.bucket_name}' does not exist")
            elif error_code == 'AccessDenied':
                raise Exception(f"Access denied to bucket '{self.bucket_name}'")
            elif error_code == 'InvalidAccessKeyId':
                raise Exception(f"Invalid access key ID")
            elif error_code == 'SignatureDoesNotMatch':
                raise Exception(f"Invalid secret access key")
            else:
                raise Exception(f"S3 error ({error_code}): {e}")
        except OSError as e:
            raise Exception(f"File I/O error: {e}")
        except Exception as e:
            raise Exception(f"Upload error: {e}")
    
    def _generate_s3_key(self, file_path: Path, timestamp: float) -> str:
        """Generate S3 key with proper folder structure."""
        # Convert timestamp to station timezone
        dt = datetime.fromtimestamp(timestamp, tz=self.timezone)

        # Create folder structure: STATION_NAME/YYYY/MM/DD/HH/filename.webm
        s3_key = f"{self.station_name}/{dt.strftime('%Y/%m/%d/%H')}/{file_path.name}"

        return s3_key
    
    def queue_upload(self, file_path: str, timestamp: Optional[float] = None) -> None:
        """
        Queue a file for upload to S3.

        Args:
            file_path: Path to the file to upload
            timestamp: File timestamp (defaults to current time)
        """
        if timestamp is None:
            timestamp = time.time()

        file_path_obj = Path(file_path)

        # Validate file exists before queueing
        if not file_path_obj.exists():
            logger.warning(f"Cannot queue non-existent file: {file_path}")
            return

        # Validate file is not empty
        if file_path_obj.stat().st_size == 0:
            logger.warning(f"Cannot queue empty file: {file_path}")
            return

        s3_key = self._generate_s3_key(file_path_obj, timestamp)

        upload_task = {
            'file_path': str(file_path_obj),
            's3_key': s3_key,
            'timestamp': timestamp
        }

        self.upload_queue.put(upload_task)

        with self.stats_lock:
            self.stats['total_queued'] += 1
            self.stats['current_queue_size'] = self.upload_queue.qsize()

        logger.info(f"Queued for upload: {file_path_obj.name} -> {s3_key}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current upload statistics."""
        with self.stats_lock:
            return self.stats.copy()

    def get_formatted_stats(self) -> str:
        """Get formatted upload statistics for logging."""
        stats = self.get_stats()
        uptime = time.time() - stats['start_time']

        # Calculate upload rate
        upload_rate = stats['total_uploaded'] / uptime if uptime > 0 else 0

        # Format last upload time
        last_upload = "Never"
        if stats['last_upload_time']:
            last_upload_ago = time.time() - stats['last_upload_time']
            if last_upload_ago < 60:
                last_upload = f"{last_upload_ago:.0f}s ago"
            elif last_upload_ago < 3600:
                last_upload = f"{last_upload_ago/60:.0f}m ago"
            else:
                last_upload = f"{last_upload_ago/3600:.1f}h ago"

        return (f"S3 Stats: {stats['total_uploaded']}/{stats['total_queued']} uploaded "
                f"({stats['total_failed']} failed), {stats['current_queue_size']} queued, "
                f"{stats['active_uploads']} active, {upload_rate:.2f}/min rate, "
                f"last: {last_upload}")
    
    def shutdown(self, timeout: float = 30.0) -> None:
        """
        Shutdown the upload manager gracefully.

        Args:
            timeout: Maximum time to wait for pending uploads
        """
        logger.info("Shutting down S3 upload manager...")

        # Get current stats before shutdown
        final_stats = self.get_stats()
        if final_stats['current_queue_size'] > 0:
            logger.info(f"Waiting for {final_stats['current_queue_size']} pending uploads to complete...")

        # Signal shutdown
        self.shutdown_event.set()

        # Add shutdown signals to queue for each worker
        for _ in self.upload_threads:
            try:
                self.upload_queue.put(None, timeout=1.0)
            except queue.Full:
                logger.warning("Upload queue full during shutdown")
                break

        # Wait for workers to finish with individual timeouts
        thread_timeout = max(1.0, timeout / len(self.upload_threads)) if self.upload_threads else 1.0
        for i, thread in enumerate(self.upload_threads):
            logger.debug(f"Waiting for upload thread {i+1} to finish...")
            thread.join(timeout=thread_timeout)
            if thread.is_alive():
                logger.warning(f"Upload thread {i+1} did not finish within timeout")

        # Final stats
        final_stats = self.get_stats()
        logger.info(f"S3 upload manager shutdown complete. Final stats: {final_stats}")
