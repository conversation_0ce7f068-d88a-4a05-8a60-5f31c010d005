#!/usr/bin/env python3
"""
Test HLS Streaming Implementation

This script tests the HLS streaming functionality including:
- Audio chunk aggregation
- Segment generation
- M3U8 playlist creation
- Integration with AudioEventManager
- VPS upload interface (mock)
"""

import logging
import time
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock
import threading

import numpy as np

from audio_event_manager import AudioEvent, AudioEventManager
from hls_audio_subscriber import HLSAudioSubscriber
from audio_config import AudioConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockAudioDevice:
    """Mock audio device for testing."""
    
    def __init__(self, name="test_device"):
        self.name = name
    
    def __str__(self):
        return self.name


class HLSStreamingTester:
    """Test suite for HLS streaming functionality."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.temp_dir = None
        self.original_hls_path = None
        
    def setup(self):
        """Set up test environment."""
        logger.info("Setting up HLS streaming test environment...")
        
        # Create temporary directory for HLS output
        self.temp_dir = Path(tempfile.mkdtemp(prefix="hls_test_"))
        
        # Backup original HLS path and set test path
        self.original_hls_path = AudioConfig.HLS_OUTPUT_PATH
        AudioConfig.HLS_OUTPUT_PATH = str(self.temp_dir)
        
        logger.info(f"Test HLS output directory: {self.temp_dir}")
        
    def teardown(self):
        """Clean up test environment."""
        logger.info("Cleaning up test environment...")
        
        # Restore original HLS path
        if self.original_hls_path:
            AudioConfig.HLS_OUTPUT_PATH = self.original_hls_path
        
        # Remove temporary directory
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            logger.info(f"Removed test directory: {self.temp_dir}")
    
    def test_hls_subscriber_initialization(self):
        """Test HLS subscriber initialization."""
        logger.info("Testing HLS subscriber initialization...")
        
        try:
            subscriber = HLSAudioSubscriber()
            
            # Check basic properties
            assert subscriber.segment_duration == AudioConfig.HLS_SEGMENT_DURATION
            assert subscriber.playlist_size == AudioConfig.HLS_PLAYLIST_SIZE
            assert subscriber.output_path == Path(AudioConfig.HLS_OUTPUT_PATH)
            assert subscriber.audio_bitrate == AudioConfig.HLS_AUDIO_BITRATE
            
            # Check initial state
            assert not subscriber.is_streaming
            assert subscriber.segment_counter == 0
            assert len(subscriber.playlist_segments) == 0
            assert subscriber.current_samples == 0
            
            logger.info("✓ HLS subscriber initialization test passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ HLS subscriber initialization test failed: {e}")
            return False
    
    def test_audio_chunk_processing(self):
        """Test audio chunk processing and aggregation."""
        logger.info("Testing audio chunk processing...")
        
        try:
            subscriber = HLSAudioSubscriber()
            subscriber.on_start()
            
            # Create test audio chunks (1024 samples each at 48kHz)
            sample_rate = 48000
            chunk_size = 1024
            num_chunks = 100  # About 2.1 seconds of audio
            
            # Generate test audio chunks
            for i in range(num_chunks):
                # Create a simple sine wave chunk
                t = np.linspace(i * chunk_size / sample_rate, 
                               (i + 1) * chunk_size / sample_rate, 
                               chunk_size, endpoint=False)
                audio_chunk = (np.sin(2 * np.pi * 440 * t) * 16384).astype(np.int16)
                
                # Create AudioEvent
                event = AudioEvent(
                    chunk=audio_chunk,
                    timestamp=time.time(),
                    sample_rate=sample_rate,
                    channels=1,
                    power_in_range=1000.0
                )
                
                # Process chunk
                subscriber.on_audio_chunk(event)
                
                # Small delay to simulate real-time processing
                time.sleep(0.001)
            
            # Wait a bit for processing
            time.sleep(1.0)
            
            # Check that segments were created
            assert subscriber.segment_counter > 0, "No segments were created"
            assert len(subscriber.playlist_segments) > 0, "No playlist segments"
            
            # Check that files were created
            output_files = list(subscriber.output_path.glob("*.ts"))
            assert len(output_files) > 0, "No segment files created"
            
            playlist_file = subscriber.output_path / "playlist.m3u8"
            assert playlist_file.exists(), "Playlist file not created"
            
            subscriber.on_stop()
            
            logger.info(f"✓ Audio chunk processing test passed")
            logger.info(f"  Created {subscriber.segment_counter} segments")
            logger.info(f"  Playlist has {len(subscriber.playlist_segments)} entries")
            logger.info(f"  Found {len(output_files)} segment files")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ Audio chunk processing test failed: {e}")
            return False
    
    def test_playlist_generation(self):
        """Test M3U8 playlist generation."""
        logger.info("Testing M3U8 playlist generation...")
        
        try:
            subscriber = HLSAudioSubscriber()
            subscriber.on_start()
            
            # Generate some audio to create segments
            sample_rate = 48000
            chunk_size = 1024
            
            # Create enough chunks for 2 segments
            samples_per_segment = int(sample_rate * subscriber.segment_duration)
            chunks_per_segment = samples_per_segment // chunk_size
            total_chunks = chunks_per_segment * 2
            
            for i in range(total_chunks):
                t = np.linspace(0, chunk_size / sample_rate, chunk_size, endpoint=False)
                audio_chunk = (np.sin(2 * np.pi * 440 * t) * 16384).astype(np.int16)
                
                event = AudioEvent(
                    chunk=audio_chunk,
                    timestamp=time.time(),
                    sample_rate=sample_rate,
                    channels=1,
                    power_in_range=1000.0
                )
                
                subscriber.on_audio_chunk(event)
                time.sleep(0.001)
            
            # Wait for processing
            time.sleep(2.0)
            
            # Check playlist file
            playlist_file = subscriber.output_path / "playlist.m3u8"
            assert playlist_file.exists(), "Playlist file not created"
            
            # Read and validate playlist content
            with open(playlist_file, 'r') as f:
                playlist_content = f.read()
            
            # Basic M3U8 validation
            assert "#EXTM3U" in playlist_content, "Missing M3U8 header"
            assert "#EXT-X-VERSION:3" in playlist_content, "Missing version"
            assert "#EXT-X-TARGETDURATION:" in playlist_content, "Missing target duration"
            assert "#EXT-X-MEDIA-SEQUENCE:" in playlist_content, "Missing media sequence"
            assert ".ts" in playlist_content, "No segment files in playlist"
            
            subscriber.on_stop()
            
            logger.info("✓ M3U8 playlist generation test passed")
            logger.info(f"  Playlist content preview:")
            for line in playlist_content.split('\n')[:10]:
                logger.info(f"    {line}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ M3U8 playlist generation test failed: {e}")
            return False
    
    def test_integration_with_audio_event_manager(self):
        """Test integration with AudioEventManager."""
        logger.info("Testing integration with AudioEventManager...")
        
        try:
            # Create AudioEventManager with mock device
            manager = AudioEventManager(
                sample_rate=48000,
                channels=1,
                chunk_size=1024
            )
            
            # Create HLS subscriber
            subscriber = HLSAudioSubscriber()
            
            # Add subscriber to manager
            manager.add_subscriber(subscriber)
            
            # Verify subscriber was added
            assert len(manager._subscribers) == 1
            assert subscriber in manager._subscribers
            
            # Test subscriber lifecycle
            subscriber.on_start()
            assert subscriber.is_streaming
            
            subscriber.on_stop()
            assert not subscriber.is_streaming
            
            logger.info("✓ AudioEventManager integration test passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ AudioEventManager integration test failed: {e}")
            return False
    
    def test_vps_uploader_interface(self):
        """Test VPS uploader interface."""
        logger.info("Testing VPS uploader interface...")
        
        try:
            subscriber = HLSAudioSubscriber()
            
            # Check uploader initialization
            assert subscriber.vps_uploader is not None
            
            # Test stats
            stats = subscriber.vps_uploader.get_stats()
            assert 'uploads_attempted' in stats
            assert 'uploads_successful' in stats
            assert 'uploads_failed' in stats
            
            # Test queue size
            queue_size = subscriber.vps_uploader.get_queue_size()
            assert isinstance(queue_size, int)
            
            # Test enabled status
            enabled = subscriber.vps_uploader.is_enabled()
            assert isinstance(enabled, bool)
            
            logger.info("✓ VPS uploader interface test passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ VPS uploader interface test failed: {e}")
            return False
    
    def test_stats_collection(self):
        """Test statistics collection."""
        logger.info("Testing statistics collection...")
        
        try:
            subscriber = HLSAudioSubscriber()
            
            # Get initial stats
            stats = subscriber.get_stats()
            
            # Verify stats structure
            required_keys = [
                'is_streaming', 'segment_counter', 'playlist_segments',
                'current_buffer_samples', 'sample_rate', 'channels',
                'output_path', 'vps_uploader', 'upload_queue_size'
            ]
            
            for key in required_keys:
                assert key in stats, f"Missing stats key: {key}"
            
            # Check initial values
            assert stats['is_streaming'] == False
            assert stats['segment_counter'] == 0
            assert stats['playlist_segments'] == 0
            assert stats['current_buffer_samples'] == 0
            
            logger.info("✓ Statistics collection test passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Statistics collection test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all HLS streaming tests."""
        logger.info("Starting HLS streaming test suite...")
        
        self.setup()
        
        tests = [
            self.test_hls_subscriber_initialization,
            self.test_audio_chunk_processing,
            self.test_playlist_generation,
            self.test_integration_with_audio_event_manager,
            self.test_vps_uploader_interface,
            self.test_stats_collection
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"Test {test.__name__} crashed: {e}")
                failed += 1
            
            # Small delay between tests
            time.sleep(0.5)
        
        self.teardown()
        
        logger.info(f"HLS streaming test suite completed:")
        logger.info(f"  Passed: {passed}")
        logger.info(f"  Failed: {failed}")
        logger.info(f"  Total:  {passed + failed}")
        
        return failed == 0


def main():
    """Main test entry point."""
    logger.info("HLS Streaming Test Suite")
    logger.info("=" * 50)
    
    # Enable HLS streaming for testing
    original_hls_enabled = AudioConfig.ENABLE_HLS_STREAMING
    AudioConfig.ENABLE_HLS_STREAMING = True
    
    try:
        tester = HLSStreamingTester()
        success = tester.run_all_tests()
        
        if success:
            logger.info("🎉 All tests passed!")
            return 0
        else:
            logger.error("❌ Some tests failed!")
            return 1
            
    finally:
        # Restore original setting
        AudioConfig.ENABLE_HLS_STREAMING = original_hls_enabled


if __name__ == "__main__":
    exit(main())
