# Weather API Container

A Docker container that fetches weather station data, generates AWOS (Automated Weather Observing System) reports, and serves them via FastAPI with Twilio integration.

## Features

- **Automatic Weather Updates**: Fetches weather data every 2 minutes from WeatherFlow API
- **AWOS Generation**: Converts weather data to spoken AWOS reports using Edge TTS
- **MP3 Conversion**: Automatically converts generated WAV files to MP3 format
- **FastAPI Integration**: Serves weather data and audio files via REST API
- **Twilio Support**: Provides TwiML endpoint for phone-based weather reports
- **CORS Enabled**: <PERSON><PERSON> cross-origin requests properly
- **Health Monitoring**: Built-in health check endpoints

## API Endpoints

- `GET /` - Root endpoint (health check)
- `GET /health` - Detailed health check with service status
- `GET /4FL5/awos.mp3` - Serves the current AWOS audio file
- `GET|POST /4FL5/twiml` - Twilio TwiML endpoint for phone integration
- `GET /4FL5/weather` - Returns current weather data in JSON format

## Environment Variables

### Required Variables
- `STATION_ID` - WeatherFlow station ID (default: 185807)
- `TOKEN` - WeatherFlow API token (default: 04839500-dc95-4fed-8dbf-d02d90a0dd7c)
- `BASE_URL` - Base URL for the service (e.g., https://your-domain.com)

### Optional Variables
- `AIRPORT_ADVISORY` - Airport name for AWOS (default: "Ridge Landing Airpark automated advisory")
- `TTS_VOICE` - Edge TTS voice (default: "en-US-JennyNeural")
- `WORD_GAP` - Gap between words in seconds (default: -0.8)
- `PHRASE_GAP` - Gap between phrases in seconds (default: 0.2)
- `UPDATE_INTERVAL_SECONDS` - Weather update interval (default: 120)
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8000)

## Quick Start

### Using Docker Compose (Recommended)

1. Clone or download the container files
2. Update environment variables in `docker-compose.yml` if needed
3. Run the container:

```bash
docker-compose up -d
```

### Using Docker Run

```bash
docker run -d --name weather-api-container \
  -p 8000:8000 \
  -e STATION_ID=185807 \
  -e TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c \
  -e AIRPORT_ADVISORY="Ridge Landing Airpark automated advisory" \
  -e BASE_URL=https://your-domain.com \
  --restart unless-stopped \
  ghcr.io/devtomsuys/weather-api-container:latest
```

## Building from Source

1. Build the image:
```bash
./build-and-push.sh
```

2. Or manually:
```bash
docker build -t weather-api-container .
```

## Coolify Deployment

For deployment on Coolify:

1. Create a new service in Coolify
2. Use the Docker image: `ghcr.io/devtomsuys/weather-api-container:latest`
3. Set the required environment variables
4. Configure port mapping: `8000:8000`
5. Set the `BASE_URL` environment variable to your domain
6. Deploy the service

## Twilio Integration

To integrate with Twilio:

1. Set up a Twilio phone number
2. Configure the webhook URL to point to: `https://your-domain.com/4FL5/twiml`
3. The service will automatically respond with TwiML to play the current AWOS report

## Health Monitoring

The container includes health checks:

- Docker health check runs every 30 seconds
- `/health` endpoint provides detailed service status
- Monitors weather update frequency and file availability

## File Structure

```
weather-api-container/
├── main.py                 # FastAPI application
├── weather_service.py      # Background weather service
├── weather_fetcher.py      # WeatherFlow API integration
├── awos_generator.py       # AWOS audio generation
├── audio_components.py     # Audio component definitions
├── download_audio.py       # TTS audio download
├── requirements.txt        # Python dependencies
├── Dockerfile             # Container definition
├── docker-compose.yml     # Docker Compose configuration
├── build-and-push.sh      # Build and deployment script
└── README.md              # This file
```

## Troubleshooting

### Container won't start
- Check that all required environment variables are set
- Verify the WeatherFlow API token is valid
- Check container logs: `docker logs weather-api-container`

### No audio file available
- Wait for the first weather update (up to 2 minutes)
- Check that TTS voice is available
- Verify internet connectivity for Edge TTS

### Twilio integration not working
- Ensure `BASE_URL` is set correctly
- Verify the webhook URL in Twilio console
- Check that the service is accessible from the internet

## Support

For issues or questions, check the container logs and health endpoint for diagnostic information.
