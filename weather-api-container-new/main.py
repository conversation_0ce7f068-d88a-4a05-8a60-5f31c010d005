#!/usr/bin/env python3
"""
Weather API Container - FastAPI Application

Serves weather data via FastAPI with Twilio integration.
Fetches weather data every 2 minutes and generates AWOS MP3 files.
"""

import asyncio
import os
import logging
from pathlib import Path
from typing import Optional
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Response, HTTPException, UploadFile, File, Form, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import json
import time
import shutil

from weather_service import WeatherService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global weather service instance
weather_service: Optional[WeatherService] = None

# HLS storage configuration
HLS_STORAGE_PATH = Path("/app/hls_storage")
HLS_STORAGE_PATH.mkdir(parents=True, exist_ok=True)

# WebSocket connection manager for HLS uploads
class HLSConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"HLS WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"HLS WebSocket disconnected. Total connections: {len(self.active_connections)}")

hls_manager = HLSConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - startup and shutdown."""
    global weather_service
    
    # Startup
    logger.info("Starting Weather API Container...")
    
    # Initialize weather service
    weather_service = WeatherService()
    
    # Start background weather fetching task
    weather_task = asyncio.create_task(weather_service.start_weather_updates())
    
    try:
        yield
    finally:
        # Shutdown
        logger.info("Shutting down Weather API Container...")
        if weather_service:
            await weather_service.stop()
        weather_task.cancel()
        try:
            await weather_task
        except asyncio.CancelledError:
            pass

# Create FastAPI app with lifespan management
app = FastAPI(
    title="Weather API Container",
    description="Serves AWOS weather data with Twilio integration",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware to handle cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for Twilio integration
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint - health check."""
    return {
        "status": "healthy",
        "service": "Weather API Container",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    last_update = weather_service.get_last_update_time()
    return {
        "status": "healthy",
        "last_weather_update": last_update.isoformat() if last_update else None,
        "awos_file_exists": weather_service.awos_file_exists()
    }

@app.get("/4FL5/awos.mp3")
async def serve_awos_mp3():
    """Serve the current AWOS MP3 file."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    mp3_path = weather_service.get_awos_mp3_path()
    
    if not mp3_path.exists():
        raise HTTPException(status_code=404, detail="AWOS file not available")
    
    return FileResponse(
        path=str(mp3_path),
        media_type="audio/mpeg",
        filename="awos.mp3",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.post("/4FL5/twiml")
@app.get("/4FL5/twiml")
async def twilio_twiml():
    """Twilio TwiML endpoint - returns XML response to play AWOS audio."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    # Get the base URL from environment or construct it
    base_url = os.getenv("BASE_URL", "http://localhost:8000")
    audio_url = f"{base_url}/4FL5/awos.mp3"
    
    # Check if AWOS file exists
    if not weather_service.awos_file_exists():
        # Return TwiML with a fallback message
        twiml_response = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Weather information is currently unavailable. Please try again later.</Say>
</Response>"""
    else:
        # Return TwiML to play the AWOS audio
        twiml_response = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Play>{audio_url}</Play>
</Response>"""
    
    return Response(
        content=twiml_response,
        media_type="application/xml",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.get("/4FL5/weather")
async def get_weather_data():
    """Get current weather data in JSON format."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    weather_data = weather_service.get_current_weather_data()
    
    if not weather_data:
        raise HTTPException(status_code=404, detail="Weather data not available")
    
    return weather_data

# HLS Upload Endpoints

@app.post("/hls/upload")
async def upload_hls_file(
    file: UploadFile = File(...),
    file_type: str = Form(...),
    station_id: str = Form(...),
    timestamp: float = Form(...)
):
    """
    Upload HLS segment or playlist file.

    Args:
        file: The file to upload (segment .ts or playlist .m3u8)
        file_type: Type of file ('segment' or 'playlist')
        station_id: Station ID for organization
        timestamp: Upload timestamp
    """
    try:
        # Validate file type
        if file_type not in ['segment', 'playlist']:
            raise HTTPException(status_code=400, detail="Invalid file type. Must be 'segment' or 'playlist'")

        # Create station directory
        station_dir = HLS_STORAGE_PATH / station_id
        station_dir.mkdir(parents=True, exist_ok=True)

        # Determine file path
        if file_type == 'playlist':
            file_path = station_dir / "playlist.m3u8"
        else:
            # For segments, use the original filename
            file_path = station_dir / file.filename

        # Save the file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"HLS {file_type} uploaded: {file.filename} for station {station_id}")

        return {
            "status": "success",
            "file_type": file_type,
            "filename": file.filename,
            "station_id": station_id,
            "timestamp": timestamp,
            "size": file_path.stat().st_size
        }

    except Exception as e:
        logger.error(f"Error uploading HLS file: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.websocket("/hls/ws/{station_id}")
async def hls_websocket_endpoint(websocket: WebSocket, station_id: str):
    """
    WebSocket endpoint for real-time HLS file uploads.

    Protocol:
    - Client sends JSON message with file metadata
    - Client sends binary file data
    - Server responds with confirmation
    """
    await hls_manager.connect(websocket)

    try:
        # Create station directory
        station_dir = HLS_STORAGE_PATH / station_id
        station_dir.mkdir(parents=True, exist_ok=True)

        while True:
            # Receive metadata message
            metadata_text = await websocket.receive_text()
            metadata = json.loads(metadata_text)

            file_type = metadata.get('type')
            filename = metadata.get('filename')
            file_size = metadata.get('size')
            timestamp = metadata.get('timestamp', time.time())

            if not all([file_type, filename, file_size]):
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "message": "Missing required metadata fields"
                }))
                continue

            # Validate file type
            if file_type not in ['segment', 'playlist']:
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "message": "Invalid file type"
                }))
                continue

            # Determine file path
            if file_type == 'playlist':
                file_path = station_dir / "playlist.m3u8"
            else:
                file_path = station_dir / filename

            # Receive file data
            file_data = await websocket.receive_bytes()

            # Validate file size
            if len(file_data) != file_size:
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "message": f"File size mismatch. Expected {file_size}, got {len(file_data)}"
                }))
                continue

            # Save file
            with open(file_path, "wb") as f:
                f.write(file_data)

            logger.info(f"HLS {file_type} received via WebSocket: {filename} for station {station_id}")

            # Send confirmation
            await websocket.send_text(json.dumps({
                "status": "success",
                "file_type": file_type,
                "filename": filename,
                "station_id": station_id,
                "timestamp": timestamp,
                "size": len(file_data)
            }))

    except WebSocketDisconnect:
        logger.info(f"HLS WebSocket disconnected for station {station_id}")
    except Exception as e:
        logger.error(f"Error in HLS WebSocket for station {station_id}: {e}")
        try:
            await websocket.send_text(json.dumps({
                "status": "error",
                "message": str(e)
            }))
        except:
            pass
    finally:
        hls_manager.disconnect(websocket)

@app.get("/hls/{station_id}/playlist.m3u8")
async def serve_hls_playlist(station_id: str):
    """Serve HLS playlist for a specific station."""
    playlist_path = HLS_STORAGE_PATH / station_id / "playlist.m3u8"

    if not playlist_path.exists():
        raise HTTPException(status_code=404, detail="Playlist not found")

    return FileResponse(
        path=str(playlist_path),
        media_type="application/vnd.apple.mpegurl",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.get("/hls/{station_id}/{segment_name}")
async def serve_hls_segment(station_id: str, segment_name: str):
    """Serve HLS segment for a specific station."""
    # Validate segment filename (security)
    if not segment_name.endswith('.ts') or '..' in segment_name:
        raise HTTPException(status_code=400, detail="Invalid segment name")

    segment_path = HLS_STORAGE_PATH / station_id / segment_name

    if not segment_path.exists():
        raise HTTPException(status_code=404, detail="Segment not found")

    return FileResponse(
        path=str(segment_path),
        media_type="video/mp2t",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.get("/hls/{station_id}/status")
async def get_hls_status(station_id: str):
    """Get HLS streaming status for a station."""
    station_dir = HLS_STORAGE_PATH / station_id

    if not station_dir.exists():
        return {
            "station_id": station_id,
            "status": "not_found",
            "playlist_exists": False,
            "segment_count": 0
        }

    playlist_path = station_dir / "playlist.m3u8"
    playlist_exists = playlist_path.exists()

    # Count segments
    segment_count = len(list(station_dir.glob("*.ts")))

    return {
        "station_id": station_id,
        "status": "active" if playlist_exists else "inactive",
        "playlist_exists": playlist_exists,
        "segment_count": segment_count,
        "last_update": playlist_path.stat().st_mtime if playlist_exists else None
    }

if __name__ == "__main__":
    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    
    # Run the FastAPI application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
