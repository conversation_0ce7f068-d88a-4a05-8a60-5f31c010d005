#!/usr/bin/env python3
"""
Weather Service - Background weather data fetching and AWOS generation

Handles periodic weather data fetching, AWOS generation, and MP3 conversion.
"""

import asyncio
import os
import logging
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Dict
from pydub import AudioSegment

from weather_fetcher import <PERSON>Fetcher
from awos_generator import AWOSGenerator
from download_audio import download_all_components

logger = logging.getLogger(__name__)

class WeatherService:
    """Service for managing weather data fetching and AWOS generation."""
    
    def __init__(self):
        # Configuration from environment variables
        self.station_id = os.getenv("STATION_ID", "185807")
        self.token = os.getenv("TOKEN", "04839500-dc95-4fed-8dbf-d02d90a0dd7c")
        self.airport_advisory = os.getenv("AIRPORT_ADVISORY", "Ridge Landing Airpark automated advisory")
        self.tts_voice = os.getenv("TTS_VOICE", "en-US-JennyNeural")
        self.word_gap = float(os.getenv("WORD_GAP", "-0.8"))
        self.phrase_gap = float(os.getenv("PHRASE_GAP", "0.2"))
        
        # Update interval (2 minutes as requested)
        self.update_interval = int(os.getenv("UPDATE_INTERVAL_SECONDS", "120"))
        
        # File paths
        self.audio_dir = Path("audio_components")
        self.output_dir = Path("output")
        self.awos_wav_path = self.output_dir / "awos.wav"
        self.awos_mp3_path = self.output_dir / "awos.mp3"
        
        # Create directories
        self.audio_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.weather_fetcher = WeatherFetcher(self.station_id, self.token)
        self.awos_generator = AWOSGenerator(
            audio_dir=str(self.audio_dir),
            word_gap=self.word_gap,
            phrase_gap=self.phrase_gap
        )
        
        # State tracking
        self.current_weather_data: Optional[Dict] = None
        self.last_update_time: Optional[datetime] = None
        self.is_running = False
        self.audio_components_downloaded = False
        
    async def initialize(self):
        """Initialize the weather service - download audio components."""
        logger.info("Initializing weather service...")
        
        try:
            # Download all required audio components
            logger.info("Downloading audio components...")
            success = await download_all_components()

            if success:
                self.audio_components_downloaded = True
                logger.info("Successfully downloaded all audio components")
                
                # Warm the audio cache
                self.awos_generator.warm_cache()
            else:
                logger.error("Failed to download audio components")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize weather service: {e}")
            return False
            
        return True
    
    async def start_weather_updates(self):
        """Start the background weather update loop."""
        self.is_running = True
        
        # Initialize first
        if not await self.initialize():
            logger.error("Failed to initialize weather service")
            return
        
        logger.info(f"Starting weather updates every {self.update_interval} seconds")
        
        # Initial weather fetch
        await self.update_weather()
        
        # Start periodic updates
        while self.is_running:
            try:
                await asyncio.sleep(self.update_interval)
                if self.is_running:
                    await self.update_weather()
            except asyncio.CancelledError:
                logger.info("Weather update loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in weather update loop: {e}")
                # Continue running even if one update fails
                await asyncio.sleep(10)  # Short delay before retrying
    
    async def update_weather(self):
        """Fetch weather data and generate new AWOS MP3."""
        try:
            logger.info("Fetching weather data...")
            
            # Fetch weather data
            weather_data = self.weather_fetcher.get_awos_data()
            
            if not weather_data:
                logger.error("Failed to fetch weather data")
                return False
            
            logger.info(f"Weather data fetched: {weather_data}")
            
            # Store current weather data
            self.current_weather_data = weather_data
            self.last_update_time = datetime.now(timezone.utc)
            
            # Generate AWOS audio
            if not self.audio_components_downloaded:
                logger.error("Audio components not downloaded, cannot generate AWOS")
                return False
            
            logger.info("Generating AWOS audio...")
            
            # Generate WAV file
            result = self.awos_generator.generate(weather_data, str(self.awos_wav_path))
            
            if result:
                logger.info(f"AWOS WAV generated: {result}")
                
                # Convert WAV to MP3
                await self.convert_wav_to_mp3()
                
                logger.info("AWOS MP3 updated successfully")
                return True
            else:
                logger.error("Failed to generate AWOS audio")
                return False
                
        except Exception as e:
            logger.error(f"Error updating weather: {e}")
            return False
    
    async def convert_wav_to_mp3(self):
        """Convert the generated WAV file to MP3."""
        try:
            if not self.awos_wav_path.exists():
                logger.error("WAV file does not exist, cannot convert to MP3")
                return False
            
            # Load WAV file and convert to MP3
            audio = AudioSegment.from_wav(str(self.awos_wav_path))
            
            # Export as MP3 with good quality settings
            audio.export(
                str(self.awos_mp3_path),
                format="mp3",
                bitrate="128k",
                parameters=["-q:a", "2"]  # Good quality
            )
            
            logger.info(f"Converted WAV to MP3: {self.awos_mp3_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error converting WAV to MP3: {e}")
            return False
    
    async def stop(self):
        """Stop the weather service."""
        logger.info("Stopping weather service...")
        self.is_running = False
    
    def get_current_weather_data(self) -> Optional[Dict]:
        """Get the current weather data."""
        return self.current_weather_data
    
    def get_last_update_time(self) -> Optional[datetime]:
        """Get the last update time."""
        return self.last_update_time
    
    def awos_file_exists(self) -> bool:
        """Check if the AWOS MP3 file exists."""
        return self.awos_mp3_path.exists()
    
    def get_awos_mp3_path(self) -> Path:
        """Get the path to the AWOS MP3 file."""
        return self.awos_mp3_path
