#!/usr/bin/env python3
"""
Test script for VPS HLS endpoints.

This script tests the HLS upload and serving endpoints on the VPS.
"""

import asyncio
import json
import logging
import requests
import tempfile
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_health_endpoint():
    """Test the health endpoint."""
    logger.info("Testing health endpoint...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        
        if response.status_code == 200:
            logger.info("✓ Health endpoint is working")
            logger.info(f"Response: {response.json()}")
            return True
        else:
            logger.error(f"✗ Health endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Health endpoint error: {e}")
        return False


def test_http_upload():
    """Test HTTP file upload endpoint."""
    logger.info("Testing HTTP upload endpoint...")
    
    try:
        # Create test files
        test_segment = b"Test HLS segment content"
        test_playlist = b"#EXTM3U\n#EXT-X-VERSION:3\n"
        
        # Test segment upload
        files = {'file': ('test_segment.ts', test_segment, 'application/octet-stream')}
        data = {
            'file_type': 'segment',
            'station_id': 'test_station',
            'timestamp': time.time()
        }
        
        response = requests.post("http://localhost:8000/hls/upload", files=files, data=data, timeout=10)
        
        if response.status_code == 200:
            logger.info("✓ Segment upload successful")
            logger.info(f"Response: {response.json()}")
        else:
            logger.error(f"✗ Segment upload failed: {response.status_code} - {response.text}")
            return False
        
        # Test playlist upload
        files = {'file': ('playlist.m3u8', test_playlist, 'application/vnd.apple.mpegurl')}
        data = {
            'file_type': 'playlist',
            'station_id': 'test_station',
            'timestamp': time.time()
        }
        
        response = requests.post("http://localhost:8000/hls/upload", files=files, data=data, timeout=10)
        
        if response.status_code == 200:
            logger.info("✓ Playlist upload successful")
            logger.info(f"Response: {response.json()}")
            return True
        else:
            logger.error(f"✗ Playlist upload failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"✗ HTTP upload error: {e}")
        return False


def test_hls_serving():
    """Test HLS file serving endpoints."""
    logger.info("Testing HLS serving endpoints...")
    
    try:
        station_id = "test_station"
        
        # Test status endpoint
        response = requests.get(f"http://localhost:8000/hls/{station_id}/status", timeout=10)
        
        if response.status_code == 200:
            status = response.json()
            logger.info(f"✓ Status endpoint working: {status}")
            
            if status.get('playlist_exists'):
                # Test playlist serving
                response = requests.get(f"http://localhost:8000/hls/{station_id}/playlist.m3u8", timeout=10)
                
                if response.status_code == 200:
                    logger.info("✓ Playlist serving successful")
                    logger.info(f"Playlist content: {response.text[:100]}...")
                else:
                    logger.warning(f"Playlist serving failed: {response.status_code}")
            
            return True
        else:
            logger.error(f"✗ Status endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"✗ HLS serving error: {e}")
        return False


def test_storage_directory():
    """Test HLS storage directory."""
    logger.info("Testing HLS storage directory...")
    
    try:
        storage_path = Path("/app/hls_storage")
        
        if storage_path.exists():
            logger.info(f"✓ Storage directory exists: {storage_path}")
            
            # List contents
            contents = list(storage_path.iterdir())
            logger.info(f"Storage contents: {[str(p) for p in contents]}")
            
            return True
        else:
            logger.error(f"✗ Storage directory does not exist: {storage_path}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Storage directory error: {e}")
        return False


def test_websocket_endpoint():
    """Test WebSocket endpoint availability."""
    logger.info("Testing WebSocket endpoint...")
    
    try:
        import websockets
        
        async def test_ws():
            try:
                uri = "ws://localhost:8000/hls/ws/test_station"
                async with websockets.connect(uri, ping_interval=20, ping_timeout=10) as websocket:
                    logger.info("✓ WebSocket connection successful")
                    
                    # Send test metadata
                    metadata = {
                        'type': 'segment',
                        'filename': 'test.ts',
                        'size': 10,
                        'timestamp': time.time()
                    }
                    
                    await websocket.send(json.dumps(metadata))
                    await websocket.send(b"test_data_")
                    
                    # Wait for response
                    response_text = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response = json.loads(response_text)
                    
                    if response.get('status') == 'success':
                        logger.info("✓ WebSocket upload test successful")
                        return True
                    else:
                        logger.error(f"✗ WebSocket upload failed: {response}")
                        return False
                        
            except Exception as e:
                logger.error(f"✗ WebSocket test error: {e}")
                return False
        
        return asyncio.run(test_ws())
        
    except ImportError:
        logger.warning("WebSocket library not available, skipping WebSocket test")
        return True
    except Exception as e:
        logger.error(f"✗ WebSocket test error: {e}")
        return False


def main():
    """Main test function."""
    logger.info("Starting VPS HLS endpoint tests...")
    
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("Storage Directory", test_storage_directory),
        ("HTTP Upload", test_http_upload),
        ("HLS Serving", test_hls_serving),
        ("WebSocket Endpoint", test_websocket_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n=== {test_name} ===")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n=== Test Results Summary ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests PASSED!")
        return 0
    else:
        logger.error("❌ Some tests FAILED!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
