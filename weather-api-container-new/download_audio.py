"""
Audio Download Module for Container Startup

Downloads all required AWOS audio components using Edge TTS for offline use.
This is a container-optimized version of the original download_audio.py.
"""

import asyncio
import os
import sys
import logging
import edge_tts
from audio_components import ALL_COMPONENTS, get_audio_mapping

# Configure logging
logger = logging.getLogger(__name__)

# Configuration (with environment variable support)
VOICE = os.getenv("TTS_VOICE", "en-US-JennyNeural")  # Natural English voice
AUDIO_DIR = "audio_components"
RATE = os.getenv("TTS_RATE", "+0%")  # Normal speech rate
VOLUME = os.getenv("TTS_VOLUME", "+0%")  # Normal volume
DOWNLOAD_TIMEOUT = int(os.getenv("AUDIO_DOWNLOAD_TIMEOUT", "300"))  # 5 minutes default

async def download_single_audio(text, filename, voice=VOICE, max_retries=3):
    """
    Download a single audio component using Edge TTS and convert to proper WAV.

    Args:
        text (str): Text to convert to speech
        filename (str): Output filename
        voice (str): Voice to use for TTS
        max_retries (int): Maximum number of retry attempts

    Returns:
        bool: True if successful, False otherwise
    """
    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                logger.info(f"Retrying download of '{text}' (attempt {attempt + 1}/{max_retries + 1})")
                # Add exponential backoff delay
                await asyncio.sleep(2 ** attempt)

            # Create TTS communication
            communicate = edge_tts.Communicate(text, voice, rate=RATE, volume=VOLUME)

            # Save to temporary MP3 file first
            temp_filepath = os.path.join(AUDIO_DIR, filename.replace('.wav', '.tmp.mp3'))
            await communicate.save(temp_filepath)

            # Check if the temporary file was created and has content
            if not os.path.exists(temp_filepath) or os.path.getsize(temp_filepath) == 0:
                if os.path.exists(temp_filepath):
                    os.remove(temp_filepath)
                raise Exception("No audio was received or file is empty")

            # Convert MP3 to proper WAV using pydub
            from pydub import AudioSegment
            audio = AudioSegment.from_mp3(temp_filepath)

            # Export as proper WAV (16-bit, 44.1kHz for compatibility)
            final_filepath = os.path.join(AUDIO_DIR, filename)
            audio.export(final_filepath, format="wav", parameters=["-ar", "44100", "-ac", "1"])

            # Remove temporary file
            os.remove(temp_filepath)

            # Verify the final WAV file exists and has content
            if not os.path.exists(final_filepath) or os.path.getsize(final_filepath) == 0:
                logger.error(f"Generated WAV file is missing or empty: {filename}")
                if attempt < max_retries:
                    continue
                return False

            logger.debug(f"✓ Downloaded and converted: {text} -> {filename}")
            return True

        except Exception as e:
            logger.error(f"✗ Failed to download '{text}' (attempt {attempt + 1}): {e}")
            if attempt < max_retries:
                continue
            else:
                logger.error(f"✗ All {max_retries + 1} attempts failed for '{text}'")
                return False

    return False

async def download_all_components():
    """
    Download all AWOS audio components.
    
    Returns:
        bool: True if all downloads successful, False otherwise
    """
    # Create audio directory if it doesn't exist
    os.makedirs(AUDIO_DIR, exist_ok=True)
    
    # Get current components (may include custom airport advisory)
    from audio_components import ALL_COMPONENTS, AIRPORT_ADVISORY

    logger.info(f"Starting download of {len(ALL_COMPONENTS)} audio components...")
    logger.info(f"Using voice: {VOICE}")
    logger.info(f"Airport advisory: {AIRPORT_ADVISORY}")
    logger.info(f"Output directory: {AUDIO_DIR}")

    # Get audio mapping
    audio_mapping = get_audio_mapping()
    
    # Download all components
    tasks = []
    for text in ALL_COMPONENTS:
        filename = audio_mapping[text]
        task = download_single_audio(text, filename)
        tasks.append(task)
    
    # Execute all downloads concurrently with timeout
    try:
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=DOWNLOAD_TIMEOUT
        )
    except asyncio.TimeoutError:
        logger.error(f"Audio download timed out after {DOWNLOAD_TIMEOUT} seconds")
        return False
    
    # Count successes and failures
    successes = sum(1 for result in results if result is True)
    failures = len(results) - successes
    
    logger.info(f"Download complete! Successful: {successes}, Failed: {failures}")
    
    if failures > 0:
        logger.error(f"Some downloads failed ({failures} out of {len(results)}). Please check the error messages above.")
        # Allow system to continue if most downloads succeeded (at least 80%)
        success_rate = successes / len(results) if len(results) > 0 else 0
        if success_rate >= 0.8:
            logger.warning(f"Continuing with {successes}/{len(results)} audio components (success rate: {success_rate:.1%})")
            return True
        else:
            logger.error(f"Too many failures ({success_rate:.1%} success rate). Cannot continue.")
            return False
    else:
        logger.info("All audio components downloaded successfully!")
        return True

def verify_downloads():
    """
    Verify that all required audio files exist.

    Returns:
        bool: True if all files exist, False otherwise
    """
    if not os.path.exists(AUDIO_DIR):
        logger.error(f"Audio directory '{AUDIO_DIR}' does not exist.")
        return False

    # Get current audio mapping (may include custom airport advisory)
    audio_mapping = get_audio_mapping()
    missing_files = []

    for text, filename in audio_mapping.items():
        filepath = os.path.join(AUDIO_DIR, filename)
        if not os.path.exists(filepath):
            missing_files.append(filename)

    if missing_files:
        logger.error(f"Missing {len(missing_files)} audio files:")
        for filename in missing_files:
            logger.error(f"  - {filename}")
        return False
    else:
        logger.info(f"All {len(audio_mapping)} audio files are present!")
        return True

def ensure_audio_components(max_attempts=3):
    """
    Ensure all audio components are available, downloading if necessary.

    Args:
        max_attempts (int): Maximum number of download attempts

    Returns:
        bool: True if audio components are ready, False otherwise
    """
    logger.info("Checking audio components...")

    # First check if all files already exist
    if verify_downloads():
        logger.info("Audio components already available, skipping download")
        return True

    # Create audio directory if it doesn't exist
    os.makedirs(AUDIO_DIR, exist_ok=True)

    # Try downloading with retries
    for attempt in range(max_attempts):
        if attempt > 0:
            logger.info(f"Retrying audio component download (attempt {attempt + 1}/{max_attempts})")
            import time
            time.sleep(5)  # Wait 5 seconds between attempts

        logger.info("Audio components missing, downloading...")

        try:
            success = asyncio.run(download_all_components())
            if success:
                # Verify the downloads
                if verify_downloads():
                    logger.info("Audio components ready!")
                    return True
                else:
                    logger.error(f"Download verification failed (attempt {attempt + 1})")
            else:
                logger.error(f"Audio download failed (attempt {attempt + 1})")

            if attempt < max_attempts - 1:
                continue
            else:
                logger.error("All download attempts failed")
                return False

        except Exception as e:
            logger.error(f"Error downloading audio components (attempt {attempt + 1}): {e}")
            if attempt < max_attempts - 1:
                continue
            else:
                logger.error("All download attempts failed due to exceptions")
                return False

    return False

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    if len(sys.argv) > 1 and sys.argv[1] == "--verify":
        success = verify_downloads()
        sys.exit(0 if success else 1)
    else:
        # Download all components
        success = ensure_audio_components()
        sys.exit(0 if success else 1)
