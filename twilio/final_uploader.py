import os
import requests
from requests.auth import HTTP<PERSON>asi<PERSON><PERSON><PERSON>
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

# --- Configuration ---
# Replace with your credentials from Step 1 & 2
# ACCOUNT_SID = "ACcfea6a9f93db4ba331ccc94b1a00e825"  # Your Account SID
API_KEY_SID = "**********************************"  # Your API Key SID
API_SECRET = "orOg9EI6zwQv4QB2m2PMTnIlmNSYtXJP"     # Your API Secret
SERVICE_SID = "ZS51eebd00b1e30c5071d220f90807b51e"  # Your Service SID

# --- Script Parameters ---
STATION_ICAO = "4FL5"
FILENAME = "awos.wav"


def upload_public_asset(station_icao, local_filename):
    """
    Uploads a file as a new public Asset Version.
    This change must be manually deployed to go live.
    """
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), local_filename)
    if not os.path.exists(file_path):
        print(f"Error: File not found at '{file_path}'")
        return

    client = Client(API_KEY_SID, API_SECRET)
    asset_friendly_name = f"/{station_icao}/awos.wav"
    asset_sid = None

    try:
        print("Searching for existing Asset container...")
        assets = client.serverless.v1.services(SERVICE_SID).assets.list()
        for asset in assets:
            if asset.friendly_name == asset_friendly_name:
                asset_sid = asset.sid
                print(f"Found existing asset with SID: {asset_sid}")
                break

        if not asset_sid:
            print(f"No existing asset found. Creating a new one...")
            new_asset = client.serverless.v1.services(SERVICE_SID).assets.create(
                friendly_name=asset_friendly_name
            )
            asset_sid = new_asset.sid
            print(f"Successfully created new asset with SID: {asset_sid}")

        # --- Direct API Upload Logic ---
        print(f"Uploading file from {file_path} via direct API call...")

        upload_url = f"https://serverless-upload.twilio.com/v1/Services/{SERVICE_SID}/Assets/{asset_sid}/Versions"
        auth = HTTPBasicAuth(API_KEY_SID, API_SECRET)

        data_payload = {
            'Path': asset_friendly_name,
            'Visibility': 'public', # Asset is set to public
        }

        with open(file_path, 'rb') as f:
            files_payload = {
                'Content': (os.path.basename(file_path), f.read(), 'audio/wav')
            }
            response = requests.post(upload_url, auth=auth, data=data_payload, files=files_payload)

        if response.status_code == 201:
            response_json = response.json()
            print("\n--- Upload Success! ---")
            print(f"Successfully uploaded: {response_json.get('path')}")
            print("\nIMPORTANT: You must now manually deploy your service in the Twilio Console.")
            
        else:
            print(f"\n--- Upload Error: {response.status_code} ---")
            print(response.text)

    except Exception as e:
        print(f"\n--- A Script Error Occurred: {e} ---")

if __name__ == "__main__":
    upload_public_asset(STATION_ICAO, FILENAME)