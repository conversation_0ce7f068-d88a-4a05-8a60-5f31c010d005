# twilio_vanity_search.py
#
# A Python script to search for available "vanity" Twilio phone numbers
# based on a desired area code and a specific sequence of digits.

from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

# --- Configuration: Set your credentials and search criteria here ---
# Replace the placeholder values with your actual Twilio credentials and desired search terms.

ACCOUNT_SID = "**********************************"
AUTH_TOKEN = "f0cecb6927cf7ed2992a0175f1328071"
SEARCH_AREA_CODE = "863"  # The 3-digit area code you want to search in.
SEARCH_CONTAINS = "400****"   # The digits you want the number to contain.


def find_vanity_numbers():
    """
    Queries the Twilio API for available phone numbers based on the
    hardcoded configuration above.
    """
    print("--- Twilio Vanity Phone Number Search ---")

    # Validate that placeholders have been changed
    if "ACxxxxxxxx" in ACCOUNT_SID or "your_auth_token" in AUTH_TOKEN:
        print("ERROR: Please replace the placeholder ACCOUNT_SID and AUTH_TOKEN values in the script.")
        return

    # Initialize the Twilio client
    client = Client(ACCOUNT_SID, AUTH_TOKEN)

    print(f"\nSearching for available numbers in area code '{SEARCH_AREA_CODE}' containing '{SEARCH_CONTAINS}'...")
    print("-" * 50)

    try:
        # --- Make the API Request ---
        # This queries for available local US phone numbers that match
        # the specified area code and contain the desired digits.
        available_numbers = client.available_phone_numbers('US').local.list(
            area_code=SEARCH_AREA_CODE,
            contains=SEARCH_CONTAINS
        )

        if not available_numbers:
            print("No matching phone numbers found. Try a different search.")
        else:
            print("Found the following numbers:")
            # Loop through the results and print each number
            for number in available_numbers:
                print(f"  - {number.friendly_name}")

    except TwilioRestException as e:
        # Handle potential API errors, such as invalid credentials
        print(f"An error occurred with the Twilio API: {e}")
    except Exception as e:
        # Handle other potential errors (e.g., network issues)
        print(f"An unexpected error occurred: {e}")

    print("-" * 50)


# --- Run the main function ---
if __name__ == "__main__":
    # Before running, make sure you have the twilio library installed:
    # pip install twilio
    #
    # Also, ensure you have replaced the placeholder values for
    # ACCOUNT_SID, AUTH_TOKEN, SEARCH_AREA_CODE, and SEARCH_CONTAINS at the top of this script.
    find_vanity_numbers()