#!/usr/bin/env python3
"""
Script to fetch and list recordings from Cloudflare R2 storage for a given day and station.

Usage:
    python fetch_recordings.py 2025/07/23 4FL5_122900
    python fetch_recordings.py 2025-07-23 4FL5_122900
    python fetch_recordings.py 20250723 4FL5_122900

The script will list all recordings for the specified date and station with:
- File name
- Start time (parsed from filename)
- Duration (parsed from filename)
- File size
"""

import boto3
import sys
import re
from datetime import datetime
from typing import List, Dict, Optional


class RecordingsFetcher:
    """Fetches and lists recordings from Cloudflare R2 storage."""
    
    def __init__(self):
        """Initialize the S3 client with Cloudflare R2 credentials."""
        # S3 configuration from commands.txt
        self.access_key_id = "c53df2e89e72fe035e7db5a899c9b4de"
        self.secret_access_key = "e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6"
        self.endpoint_url = "https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com"
        self.bucket_name = "recordings"
        self.region_name = "auto"
        
        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=self.access_key_id,
            aws_secret_access_key=self.secret_access_key,
            endpoint_url=self.endpoint_url,
            region_name=self.region_name
        )
    
    def normalize_date(self, date_str: str) -> str:
        """
        Normalize various date formats to YYYYMMDD format.
        
        Args:
            date_str: Date in format YYYY/MM/DD, YYYY-MM-DD, or YYYYMMDD
            
        Returns:
            Date in YYYYMMDD format
        """
        # Remove any separators and ensure we have 8 digits
        clean_date = re.sub(r'[/-]', '', date_str)
        
        if len(clean_date) == 8 and clean_date.isdigit():
            return clean_date
        
        # Try to parse with common formats
        for fmt in ['%Y/%m/%d', '%Y-%m-%d', '%Y%m%d']:
            try:
                dt = datetime.strptime(date_str, fmt)
                return dt.strftime('%Y%m%d')
            except ValueError:
                continue
        
        raise ValueError(f"Invalid date format: {date_str}. Use YYYY/MM/DD, YYYY-MM-DD, or YYYYMMDD")
    
    def parse_filename(self, filename: str) -> Optional[Dict[str, any]]:
        """
        Parse recording filename to extract timestamp and duration.

        Expected format: YYYYMMDD_HHMMSS_mmm_dDDDDDD.webm (or .opus for backward compatibility)
        Where:
        - YYYYMMDD_HHMMSS_mmm is the timestamp with milliseconds
        - dDDDDDD is duration in milliseconds (up to 999 seconds)

        Args:
            filename: The recording filename

        Returns:
            Dict with parsed info or None if parsing fails
        """
        # Pattern for: YYYYMMDD_HHMMSS_mmm_dDDDDDD.webm or .opus (backward compatibility)
        pattern = r'^(\d{8})_(\d{6})_(\d{3})_d(\d{1,6})\.(webm|opus)$'
        match = re.match(pattern, filename)
        
        if not match:
            return None

        date_part, time_part, ms_part, duration_ms, file_ext = match.groups()
        
        try:
            # Parse timestamp
            start_time = datetime.strptime(f"{date_part}_{time_part}", '%Y%m%d_%H%M%S')
            start_time = start_time.replace(microsecond=int(ms_part) * 1000)
            
            # Parse duration (in milliseconds)
            duration_ms = int(duration_ms)
            duration_seconds = duration_ms / 1000.0
            
            return {
                'start_time': start_time,
                'duration_ms': duration_ms,
                'duration_seconds': duration_seconds
            }
        except (ValueError, TypeError):
            return None
    
    def fetch_recordings(self, date: str, station: str) -> List[Dict[str, any]]:
        """
        Fetch recordings for a specific date and station.

        Args:
            date: Date in YYYYMMDD format
            station: Station name (e.g., "4FL5_122900")

        Returns:
            List of recording info dictionaries
        """
        # Convert YYYYMMDD to YYYY/MM/DD format for the S3 path
        if len(date) == 8:
            formatted_date = f"{date[:4]}/{date[4:6]}/{date[6:8]}"
        else:
            formatted_date = date

        # Construct the prefix for the station folder and date
        prefix = f"{station}/{formatted_date}/"

        recordings = []

        try:
            # List objects with the specified prefix
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix
            )

            if 'Contents' not in response:
                return recordings  # Remove duplicate message, let print_recordings handle it
            
            for obj in response['Contents']:
                key = obj['Key']
                filename = key.split('/')[-1]  # Get just the filename
                
                # Parse the filename
                parsed = self.parse_filename(filename)
                if parsed:
                    recording_info = {
                        'filename': filename,
                        'key': key,
                        'start_time': parsed['start_time'],
                        'duration_ms': parsed['duration_ms'],
                        'duration_seconds': parsed['duration_seconds'],
                        'size_bytes': obj['Size'],
                        'last_modified': obj['LastModified']
                    }
                    recordings.append(recording_info)
                else:
                    print(f"Warning: Could not parse filename: {filename}")
            
            # Sort by start time
            recordings.sort(key=lambda x: x['start_time'])
            
        except Exception as e:
            print(f"Error fetching recordings: {e}")
            return []
        
        return recordings
    
    def format_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def print_recordings(self, recordings: List[Dict[str, any]], date: str, station: str):
        """Print recordings in a formatted table."""
        if not recordings:
            print(f"No recordings found for station {station} on {date}")
            return

        # Format date for display (YYYY-MM-DD)
        if len(date) == 8:
            display_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
        else:
            display_date = date

        print(f"\nRecordings for station {station} on {display_date}")
        print("=" * 80)
        print(f"{'Filename':<35} {'Start Time':<20} {'Duration':<10} {'Size':<10}")
        print("-" * 80)
        
        total_duration = 0
        total_size = 0
        
        for recording in recordings:
            start_time_str = recording['start_time'].strftime('%H:%M:%S.%f')[:-3]  # Include milliseconds
            duration_str = f"{recording['duration_seconds']:.1f}s"
            size_str = self.format_size(recording['size_bytes'])
            
            print(f"{recording['filename']:<35} {start_time_str:<20} {duration_str:<10} {size_str:<10}")
            
            total_duration += recording['duration_seconds']
            total_size += recording['size_bytes']
        
        print("-" * 80)
        print(f"Total: {len(recordings)} recordings, {total_duration:.1f}s duration, {self.format_size(total_size)} size")


def main():
    """Main function to handle command line arguments and run the fetcher."""
    if len(sys.argv) != 3:
        print("Usage: python fetch_recordings.py <date> <station>")
        print("Examples:")
        print("  python fetch_recordings.py 2025/07/23 4FL5_122900")
        print("  python fetch_recordings.py 2025-07-23 4FL5_122900")
        print("  python fetch_recordings.py 20250723 4FL5_122900")
        sys.exit(1)
    
    date_input = sys.argv[1]
    station = sys.argv[2]
    
    try:
        fetcher = RecordingsFetcher()
        normalized_date = fetcher.normalize_date(date_input)
        recordings = fetcher.fetch_recordings(normalized_date, station)
        fetcher.print_recordings(recordings, normalized_date, station)
        
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
