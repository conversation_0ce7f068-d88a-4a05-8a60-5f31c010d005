#!/usr/bin/env python3
"""
S3 Connection Test Script for Cloudflare R2

This script validates the S3 connection using the provided Cloudflare R2 credentials
before implementing the upload functionality in the main container code.
"""

import os
import sys
import tempfile
from datetime import datetime, timezone
from pathlib import Path

try:
    import boto3
    from botocore.exceptions import ClientError, NoCredentialsError
except ImportError:
    print("ERROR: boto3 not installed. Install with: pip install boto3")
    sys.exit(1)


def test_s3_connection():
    """Test S3 connection with Cloudflare R2 credentials."""
    
    # Cloudflare R2 credentials (replace with your actual values)
    access_key_id = "c53df2e89e72fe035e7db5a899c9b4de"
    secret_access_key = "e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6"
    endpoint_url = "https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com"
    bucket_name = "recordings"
    
    print("Testing Cloudflare R2 S3 Connection...")
    print(f"Endpoint: {endpoint_url}")
    print(f"Bucket: {bucket_name}")
    print("-" * 50)
    
    try:
        # Create S3 client
        s3_client = boto3.client(
            's3',
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            region_name='auto'  # Cloudflare R2 uses 'auto' region
        )
        
        print("✓ S3 client created successfully")
        
        # Test 1: Check bucket access (skip list_buckets if no permission)
        print("\nTest 1: Checking bucket access...")
        try:
            # Try to list objects in the bucket instead of listing all buckets
            response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
            print(f"✓ Successfully accessed bucket '{bucket_name}'")

        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchBucket':
                print(f"✗ Bucket '{bucket_name}' does not exist")
                return False
            elif e.response['Error']['Code'] == 'AccessDenied':
                print(f"✗ Access denied to bucket '{bucket_name}': {e}")
                return False
            else:
                print(f"✗ Failed to access bucket: {e}")
                return False
        
        # Test 2: Test folder structure creation
        print("\nTest 2: Testing folder structure...")
        station_name = "TEST_STATION"
        now = datetime.now(timezone.utc)
        test_key = f"{station_name}/{now.strftime('%Y/%m/%d')}/test_connection.txt"
        
        try:
            # Upload a test file
            test_content = f"S3 connection test at {now.isoformat()}"
            s3_client.put_object(
                Bucket=bucket_name,
                Key=test_key,
                Body=test_content.encode('utf-8'),
                ContentType='text/plain'
            )
            print(f"✓ Test file uploaded: {test_key}")
            
        except ClientError as e:
            print(f"✗ Failed to upload test file: {e}")
            return False
        
        # Test 3: Verify file exists
        print("\nTest 3: Verifying uploaded file...")
        try:
            response = s3_client.head_object(Bucket=bucket_name, Key=test_key)
            print(f"✓ Test file verified, size: {response['ContentLength']} bytes")
            
        except ClientError as e:
            print(f"✗ Failed to verify test file: {e}")
            return False
        
        # Test 4: Download and verify content
        print("\nTest 4: Downloading and verifying content...")
        try:
            response = s3_client.get_object(Bucket=bucket_name, Key=test_key)
            downloaded_content = response['Body'].read().decode('utf-8')
            
            if downloaded_content == test_content:
                print("✓ Downloaded content matches uploaded content")
            else:
                print("✗ Downloaded content does not match")
                return False
                
        except ClientError as e:
            print(f"✗ Failed to download test file: {e}")
            return False
        
        # Test 5: Clean up test file
        print("\nTest 5: Cleaning up test file...")
        try:
            s3_client.delete_object(Bucket=bucket_name, Key=test_key)
            print("✓ Test file deleted successfully")
            
        except ClientError as e:
            print(f"✗ Failed to delete test file: {e}")
            return False
        
        # Test 6: Test with actual .opus file structure
        print("\nTest 6: Testing .opus file upload simulation...")
        try:
            # Create a small test file to simulate .opus upload
            with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_file:
                temp_file.write(b"fake opus data for testing")
                temp_file_path = temp_file.name
            
            # Generate realistic filename
            timestamp = datetime.now(timezone.utc)
            filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_{timestamp.microsecond//1000:03d}_d005000.opus"
            opus_key = f"{station_name}/{timestamp.strftime('%Y/%m/%d')}/{filename}"
            
            # Upload the test .opus file
            with open(temp_file_path, 'rb') as f:
                s3_client.put_object(
                    Bucket=bucket_name,
                    Key=opus_key,
                    Body=f,
                    ContentType='audio/opus'
                )
            
            print(f"✓ Test .opus file uploaded: {opus_key}")
            
            # Clean up
            s3_client.delete_object(Bucket=bucket_name, Key=opus_key)
            os.unlink(temp_file_path)
            print("✓ Test .opus file cleaned up")
            
        except Exception as e:
            print(f"✗ Failed .opus file test: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! S3 connection is working correctly.")
        print("=" * 50)
        return True
        
    except NoCredentialsError:
        print("✗ No AWS credentials found")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_environment_variables():
    """Test using environment variables for credentials."""
    print("\nTesting with environment variables...")
    
    # Set environment variables
    os.environ['AWS_ACCESS_KEY_ID'] = "c53df2e89e72fe035e7db5a899c9b4de"
    os.environ['AWS_SECRET_ACCESS_KEY'] = "e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6"
    os.environ['AWS_ENDPOINT_URL'] = "https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com"
    
    try:
        # Create client using environment variables
        s3_client = boto3.client('s3', region_name='auto')
        
        # Simple test
        response = s3_client.list_buckets()
        print("✓ Environment variable authentication works")
        return True
        
    except Exception as e:
        print(f"✗ Environment variable test failed: {e}")
        return False


if __name__ == "__main__":
    print("Cloudflare R2 S3 Connection Test")
    print("=" * 50)
    
    success = test_s3_connection()
    
    if success:
        test_environment_variables()
        print("\nReady to implement S3 upload functionality!")
    else:
        print("\nPlease check your credentials and try again.")
        sys.exit(1)
