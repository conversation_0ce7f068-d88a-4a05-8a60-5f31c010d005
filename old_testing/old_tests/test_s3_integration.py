#!/usr/bin/env python3
"""
S3 Integration Test Script

This script tests the complete S3 upload integration with the audio recording system.
It validates that the S3UploadManager works correctly with the AudioRecorder and
that files are uploaded with the proper folder structure.
"""

import os
import sys
import time
import tempfile
import logging
from datetime import datetime, timezone
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from s3_upload_manager import S3UploadManager
    from audio_config import AudioConfig
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sound-player-container directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_test_webm_file(duration_seconds: float = 5.0) -> str:
    """Create a test .webm file for upload testing."""
    # Generate timestamp-based filename
    now = datetime.now(timezone.utc)
    duration_ms = int(duration_seconds * 1000)
    filename = f"{now.strftime('%Y%m%d_%H%M%S')}_{now.microsecond//1000:03d}_d{duration_ms:06d}.webm"
    
    # Create temporary file with fake opus data
    temp_dir = Path(tempfile.gettempdir()) / "s3_test"
    temp_dir.mkdir(exist_ok=True)
    
    test_file = temp_dir / filename
    with open(test_file, 'wb') as f:
        # Write some fake WebM data
        f.write(b"\x1A\x45\xDF\xA3")  # WebM container header (EBML)
        f.write(b"\x00" * 100)  # Fake WebM data
        f.write(f"Test WebM file created at {now.isoformat()}".encode('utf-8'))
    
    logger.info(f"Created test file: {test_file}")
    return str(test_file)


def test_s3_upload_manager():
    """Test the S3UploadManager functionality."""
    print("Testing S3UploadManager...")
    print("-" * 50)
    
    # Test configuration
    config = {
        'access_key_id': "c53df2e89e72fe035e7db5a899c9b4de",
        'secret_access_key': "e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6",
        'endpoint_url': "https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com",
        'bucket_name': "recordings",
        'station_name': "TEST_STATION",
        'station_timezone': "America/New_York",
        'max_concurrent_uploads': 2
    }
    
    try:
        # Initialize S3 upload manager
        s3_manager = S3UploadManager(**config)
        logger.info("✓ S3UploadManager initialized successfully")
        
        # Create test files
        test_files = []
        for i in range(3):
            test_file = create_test_webm_file(duration_seconds=2.0 + i)
            test_files.append(test_file)
        
        # Queue uploads
        logger.info("Queueing test files for upload...")
        for test_file in test_files:
            s3_manager.queue_upload(test_file)
        
        # Wait for uploads to complete
        logger.info("Waiting for uploads to complete...")
        start_time = time.time()
        timeout = 60.0  # 1 minute timeout
        
        while time.time() - start_time < timeout:
            stats = s3_manager.get_stats()
            logger.info(f"Upload stats: {stats}")
            
            if stats['current_queue_size'] == 0 and stats['active_uploads'] == 0:
                logger.info("✓ All uploads completed")
                break
            
            time.sleep(2)
        else:
            logger.warning("Timeout waiting for uploads to complete")
        
        # Final stats
        final_stats = s3_manager.get_stats()
        logger.info(f"Final upload stats: {final_stats}")
        
        # Cleanup
        s3_manager.shutdown(timeout=10.0)
        
        # Clean up test files
        for test_file in test_files:
            try:
                os.unlink(test_file)
            except:
                pass
        
        print("✓ S3UploadManager test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"S3UploadManager test failed: {e}")
        return False


def test_audio_config_integration():
    """Test that AudioConfig properly loads S3 settings."""
    print("\nTesting AudioConfig S3 integration...")
    print("-" * 50)
    
    # Set test environment variables
    test_env = {
        'ENABLE_S3_UPLOAD': 'true',
        'S3_ACCESS_KEY_ID': 'test_key',
        'S3_SECRET_ACCESS_KEY': 'test_secret',
        'S3_ENDPOINT_URL': 'https://test.endpoint.com',
        'S3_BUCKET_NAME': 'test-bucket',
        'STATION_NAME': 'TEST_STATION',
        'S3_MAX_RETRY_DELAY': '600',
        'S3_MAX_CONCURRENT_UPLOADS': '5'
    }
    
    # Backup original environment
    original_env = {}
    for key in test_env:
        original_env[key] = os.environ.get(key)
        os.environ[key] = test_env[key]
    
    try:
        # Reload AudioConfig (this is a bit hacky but works for testing)
        import importlib
        import audio_config
        importlib.reload(audio_config)
        
        # Test configuration values
        assert audio_config.AudioConfig.ENABLE_S3_UPLOAD == True
        assert audio_config.AudioConfig.S3_ACCESS_KEY_ID == 'test_key'
        assert audio_config.AudioConfig.S3_SECRET_ACCESS_KEY == 'test_secret'
        assert audio_config.AudioConfig.S3_ENDPOINT_URL == 'https://test.endpoint.com'
        assert audio_config.AudioConfig.S3_BUCKET_NAME == 'test-bucket'
        assert audio_config.AudioConfig.STATION_NAME == 'TEST_STATION'
        assert audio_config.AudioConfig.S3_MAX_RETRY_DELAY == 600.0
        assert audio_config.AudioConfig.S3_MAX_CONCURRENT_UPLOADS == 5
        
        logger.info("✓ AudioConfig S3 settings loaded correctly")
        print("✓ AudioConfig S3 integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"AudioConfig test failed: {e}")
        return False
        
    finally:
        # Restore original environment
        for key, value in original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value
        
        # Reload AudioConfig to restore original settings
        importlib.reload(audio_config)


def test_folder_structure():
    """Test that S3 folder structure is generated correctly."""
    print("\nTesting S3 folder structure generation...")
    print("-" * 50)
    
    try:
        # Create S3 manager
        s3_manager = S3UploadManager(
            access_key_id="test",
            secret_access_key="test",
            endpoint_url="https://test.com",
            bucket_name="test",
            station_name="RIDGE_LANDING",
            station_timezone="America/New_York"
        )
        
        # Test file path
        test_file = Path("/tmp/20250723_123045_123_d005000.opus")
        
        # Test with specific timestamp (July 23, 2025, 12:30:45 UTC)
        test_timestamp = datetime(2025, 7, 23, 12, 30, 45, tzinfo=timezone.utc).timestamp()
        
        # Generate S3 key
        s3_key = s3_manager._generate_s3_key(test_file, test_timestamp)
        
        # Expected: RIDGE_LANDING/2025/07/23/20250723_123045_123_d005000.opus
        # (converted to Eastern Time: 08:30:45)
        expected_pattern = "RIDGE_LANDING/2025/07/23/20250723_123045_123_d005000.opus"
        
        logger.info(f"Generated S3 key: {s3_key}")
        logger.info(f"Expected pattern: {expected_pattern}")
        
        # Check structure: STATION_NAME/YYYY/MM/DD/filename.opus (5 parts)
        parts = s3_key.split('/')
        assert len(parts) == 5, f"Expected 5 parts, got {len(parts)}"
        assert parts[0] == "RIDGE_LANDING", f"Station name mismatch: {parts[0]}"
        assert parts[1] == "2025", f"Year mismatch: {parts[1]}"
        assert parts[2] == "07", f"Month mismatch: {parts[2]}"
        assert parts[3] == "23", f"Day mismatch: {parts[3]}"
        assert parts[4].endswith(".opus"), f"File extension mismatch: {parts[4]}"
        
        logger.info("✓ S3 folder structure generated correctly")
        print("✓ S3 folder structure test passed")
        return True
        
    except Exception as e:
        logger.error(f"Folder structure test failed: {e}")
        return False


def main():
    """Run all S3 integration tests."""
    print("S3 Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_audio_config_integration,
        test_folder_structure,
        test_s3_upload_manager
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All S3 integration tests passed!")
        return 0
    else:
        print("❌ Some tests failed. Check the logs above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
