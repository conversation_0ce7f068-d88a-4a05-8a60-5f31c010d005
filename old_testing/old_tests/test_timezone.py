#!/usr/bin/env python3
"""
Test script to verify timezone functionality for recording filenames.
"""

import os
import sys
import time
import tempfile
from datetime import datetime, timezone
import zoneinfo

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from audio_config import AudioConfig
from audio_recorder import AudioRecorder

def test_timezone_filename_generation():
    """Test filename generation with different timezones."""
    print("Testing timezone-aware filename generation...")
    
    # Test timestamp (fixed for consistent testing)
    test_timestamp = 1642694400.123  # 2022-01-20 12:00:00.123 UTC
    test_duration = 2.456  # 2.456 seconds
    
    # Test different timezones
    timezones = [
        'UTC',
        'America/New_York',
        'America/Los_Angeles', 
        'America/Chicago',
        'America/Denver'
    ]
    
    print(f"Test timestamp: {test_timestamp} (UTC: {datetime.fromtimestamp(test_timestamp, tz=timezone.utc)})")
    print(f"Test duration: {test_duration} seconds")
    print()
    
    for tz_name in timezones:
        # Set the timezone environment variable
        os.environ['STATION_TZ'] = tz_name
        
        # Reload the config to pick up the new timezone
        import importlib
        import audio_config
        importlib.reload(audio_config)
        
        # Create a recorder instance
        recorder = AudioRecorder()
        
        # Generate filename
        filename = recorder._generate_filename(test_timestamp, test_duration)
        
        # Show the timezone conversion
        if tz_name == 'UTC':
            tz = timezone.utc
        else:
            tz = zoneinfo.ZoneInfo(tz_name)
        
        local_time = datetime.fromtimestamp(test_timestamp, tz=tz)
        
        print(f"Timezone: {tz_name}")
        print(f"  Local time: {local_time}")
        print(f"  Filename: {filename}")
        print()
    
    # Test invalid timezone
    print("Testing invalid timezone fallback...")
    os.environ['STATION_TZ'] = 'Invalid/Timezone'
    importlib.reload(audio_config)
    recorder = AudioRecorder()
    filename = recorder._generate_filename(test_timestamp, test_duration)
    print(f"Invalid timezone fallback filename: {filename}")
    print()
    
    return True

def test_filename_format():
    """Test that filename format is correct."""
    print("Testing filename format...")
    
    # Set a known timezone
    os.environ['STATION_TZ'] = 'America/New_York'
    
    # Reload config
    import importlib
    import audio_config
    importlib.reload(audio_config)
    
    recorder = AudioRecorder()
    
    # Test with current time
    current_time = time.time()
    duration = 5.123
    
    filename = recorder._generate_filename(current_time, duration)
    
    print(f"Generated filename: {filename}")
    
    # Check format: YYYYMMDD_HHMMSS_mmm_dDDDDDD.opus
    parts = filename.split('_')
    if len(parts) == 4 and parts[3].endswith('.opus'):
        date_part = parts[0]  # YYYYMMDD
        time_part = parts[1]  # HHMMSS
        ms_part = parts[2]    # mmm
        duration_part = parts[3].replace('.opus', '')  # dDDDDDD
        
        print(f"  Date: {date_part} (format: YYYYMMDD)")
        print(f"  Time: {time_part} (format: HHMMSS)")
        print(f"  Milliseconds: {ms_part} (format: mmm)")
        print(f"  Duration: {duration_part} (format: dDDDDDD)")
        
        # Validate format
        if (len(date_part) == 8 and date_part.isdigit() and
            len(time_part) == 6 and time_part.isdigit() and
            len(ms_part) == 3 and ms_part.isdigit() and
            duration_part.startswith('d') and len(duration_part) == 7 and
            duration_part[1:].isdigit()):
            print("✓ Filename format is correct!")
            return True
        else:
            print("✗ Filename format is incorrect!")
            return False
    else:
        print("✗ Filename structure is incorrect!")
        return False

if __name__ == "__main__":
    try:
        print("=== Timezone Functionality Test ===\n")
        
        success1 = test_timezone_filename_generation()
        success2 = test_filename_format()
        
        if success1 and success2:
            print("\n✓ All timezone tests PASSED!")
        else:
            print("\n✗ Some timezone tests FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n✗ Test ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
