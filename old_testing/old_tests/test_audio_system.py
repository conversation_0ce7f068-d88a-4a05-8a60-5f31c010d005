#!/usr/bin/env python3
"""
Test script for the new audio event system

This script tests the AudioEventManager, ClickDetector, and AudioRecorder
to ensure they work correctly together.
"""

import logging
import time
import tempfile
import threading
from pathlib import Path
import numpy as np

from audio_event_manager import AudioEventManager, AudioEvent
from signal_detector import <PERSON>lick<PERSON>atternDetector
from audio_recorder import AudioRecorder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockAudioDevice:
    """Mock audio device for testing without real hardware."""
    
    def __init__(self, sample_rate=48000):
        self.sample_rate = sample_rate
        self.is_active = False
    
    def start(self):
        self.is_active = True
    
    def stop(self):
        self.is_active = False
    
    def close(self):
        self.is_active = False


class TestAudioEventSystem:
    """Test suite for the audio event system."""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp())
        self.click_count = 0
        self.awos_triggered = False
        self.weather_prep_started = False
        self.radio_check_triggered = False

    def cleanup(self):
        """Clean up test resources."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def awos_callback(self):
        """Mock AWOS callback."""
        self.awos_triggered = True
        logger.info("AWOS callback triggered!")

    def weather_prep_callback(self):
        """Mock weather preparation callback."""
        self.weather_prep_started = True
        logger.info("Weather preparation started!")

    def radio_check_callback(self):
        """Mock radio check callback."""
        self.radio_check_triggered = True
        logger.info("Radio check callback triggered!")
    
    def test_audio_event_manager(self):
        """Test basic AudioEventManager functionality."""
        logger.info("Testing AudioEventManager...")
        
        # Create manager
        manager = AudioEventManager()
        
        # Test subscriber management
        class TestSubscriber:
            def __init__(self):
                self.events_received = 0
                self.started = False
                self.stopped = False
            
            def on_audio_chunk(self, event):
                self.events_received += 1
            
            def on_start(self):
                self.started = True
            
            def on_stop(self):
                self.stopped = True
        
        subscriber = TestSubscriber()
        manager.add_subscriber(subscriber)
        
        assert manager.get_subscriber_count() == 1
        logger.info("✓ Subscriber management works")
        
        # Test event creation
        chunk = np.random.randint(-1000, 1000, 1024, dtype=np.int16)
        event = AudioEvent(
            chunk=chunk,
            timestamp=time.time(),
            sample_rate=48000,
            channels=1,
            power_in_range=1000.0
        )
        
        assert event.chunk.shape == (1024,)
        assert event.sample_rate == 48000
        logger.info("✓ AudioEvent creation works")
        
        return True
    
    def test_signal_detector(self):
        """Test ClickPatternDetector functionality."""
        logger.info("Testing ClickPatternDetector...")

        # Create click pattern detector
        detector = ClickPatternDetector(
            awos_callback=self.awos_callback,
            weather_prep_callback=self.weather_prep_callback,
            radio_check_callback=self.radio_check_callback,
            thigh=50000,
            tlow=10000,
            click_min_duration=0.1,
            click_max_duration=0.6,
            click_max_gap=1.0,
            click_cooldown=0.5,  # Shorter for testing
            awos_click_count=3,
            radio_check_click_count=4
        )

        # Test initialization
        assert detector.get_click_count() == 0
        assert not detector.is_in_signal()
        assert not detector.is_cooldown_active()
        logger.info("✓ ClickPatternDetector initialization works")
        
        # Simulate click detection
        detector.on_start()
        
        # Simulate high power (signal start)
        event_high = AudioEvent(
            chunk=np.random.randint(-1000, 1000, 1024, dtype=np.int16),
            timestamp=time.time(),
            sample_rate=48000,
            channels=1,
            power_in_range=60000  # Above threshold
        )
        
        detector.on_audio_chunk(event_high)
        assert detector.is_in_signal()
        logger.info("✓ Signal detection works")
        
        # Simulate signal end after valid duration
        time.sleep(0.2)  # Valid click duration
        event_low = AudioEvent(
            chunk=np.random.randint(-100, 100, 1024, dtype=np.int16),
            timestamp=time.time(),
            sample_rate=48000,
            channels=1,
            power_in_range=5000  # Below threshold
        )
        
        detector.on_audio_chunk(event_low)
        assert not detector.is_in_signal()
        assert detector.get_click_count() == 1
        logger.info("✓ Click counting works")
        
        detector.on_stop()
        return True
    
    def test_audio_recorder(self):
        """Test AudioRecorder functionality."""
        logger.info("Testing AudioRecorder...")
        
        # Create recorder with test directory
        recorder = AudioRecorder(
            storage_path=str(self.temp_dir),
            signal_threshold_high=50000,
            signal_threshold_low=10000,
            pre_roll_seconds=0.1,
            post_roll_seconds=0.2,
            min_segment_duration=0.05
        )
        
        # Test initialization
        stats = recorder.get_recording_stats()
        assert not stats['is_recording']
        assert stats['storage_path'] == str(self.temp_dir)
        logger.info("✓ AudioRecorder initialization works")
        
        recorder.on_start()

        # Simulate audio chunks with signal
        start_time = time.time()

        # Add chunks with signal (above threshold)
        for i in range(5):
            time.sleep(0.01)
            event = AudioEvent(
                chunk=np.random.randint(-500, 500, 1024, dtype=np.int16),
                timestamp=time.time(),
                sample_rate=48000,
                channels=1,
                power_in_range=15000  # Above threshold
            )
            recorder.on_audio_chunk(event)

            if i == 0:
                # Check that recording started
                stats = recorder.get_recording_stats()
                assert stats['is_recording']
                logger.info("✓ Recording start works")

        # Add post-roll chunks (below threshold)
        for _ in range(5):
            time.sleep(0.05)
            event = AudioEvent(
                chunk=np.random.randint(-100, 100, 1024, dtype=np.int16),
                timestamp=time.time(),
                sample_rate=48000,
                channels=1,
                power_in_range=5000  # Below threshold
            )
            recorder.on_audio_chunk(event)
        
        # Check if recording was saved
        time.sleep(0.1)  # Allow processing time
        opus_files = list(self.temp_dir.glob("*.opus"))
        wav_files = list(self.temp_dir.glob("*.wav"))
        
        if opus_files or wav_files:
            logger.info("✓ Audio recording and saving works")
        else:
            logger.warning("⚠ No audio files created (may need ffmpeg for opus)")
        
        recorder.on_stop()
        return True
    
    def test_integration(self):
        """Test integration of all components."""
        logger.info("Testing system integration...")

        # Create manager and subscribers
        manager = AudioEventManager(sample_rate=48000, channels=1, chunk_size=1024)

        recorder = AudioRecorder(
            storage_path=str(self.temp_dir / "integration_test"),
            signal_threshold_high=50000,
            signal_threshold_low=10000
        )

        from signal_detector import ClickPatternDetector
        click_pattern_detector = ClickPatternDetector(
            awos_callback=self.awos_callback,
            weather_prep_callback=self.weather_prep_callback,
            radio_check_callback=self.radio_check_callback,
            click_cooldown=0.3,  # Shorter for testing
            awos_click_count=3,
            radio_check_click_count=4
        )

        # Register subscribers
        manager.add_subscriber(click_pattern_detector)
        manager.add_subscriber(recorder)

        assert manager.get_subscriber_count() == 2
        logger.info("✓ Multiple subscribers registered")

        # Simulate events
        click_pattern_detector.on_start()
        recorder.on_start()

        # Simulate 3 clicks
        for _ in range(3):
            # High signal
            event_high = AudioEvent(
                chunk=np.random.randint(-1000, 1000, 1024, dtype=np.int16),
                timestamp=time.time(),
                sample_rate=48000,
                channels=1,
                power_in_range=60000
            )

            click_pattern_detector.on_audio_chunk(event_high)
            recorder.on_audio_chunk(event_high)

            time.sleep(0.2)  # Valid click duration

            # Low signal
            event_low = AudioEvent(
                chunk=np.random.randint(-100, 100, 1024, dtype=np.int16),
                timestamp=time.time(),
                sample_rate=48000,
                channels=1,
                power_in_range=5000
            )

            click_pattern_detector.on_audio_chunk(event_low)
            recorder.on_audio_chunk(event_low)

            time.sleep(0.1)  # Gap between clicks

        # Wait for cooldown
        time.sleep(0.5)

        assert click_pattern_detector.get_click_count() == 3
        assert self.weather_prep_started
        logger.info("✓ Integration test completed")

        click_pattern_detector.on_stop()
        recorder.on_stop()
        return True
    
    def run_all_tests(self):
        """Run all tests."""
        logger.info("Starting audio event system tests...")
        
        try:
            self.test_audio_event_manager()
            self.test_signal_detector()
            self.test_audio_recorder()
            self.test_integration()
            
            logger.info("✅ All tests passed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False
        finally:
            self.cleanup()


if __name__ == "__main__":
    test_suite = TestAudioEventSystem()
    success = test_suite.run_all_tests()
    exit(0 if success else 1)
