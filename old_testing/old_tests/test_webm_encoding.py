#!/usr/bin/env python3
"""
WebM Encoding Test Script

This script validates that the new WebM encoding with Opus audio codec works correctly
and achieves the desired file size compression for voice recordings.
"""

import numpy as np
import tempfile
from pathlib import Path
from pydub import AudioSegment
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_test_audio(duration_seconds=5.0, sample_rate=48000):
    """Generate test audio data simulating voice recording."""
    # Generate a mix of tones that simulate voice frequencies
    t = np.linspace(0, duration_seconds, int(sample_rate * duration_seconds), False)
    
    # Voice-like frequency mix (200-3500 Hz range)
    audio = (
        0.3 * np.sin(2 * np.pi * 300 * t) +  # Fundamental frequency
        0.2 * np.sin(2 * np.pi * 600 * t) +  # First harmonic
        0.1 * np.sin(2 * np.pi * 1200 * t) + # Second harmonic
        0.05 * np.sin(2 * np.pi * 2400 * t)  # Third harmonic
    )
    
    # Add some noise to simulate real voice
    noise = np.random.normal(0, 0.02, audio.shape)
    audio = audio + noise
    
    # Normalize and convert to int16
    audio = np.clip(audio, -1.0, 1.0)
    audio_int16 = (audio * 32767).astype(np.int16)
    
    return audio_int16


def test_webm_encoding():
    """Test WebM encoding with our optimized parameters."""
    logger.info("Testing WebM encoding with Opus audio codec...")
    
    # Generate test audio
    audio_data = generate_test_audio(duration_seconds=3.0, sample_rate=48000)
    sample_rate = 48000
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
        temp_path = Path(temp_file.name)
    
    try:
        # Create AudioSegment
        audio_segment = AudioSegment(
            audio_data.tobytes(),
            frame_rate=sample_rate,
            sample_width=2,  # 16-bit
            channels=1       # Mono
        )
        
        logger.info(f"Original audio: {len(audio_segment)}ms duration, {sample_rate}Hz sample rate")
        
        # Export as WebM with our optimized parameters
        audio_segment.export(
            str(temp_path),
            format="webm",
            codec="libopus",
            parameters=[
                "-c:a", "libopus",      # Use Opus audio codec
                "-b:a", "16k",          # Very low bitrate (16 kbps) - suitable for voice
                "-vbr", "on",           # Variable bitrate for better compression
                "-compression_level", "10",  # Maximum compression (0-10, 10 is highest)
                "-application", "voip", # Optimize for voice (voip mode)
                "-frame_duration", "60", # Longer frame duration for better compression
                "-cutoff", "8000",      # Low-pass filter at 8kHz (sufficient for voice)
                "-ac", "1",             # Force mono
                "-vn"                   # No video stream (audio-only WebM)
            ]
        )
        
        # Check file size and calculate metrics
        file_size = temp_path.stat().st_size
        duration = len(audio_segment) / 1000.0  # Duration in seconds
        bitrate = (file_size * 8) / duration / 1000 if duration > 0 else 0  # kbps
        
        logger.info(f"✓ WebM file created successfully!")
        logger.info(f"  File size: {file_size:,} bytes")
        logger.info(f"  Duration: {duration:.2f} seconds")
        logger.info(f"  Effective bitrate: {bitrate:.1f} kbps")
        logger.info(f"  Compression ratio: {len(audio_data) * 2 / file_size:.1f}:1")
        
        # Test if file can be read back
        try:
            test_segment = AudioSegment.from_file(str(temp_path))
            logger.info(f"✓ WebM file can be read back successfully!")
            logger.info(f"  Read duration: {len(test_segment)}ms")
            logger.info(f"  Read sample rate: {test_segment.frame_rate}Hz")
            logger.info(f"  Read channels: {test_segment.channels}")
        except Exception as e:
            logger.error(f"✗ Failed to read back WebM file: {e}")
            return False
        
        # Validate file size is reasonable for voice
        expected_max_size = int(duration * 16000 / 8 * 1.2)  # 16kbps + 20% overhead
        if file_size <= expected_max_size:
            logger.info(f"✓ File size is within expected range (≤{expected_max_size:,} bytes)")
        else:
            logger.warning(f"⚠ File size is larger than expected (>{expected_max_size:,} bytes)")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ WebM encoding failed: {e}")
        return False
        
    finally:
        # Clean up
        if temp_path.exists():
            temp_path.unlink()


def test_compatibility():
    """Test WebM compatibility with different audio parameters."""
    logger.info("\nTesting WebM compatibility with different parameters...")
    
    test_cases = [
        {
            "name": "16kHz Mono (Voice optimized)",
            "sample_rate": 16000,
            "duration": 2.0,
            "parameters": [
                "-c:a", "libopus", "-b:a", "16k", "-vbr", "on",
                "-compression_level", "10", "-application", "voip",
                "-frame_duration", "60", "-cutoff", "8000", "-ac", "1", "-vn"
            ]
        },
        {
            "name": "48kHz Mono (High quality)",
            "sample_rate": 48000,
            "duration": 2.0,
            "parameters": [
                "-c:a", "libopus", "-b:a", "32k", "-vbr", "on",
                "-compression_level", "10", "-application", "voip",
                "-ac", "1", "-vn"
            ]
        },
        {
            "name": "Ultra-compressed",
            "sample_rate": 16000,
            "duration": 2.0,
            "parameters": [
                "-c:a", "libopus", "-b:a", "8k", "-vbr", "on",
                "-compression_level", "10", "-application", "voip",
                "-frame_duration", "60", "-cutoff", "4000", "-ac", "1", "-vn"
            ]
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        logger.info(f"\nTesting: {test_case['name']}")
        
        # Generate test audio
        audio_data = generate_test_audio(
            duration_seconds=test_case['duration'],
            sample_rate=test_case['sample_rate']
        )
        
        with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
            temp_path = Path(temp_file.name)
        
        try:
            # Create AudioSegment
            audio_segment = AudioSegment(
                audio_data.tobytes(),
                frame_rate=test_case['sample_rate'],
                sample_width=2,
                channels=1
            )
            
            # Export with test parameters
            audio_segment.export(
                str(temp_path),
                format="webm",
                codec="libopus",
                parameters=test_case['parameters']
            )
            
            # Get metrics
            file_size = temp_path.stat().st_size
            duration = len(audio_segment) / 1000.0
            bitrate = (file_size * 8) / duration / 1000 if duration > 0 else 0
            
            results.append({
                'name': test_case['name'],
                'file_size': file_size,
                'duration': duration,
                'bitrate': bitrate,
                'success': True
            })
            
            logger.info(f"  ✓ Success: {file_size:,} bytes, {bitrate:.1f} kbps")
            
        except Exception as e:
            logger.error(f"  ✗ Failed: {e}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'error': str(e)
            })
            
        finally:
            if temp_path.exists():
                temp_path.unlink()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("COMPATIBILITY TEST SUMMARY")
    logger.info("="*60)
    
    successful_tests = [r for r in results if r.get('success', False)]
    
    if successful_tests:
        logger.info(f"✓ {len(successful_tests)}/{len(results)} tests passed")
        
        # Find the most efficient encoding
        best_compression = min(successful_tests, key=lambda x: x['file_size'])
        logger.info(f"Most efficient: {best_compression['name']} "
                   f"({best_compression['file_size']:,} bytes, {best_compression['bitrate']:.1f} kbps)")
    else:
        logger.error("✗ All tests failed!")
    
    return len(successful_tests) == len(results)


def main():
    """Main test function."""
    logger.info("Starting WebM encoding validation tests...")
    logger.info("="*60)
    
    # Test basic WebM encoding
    basic_test_passed = test_webm_encoding()
    
    # Test compatibility with different parameters
    compatibility_test_passed = test_compatibility()
    
    # Final summary
    logger.info("\n" + "="*60)
    logger.info("FINAL TEST RESULTS")
    logger.info("="*60)
    
    if basic_test_passed and compatibility_test_passed:
        logger.info("✓ All tests passed! WebM encoding is working correctly.")
        logger.info("✓ Ready for production deployment.")
        return True
    else:
        logger.error("✗ Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
