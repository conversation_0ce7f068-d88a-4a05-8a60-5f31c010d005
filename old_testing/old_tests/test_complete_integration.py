#!/usr/bin/env python3
"""
Complete Integration Test

This script tests the complete integration of the audio recording system
with S3 upload functionality by simulating the full workflow.
"""

import os
import sys
import time
import tempfile
import logging
from datetime import datetime, timezone
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from s3_upload_manager import S3UploadManager
    from audio_recorder import AudioRecorder
    from audio_config import AudioConfig
    import numpy as np
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sound-player-container directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockAudioEvent:
    """Mock audio event for testing."""
    def __init__(self, chunk, timestamp, sample_rate=48000, channels=1, power_in_range=1000):
        self.chunk = chunk
        self.timestamp = timestamp
        self.sample_rate = sample_rate
        self.channels = channels
        self.power_in_range = power_in_range


def test_complete_integration():
    """Test the complete integration of AudioRecorder with S3UploadManager."""
    print("Testing Complete Integration: AudioRecorder + S3UploadManager")
    print("-" * 60)
    
    # Create temporary directory for recordings
    temp_dir = Path(tempfile.mkdtemp(prefix="awos_test_"))
    logger.info(f"Using temporary directory: {temp_dir}")
    
    try:
        # Initialize S3 upload manager
        s3_manager = S3UploadManager(
            access_key_id="c53df2e89e72fe035e7db5a899c9b4de",
            secret_access_key="e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6",
            endpoint_url="https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com",
            bucket_name="recordings",
            station_name="INTEGRATION_TEST",
            station_timezone="America/New_York",
            max_concurrent_uploads=1
        )
        logger.info("✓ S3UploadManager initialized")
        
        # Initialize AudioRecorder with S3 integration
        audio_recorder = AudioRecorder(
            storage_path=str(temp_dir),
            signal_threshold_high=1000,
            signal_threshold_low=500,
            pre_roll_seconds=0.1,
            post_roll_seconds=0.1,
            min_segment_duration=0.1,
            max_segment_duration=5.0,
            merge_gap_threshold=1.0,
            s3_upload_manager=s3_manager
        )
        logger.info("✓ AudioRecorder initialized with S3 integration")
        
        # Start the recorder
        audio_recorder.on_start()
        logger.info("✓ AudioRecorder started")
        
        # Simulate audio recording workflow
        logger.info("Simulating audio recording workflow...")
        
        # Generate some fake audio data
        sample_rate = 48000
        duration = 2.0  # 2 seconds
        samples = int(sample_rate * duration)
        
        # Create audio chunks (simulate real-time audio)
        chunk_size = 1024
        num_chunks = samples // chunk_size
        
        for i in range(num_chunks):
            # Generate fake audio data (sine wave)
            t = np.linspace(i * chunk_size / sample_rate, 
                          (i + 1) * chunk_size / sample_rate, 
                          chunk_size, endpoint=False)
            
            # Create a signal that exceeds the threshold for recording
            if i < num_chunks // 4 or i > 3 * num_chunks // 4:
                # Low signal (below threshold)
                audio_chunk = np.random.normal(0, 100, chunk_size).astype(np.int16)
                power = 100
            else:
                # High signal (above threshold) - this should trigger recording
                audio_chunk = (np.sin(2 * np.pi * 440 * t) * 5000 + 
                              np.random.normal(0, 100, chunk_size)).astype(np.int16)
                power = 5000
            
            # Create mock audio event
            event = MockAudioEvent(
                chunk=audio_chunk,
                timestamp=time.time(),
                sample_rate=sample_rate,
                power_in_range=power
            )
            
            # Process the audio chunk
            audio_recorder.on_audio_chunk(event)
            
            # Small delay to simulate real-time processing
            time.sleep(0.01)
        
        # Wait a bit for recording to finalize
        time.sleep(1.0)
        
        # Stop the recorder
        audio_recorder.on_stop()
        logger.info("✓ AudioRecorder stopped")
        
        # Check if files were created
        opus_files = list(temp_dir.glob("*.opus"))
        logger.info(f"Created {len(opus_files)} .opus files")
        
        if opus_files:
            for opus_file in opus_files:
                logger.info(f"  - {opus_file.name} ({opus_file.stat().st_size} bytes)")
            
            # Wait for S3 uploads to complete
            logger.info("Waiting for S3 uploads to complete...")
            start_time = time.time()
            timeout = 30.0
            
            while time.time() - start_time < timeout:
                stats = s3_manager.get_stats()
                logger.info(f"S3 upload stats: {stats}")
                
                if stats['current_queue_size'] == 0 and stats['active_uploads'] == 0:
                    logger.info("✓ All S3 uploads completed")
                    break
                
                time.sleep(2)
            else:
                logger.warning("Timeout waiting for S3 uploads")
            
            # Final stats
            final_stats = s3_manager.get_stats()
            logger.info(f"Final S3 stats: {final_stats}")
            
            if final_stats['total_uploaded'] > 0:
                logger.info("✓ Files successfully uploaded to S3")
                print("✓ Complete integration test PASSED")
                return True
            else:
                logger.error("✗ No files were uploaded to S3")
                print("✗ Complete integration test FAILED")
                return False
        else:
            logger.error("✗ No .opus files were created")
            print("✗ Complete integration test FAILED")
            return False
            
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        print("✗ Complete integration test FAILED")
        return False
        
    finally:
        # Cleanup
        try:
            s3_manager.shutdown(timeout=10.0)
        except:
            pass
        
        # Clean up temporary files
        try:
            import shutil
            shutil.rmtree(temp_dir)
            logger.info(f"Cleaned up temporary directory: {temp_dir}")
        except:
            pass


def main():
    """Run the complete integration test."""
    print("AWOS System - Complete Integration Test")
    print("=" * 60)
    
    success = test_complete_integration()
    
    if success:
        print("\n🎉 Integration test completed successfully!")
        print("The audio recording system is ready for deployment with S3 uploads.")
        return 0
    else:
        print("\n❌ Integration test failed.")
        print("Please check the logs and fix any issues before deployment.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
