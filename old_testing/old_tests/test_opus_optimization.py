#!/usr/bin/env python3
"""
WebM Optimization Test Script

This script tests the .webm file optimization with Opus audio codec and compares file sizes
between different encoding settings to demonstrate storage cost savings.
"""

import os
import sys
import tempfile
import logging
from pathlib import Path
import numpy as np
from pydub import AudioSegment

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_test_audio(duration_seconds=10, sample_rate=48000):
    """Generate test audio that simulates voice recording."""
    # Generate a mix of tones that simulate voice frequencies
    t = np.linspace(0, duration_seconds, int(sample_rate * duration_seconds), endpoint=False)
    
    # Voice-like signal: mix of fundamental frequency and harmonics
    fundamental = 150  # Hz - typical male voice fundamental
    audio = (
        0.5 * np.sin(2 * np.pi * fundamental * t) +      # Fundamental
        0.3 * np.sin(2 * np.pi * fundamental * 2 * t) +  # 2nd harmonic
        0.2 * np.sin(2 * np.pi * fundamental * 3 * t) +  # 3rd harmonic
        0.1 * np.sin(2 * np.pi * fundamental * 4 * t)    # 4th harmonic
    )
    
    # Add some noise to simulate real voice
    noise = np.random.normal(0, 0.05, len(audio))
    audio += noise
    
    # Add some silence periods to simulate pauses in speech
    silence_mask = np.sin(2 * np.pi * 0.5 * t) < -0.3  # Create silence periods
    audio[silence_mask] *= 0.1
    
    # Normalize and convert to int16
    audio = np.clip(audio, -1, 1)
    audio_int16 = (audio * 32767).astype(np.int16)
    
    return audio_int16


def test_webm_encoding(audio_data, sample_rate, test_name, **export_params):
    """Test WebM encoding with Opus audio codec and specific parameters and return file size."""
    with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
        temp_path = Path(temp_file.name)
    
    try:
        # Create AudioSegment
        audio_segment = AudioSegment(
            audio_data.tobytes(),
            frame_rate=sample_rate,
            sample_width=2,
            channels=1
        )
        
        # Export with specified parameters as WebM with Opus audio
        audio_segment.export(str(temp_path), format="webm", codec="libopus", **export_params)
        
        # Get file size
        file_size = temp_path.stat().st_size
        duration = len(audio_segment) / 1000.0
        bitrate = (file_size * 8) / duration / 1000 if duration > 0 else 0
        
        logger.info(f"{test_name}: {file_size:,} bytes, {duration:.1f}s, {bitrate:.1f} kbps")
        
        return file_size, bitrate
        
    finally:
        # Clean up
        if temp_path.exists():
            temp_path.unlink()


def main():
    """Run opus optimization tests."""
    print("Opus File Size Optimization Test")
    print("=" * 50)
    
    # Generate test audio (10 seconds of voice-like audio)
    duration = 10  # seconds
    sample_rate_high = 48000
    sample_rate_low = 16000
    
    print(f"Generating {duration}s of test audio...")
    audio_high = generate_test_audio(duration, sample_rate_high)
    audio_low = generate_test_audio(duration, sample_rate_low)
    
    print("\nTesting different WebM/Opus encoding settings:")
    print("-" * 50)

    # Test 1: Default high quality (what we had before)
    size1, br1 = test_webm_encoding(
        audio_high, sample_rate_high,
        "1. Default High Quality (48kHz)",
        parameters=["-c:a", "libopus", "-vn"]
    )

    # Test 2: Medium quality
    size2, br2 = test_webm_encoding(
        audio_high, sample_rate_high,
        "2. Medium Quality (48kHz)",
        parameters=["-c:a", "libopus", "-b:a", "64k", "-vbr", "on", "-vn"]
    )

    # Test 3: Low quality but still 48kHz
    size3, br3 = test_webm_encoding(
        audio_high, sample_rate_high,
        "3. Low Quality (48kHz)",
        parameters=["-c:a", "libopus", "-b:a", "32k", "-vbr", "on", "-application", "voip", "-vn"]
    )

    # Test 4: Our optimized settings with downsampling
    size4, br4 = test_webm_encoding(
        audio_low, sample_rate_low,
        "4. Optimized Voice (16kHz)",
        parameters=[
            "-c:a", "libopus",
            "-b:a", "16k",
            "-vbr", "on",
            "-compression_level", "10",
            "-application", "voip",
            "-frame_duration", "60",
            "-cutoff", "8000",
            "-vn"
        ]
    )

    # Test 5: Ultra-compressed for comparison
    size5, br5 = test_webm_encoding(
        audio_low, sample_rate_low,
        "5. Ultra-Compressed (16kHz)",
        parameters=[
            "-c:a", "libopus",
            "-b:a", "8k",
            "-vbr", "on",
            "-compression_level", "10",
            "-application", "voip",
            "-frame_duration", "60",
            "-cutoff", "4000",
            "-vn"
        ]
    )
    
    print("\nFile Size Comparison:")
    print("-" * 50)
    print(f"Default High Quality:    {size1:,} bytes (baseline)")
    print(f"Medium Quality:          {size2:,} bytes ({size2/size1*100:.1f}% of baseline)")
    print(f"Low Quality:             {size3:,} bytes ({size3/size1*100:.1f}% of baseline)")
    print(f"Optimized Voice:         {size4:,} bytes ({size4/size1*100:.1f}% of baseline) ⭐")
    print(f"Ultra-Compressed:        {size5:,} bytes ({size5/size1*100:.1f}% of baseline)")
    
    print(f"\nStorage Cost Savings with Optimized Voice:")
    print(f"- File size reduction: {(1-size4/size1)*100:.1f}%")
    print(f"- Storage cost reduction: {(1-size4/size1)*100:.1f}%")
    
    # Calculate annual storage costs for different scenarios
    print(f"\nAnnual Storage Cost Estimates (Cloudflare R2):")
    print("-" * 50)
    
    # Assume 100 recordings per day, average 10 seconds each
    recordings_per_day = 100
    days_per_year = 365
    total_recordings = recordings_per_day * days_per_year
    
    # R2 storage cost: $0.015 per GB per month
    r2_cost_per_gb_month = 0.015
    months_per_year = 12
    
    for name, size in [
        ("Default High Quality", size1),
        ("Optimized Voice", size4),
        ("Ultra-Compressed", size5)
    ]:
        annual_storage_gb = (size * total_recordings) / (1024**3)
        annual_cost = annual_storage_gb * r2_cost_per_gb_month * months_per_year
        print(f"{name:20}: {annual_storage_gb:.2f} GB/year = ${annual_cost:.2f}/year")
    
    savings = ((size1 - size4) * total_recordings) / (1024**3) * r2_cost_per_gb_month * months_per_year
    print(f"\nAnnual savings with optimization: ${savings:.2f}")
    
    print(f"\nRecommendation: Use 'Optimized Voice' settings")
    print(f"- Excellent voice quality for aviation communications")
    print(f"- {(1-size4/size1)*100:.1f}% smaller files")
    print(f"- Significant storage cost savings")


if __name__ == "__main__":
    main()
