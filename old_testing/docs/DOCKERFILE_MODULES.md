# Dockerfile Module Checklist

This document lists all the Python modules and files included in the Docker container.

## ✅ Core System Files

- `weather_sound_player.py` - Main application entry point
- `weather_fetcher.py` - Weather data fetching from API
- `awos_generator.py` - AWOS audio generation
- `audio_components.py` - Audio component definitions
- `audio_device_manager.py` - Audio device detection and management
- `download_audio.py` - Audio component downloading
- `startup.sh` - Container startup script

## ✅ New Audio Event System Files

- `audio_event_manager.py` - Central audio event hub (publisher-subscriber)
- `signal_detector.py` - Signal detection and 3-click pattern recognition
- `audio_recorder.py` - Audio recording to .opus files
- `audio_config.py` - Centralized configuration management

## ✅ Dependencies (requirements.txt)

- `requests>=2.31.0` - HTTP requests for weather API
- `pydub>=0.25.1` - Audio processing and .opus encoding
- `numpy` - Numerical operations for audio analysis
- `scipy` - Signal processing (welch function)
- `sounddevice` - Audio input/output
- `edge-tts>=6.1.0` - Text-to-speech generation

## ✅ System Dependencies (apt packages)

- `alsa-utils` - ALSA audio utilities
- `sox` - Audio processing tools
- `ffmpeg` - Audio/video processing (for .opus encoding)
- `libportaudio2` - PortAudio library
- `libportaudiocpp0` - PortAudio C++ bindings
- `portaudio19-dev` - PortAudio development headers

## ✅ Runtime Directories

- `/app/audio_components` - Downloaded TTS audio files
- `/app/recordings` - Recorded audio segments (volume mount point)

## ✅ Permissions

- User: `soundplayer` (non-root)
- Group: `audio` (for audio device access)
- Recordings directory: `755` permissions

## ✅ Import Verification

All modules can be imported successfully:
```python
from weather_sound_player import WeatherSoundPlayer
from weather_fetcher import WeatherFetcher
from awos_generator import AWOSGenerator
from audio_components import *
from audio_device_manager import AudioDeviceManager
from download_audio import ensure_audio_components
from audio_event_manager import AudioEventManager, AudioEvent, AudioEventSubscriber
from signal_detector import ClickPatternDetector
from audio_recorder import AudioRecorder
from audio_config import AudioConfig
```

## ✅ Container Features

- **Audio Processing**: Full audio input/output support
- **Signal Detection**: 3-click AWOS pattern recognition
- **Audio Recording**: Automatic recording of detected audio segments
- **Configuration**: Environment variable-based configuration
- **Security**: Non-root user execution
- **Persistence**: Volume mount for recordings
- **Logging**: Comprehensive startup and runtime logging
