# WebM Migration Guide

This document provides everything an AI agent needs to know to update the API and webapp for the migration from .opus to .webm audio format.

## Overview

The AWOS system has been migrated from `.opus` to `.webm` format to improve compatibility with Apple devices (Safari, iOS) while maintaining excellent compression for voice recordings.

### Key Benefits
- **Apple Device Compatibility**: WebM format works natively in Safari and iOS browsers
- **Maintained File Size**: Similar or better compression compared to .opus files
- **Voice Optimization**: Optimized encoding parameters for voice recordings
- **Backward Compatibility**: System supports both .webm (new) and .opus (legacy) files

## File Format Changes

### New Format: WebM with Opus Audio
- **Container**: WebM (audio-only, no video stream)
- **Audio Codec**: Opus (same as before, but in WebM container)
- **File Extension**: `.webm`
- **MIME Type**: `video/webm`

### Encoding Parameters
```bash
# Optimized WebM encoding for voice recordings
-c:a libopus          # Use Opus audio codec
-b:a 16k              # 16 kbps bitrate (suitable for voice)
-vbr on               # Variable bitrate
-compression_level 10 # Maximum compression
-application voip     # Voice optimization
-frame_duration 60    # Longer frames for better compression
-cutoff 8000          # 8kHz low-pass filter
-ac 1                 # Force mono
-vn                   # No video stream (audio-only)
```

### File Size Comparison
| Duration | .opus (old) | .webm (new) | Savings |
|----------|-------------|-------------|---------|
| 3.0s     | ~20.7 KB    | ~4.6 KB     | 78%     |
| 8.4s     | ~11.8 KB    | ~12.9 KB    | Similar |
| 4.8s     | ~7.1 KB     | ~7.4 KB     | Similar |

## API Changes Required

### 1. Content-Type Headers
**Before:**
```javascript
'Content-Type': 'audio/opus'
```

**After:**
```javascript
// Dynamic content type based on file extension
const contentType = filename.endsWith('.webm') ? 'video/webm' : 'audio/opus';
headers['Content-Type'] = contentType;
```

### 2. File Extension Handling
**Before:**
```javascript
const pattern = /^(\d{8})_(\d{6})_(\d{3})_d(\d{1,6})\.opus$/;
```

**After:**
```javascript
// Support both .webm and .opus for backward compatibility
const pattern = /^(\d{8})_(\d{6})_(\d{3})_d(\d{1,6})\.(webm|opus)$/;
```

### 3. Response Format Updates
Add `format` field to recording objects:
```json
{
  "filename": "20250723_031946_192_d002950.webm",
  "start_time": "2025-07-23T03:19:46.192Z",
  "duration_ms": 2950,
  "duration_seconds": 2.95,
  "size_bytes": 4636,
  "format": "webm",
  "download_url": "/api/recordings/4FL5_122900/20250723/20250723_031946_192_d002950.webm"
}
```

## Webapp Changes Required

### 1. Audio Player Updates
**Before:**
```html
<audio controls>
  <source src="/api/recordings/station/date/file.opus" type="audio/opus">
  Your browser does not support the audio element.
</audio>
```

**After:**
```html
<audio controls>
  <source src="/api/recordings/station/date/file.webm" type="video/webm">
  <source src="/api/recordings/station/date/file.opus" type="audio/opus">
  Your browser does not support the audio element.
</audio>
```

### 2. JavaScript Audio Handling
**Before:**
```javascript
const audio = new Audio();
audio.src = recordingUrl;
audio.type = 'audio/opus';
```

**After:**
```javascript
const audio = new Audio();
audio.src = recordingUrl;
// WebM files work with HTML5 audio element
// No special handling needed - browser auto-detects format
```

### 3. Download Links
**Before:**
```javascript
const downloadLink = `${baseUrl}/${filename}`;
// Assumes .opus extension
```

**After:**
```javascript
const downloadLink = `${baseUrl}/${filename}`;
// Works with both .webm and .opus extensions
// Set appropriate download filename
const downloadElement = document.createElement('a');
downloadElement.download = filename;
downloadElement.href = downloadLink;
```

## Database/Storage Changes

### 1. File Extension Updates
If storing file extensions in database:
```sql
-- Update file extension column to support both formats
ALTER TABLE recordings MODIFY COLUMN file_extension VARCHAR(10);

-- Update existing records (if needed)
UPDATE recordings SET file_extension = 'webm' WHERE file_extension = 'opus' AND created_date > '2025-07-24';
```

### 2. MIME Type Storage
```sql
-- Add MIME type column if storing content types
ALTER TABLE recordings ADD COLUMN mime_type VARCHAR(50);

-- Set MIME types based on file extension
UPDATE recordings SET mime_type = 'video/webm' WHERE file_extension = 'webm';
UPDATE recordings SET mime_type = 'audio/opus' WHERE file_extension = 'opus';
```

## Testing Checklist

### 1. API Testing
- [ ] Verify .webm files return `Content-Type: video/webm`
- [ ] Verify .opus files return `Content-Type: audio/opus`
- [ ] Test file listing includes both .webm and .opus files
- [ ] Test download functionality for both formats
- [ ] Verify file size calculations are correct

### 2. Webapp Testing
- [ ] Test audio playback in Safari (macOS/iOS)
- [ ] Test audio playback in Chrome
- [ ] Test audio playback in Firefox
- [ ] Test download functionality
- [ ] Verify UI displays correct file information

### 3. Mobile Testing
- [ ] Test on iPhone Safari
- [ ] Test on iPad Safari
- [ ] Test on Android Chrome
- [ ] Verify audio controls work properly

## Browser Compatibility

| Browser | .webm Support | .opus Support | Recommendation |
|---------|---------------|---------------|----------------|
| Safari (macOS) | ✅ Yes | ❌ No | Use .webm |
| Safari (iOS) | ✅ Yes | ❌ No | Use .webm |
| Chrome | ✅ Yes | ✅ Yes | Use .webm |
| Firefox | ✅ Yes | ✅ Yes | Use .webm |
| Edge | ✅ Yes | ✅ Yes | Use .webm |

## Migration Timeline

### Phase 1: Backend Updates (Immediate)
1. Update recording system to generate .webm files
2. Update S3 upload to use correct MIME types
3. Update API to handle both formats

### Phase 2: API Updates (Same deployment)
1. Update file listing endpoints
2. Update download endpoints
3. Add format field to responses

### Phase 3: Frontend Updates (Next deployment)
1. Update audio players to support .webm
2. Update download functionality
3. Test across all target browsers

### Phase 4: Cleanup (Future)
1. Monitor .opus file usage
2. Consider migration of legacy .opus files to .webm
3. Remove .opus support when no longer needed

## Rollback Plan

If issues arise, the system can be rolled back by:

1. **Reverting recording format**: Change `format="webm"` back to `format="opus"` in audio_recorder.py
2. **API compatibility**: The API already supports both formats
3. **Frontend compatibility**: HTML5 audio elements work with both formats

## Performance Impact

### Storage Savings
- **New recordings**: ~50-80% smaller file sizes
- **Bandwidth**: Reduced download times
- **S3 costs**: Lower storage and transfer costs

### Processing Impact
- **Encoding**: Similar CPU usage (same Opus codec)
- **Container overhead**: Minimal WebM container overhead
- **Compatibility**: Better browser support reduces client-side issues

## Support and Troubleshooting

### Common Issues
1. **Safari not playing audio**: Ensure `Content-Type: video/webm` is set correctly
2. **Large file sizes**: Verify encoding parameters are applied correctly
3. **Download issues**: Check file extension handling in download logic

### Debugging
```bash
# Test WebM file validity
ffprobe recording.webm

# Check encoding parameters
ffprobe -show_streams recording.webm

# Test browser compatibility
curl -I https://your-api.com/recordings/station/date/file.webm
```

This migration provides significant improvements in Apple device compatibility while maintaining excellent compression and audio quality for voice recordings.
