# AWOS System Docker Container

An automated weather observation system (AWOS) for Raspberry Pi that provides real-time weather reports through audio announcements. The system listens for 3-click sequences to trigger weather reports using WeatherFlow API data and text-to-speech generation.

## Features

- **Click Detection**: Listens for 3-click sequences to trigger weather reports
- **Real-time Weather**: Fetches current weather data from WeatherFlow API
- **Audio Generation**: Converts weather data to natural-sounding AWOS announcements
- **Auto Audio Detection**: Automatically detects and configures USB audio devices
- **Configurable**: Extensive environment variable configuration
- **Containerized**: Runs reliably in Docker with proper audio device access

## Quick Start

### Basic Deployment
```bash
docker run -d --name awos-system --device /dev/snd --privileged --restart unless-stopped \
  -e AIRPORT_ADVISORY="Riverside Municipal Airport automated weather station" \
  -e TTS_VOICE="en-US-EricNeural" \
  ghcr.io/devtomsuys/awos-system:latest
```

### Full Configuration
```bash
docker run -d --name awos-system --device /dev/snd --privileged --restart unless-stopped \
  -e AIRPORT_ADVISORY="Ridge landing airpark automated weather advisory." \
  -e TTS_VOICE="en-US-EricNeural" \
  -e VOLUME=80 \
  -e STATION_ID=185807 \
  -e TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c \
  -e WORD_GAP=-0.9 \
  -e PHRASE_GAP=0.3 \
  ghcr.io/devtomsuys/awos-system:latest
```

## Configuration

### Environment Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `AIRPORT_ADVISORY` | Airport identification text | "Ridge Landing Airpark automated advisory" | "Riverside Municipal Airport automated weather station" |
| `TTS_VOICE` | Text-to-speech voice | "en-US-JennyNeural" | "en-US-EricNeural" |
| `VOLUME` | Audio volume (0-100) | 80 | 60 |
| `STATION_ID` | WeatherFlow station ID | 185807 | 185807 |
| `TOKEN` | WeatherFlow API token | (default provided) | your-api-token |
| `WORD_GAP` | Gap between words in seconds | -0.6 | -0.8 |
| `PHRASE_GAP` | Gap between phrases in seconds | 0.2 | 0.5 |

### Audio Configuration

The system automatically detects and configures:
- **Input Device**: USB microphone for click detection
- **Output Device**: USB speakers/headphones for audio playback
- **Format Conversion**: Handles different audio formats automatically

## Hardware Requirements

### Raspberry Pi 5
- USB audio input device (microphone)
- USB audio output device (speakers/headphones)
- Internet connection for weather data
- Docker installed

### Audio Devices
- USB audio devices are automatically prioritized
- HDMI audio is deprioritized to avoid issues
- Fallback to default system audio if needed

## How It Works

1. **Startup**: Container downloads audio components and initializes audio devices
2. **Listening**: System continuously listens for audio input through microphone
3. **Click Detection**: Detects 3 consecutive clicks (0.1-0.6s each, max 1s gap)
4. **Weather Fetch**: Immediately starts fetching fresh weather data after 3rd click
5. **Quiet Period**: Waits for 1 second of silence after clicks
6. **Playback**: Plays generated AWOS report through speakers

## Building and Deployment

### Building Locally
```bash
# Clone repository and navigate to container directory
cd sound-player-container

# Build the image
docker build -t awos-system .

# Run locally
docker run -d --name awos-system --device /dev/snd --privileged \
  -e AIRPORT_ADVISORY="Your Airport Name" \
  awos-system
```

### Using Docker Compose
```bash
# Use the provided docker-compose.yml
docker-compose up -d

# View logs
docker-compose logs -f
```

## Management

### Container Operations
```bash
# View logs
docker logs -f awos-system

# Stop/Start
docker stop awos-system
docker start awos-system

# Remove
docker stop awos-system && docker rm awos-system
```

### Monitoring
```bash
# Check audio device detection
docker exec awos-system aplay -l

# Test audio output
docker exec awos-system speaker-test -c 2 -t wav -l 1
```

## Troubleshooting

### No Audio Output
1. Verify USB audio device is connected: `lsusb | grep -i audio`
2. Check container has audio access: `docker logs awos-system`
3. Test audio manually: `speaker-test -c 2 -t wav`

### Click Detection Issues
1. Check microphone input: `arecord -l`
2. Verify microphone sensitivity and positioning
3. Monitor logs for click detection: `docker logs -f awos-system`

### Weather Data Issues
1. Verify internet connectivity
2. Check WeatherFlow API token validity
3. Confirm station ID is correct

### Audio Quality Issues
- Adjust `WORD_GAP` for word spacing (-1.0 to 0.0)
- Adjust `PHRASE_GAP` for phrase spacing (0.0 to 1.0)
- Change `TTS_VOICE` for different voice characteristics

## Files Structure

- `Dockerfile` - Container definition with audio support
- `weather_sound_player.py` - Main application with click detection
- `weather_fetcher.py` - WeatherFlow API integration
- `awos_generator.py` - Audio generation from weather data
- `audio_components.py` - Audio component definitions
- `download_audio.py` - TTS audio component downloader
- `startup.sh` - Container startup script
- `requirements.txt` - Python dependencies
- `docker-compose.yml` - Docker Compose configuration
