****************************************



  # Pull the latest version of the image
docker pull ghcr.io/devtomsuys/sound-player:latest

# Optional: Stop and remove the existing container
docker stop sound-player
docker rm sound-player

# Run the new container with the updated image

  docker run -d --name sound-player \
  --restart unless-stopped \
  --device /dev/snd \
  -e VOLUME=80 \
  -e LOOP_PAUSE=0.5 \
  ghcr.io/devtomsuys/sound-player:latest

docker pull ghcr.io/devtomsuys/click-detector:latest

docker run -d --name click-detector \
  --restart unless-stopped \
  --device /dev/snd \
  --privileged \
  ghcr.io/devtomsuys/click-detector:latest

docker run -d --name click-detector  \
  --restart unless-stopped \
  --device /dev/snd \
  ghcr.io/devtomsuys/click-detector:latest


  


  tskey-auth-kFTJrpz95711CNTRL-FKZJwQhFgLeoGSgJzpUKLe4DasXRBRgWL

  docker login ghcr.io -u devtomsuys

  API TOKEN: 04839500-dc95-4fed-8dbf-d02d90a0dd7c
  STATION ID: 185807

  docker run -d --name weather-player \
  --restart unless-stopped \
  --device /dev/snd \
  -e VOLUME=80 \
  -e WEATHER_STATION_ID=185807 \
  -e WEATHER_TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c \
  --privileged \
  --network host \
  ghcr.io/devtomsuys/sound-player:latest


  

SSH recording guide:


arecord -D hw:0,0 -f S16_LE -r 48000 -c 1 apopkus_test_x04.wav
