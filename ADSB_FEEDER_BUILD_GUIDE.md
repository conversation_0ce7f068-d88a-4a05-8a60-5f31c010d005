# Building adsb-feeder-image for Raspberry Pi on macOS

This guide documents the **proven Docker-based method** for building custom adsb-feeder images for Raspberry Pi using macOS.

## Overview

The adsb-feeder-image project creates custom Raspberry Pi images with:
- **Automatic WiFi Hotspot**: Creates `adsb.im-feeder` hotspot when WiFi connection fails
- **Web Configuration Interface**: Complete web UI for setup and monitoring
- **Docker-based Architecture**: All ADS-B services run in containers
- **Multi-board Support**: Raspberry Pi 2/3/4/5, Orange Pi, and other ARM boards
- **20+ ADS-B Aggregators**: Feeds data to multiple flight tracking services

## Prerequisites

### System Requirements
- **macOS** (tested on macOS Sequoia)
- **Docker Desktop** (must be running)
- **50+ GB free disk space** (for build process and images)
- **Stable internet connection** (downloads ~400MB base image)

### Required Tools Installation

```bash
# Install Docker Desktop (if not already installed)
brew install --cask docker

# Start Docker Desktop and ensure it's running
open -a "Docker Desktop"

# Verify Docker is running
docker ps
```

## Project Setup

### 1. Clone Required Repositories

```bash
# Create workspace directory
mkdir ~/adsb-feeder-workspace
cd ~/adsb-feeder-workspace

# Clone adsb-feeder-image project
git clone https://github.com/dirkhh/adsb-feeder-image.git

# Clone CustomPiOS framework with correct branch
git clone https://github.com/guysoft/CustomPiOS.git
cd CustomPiOS
git remote add dirkhh https://github.com/dirkhh/CustomPiOS.git
git fetch dirkhh
git checkout -b adsbim dirkhh/adsbim
cd ..
```

### 2. Configure Build Environment

```bash
cd adsb-feeder-image/src

# Create CustomPiOS path reference (CRITICAL: must point to src directory)
echo "../../CustomPiOS/src" > custompios_path

# Create basic secrets file for local build
cat > .secrets << 'EOF'
# Basic secrets for local build
secrets_USER_PASSWORD="adsb"
secrets_ROOT_PASSWORD="adsb"
secrets_SSH_KEY="ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIG8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K adsb-feeder-local"
EOF
```

## Building the Image (Docker Method - RECOMMENDED)

### Why Docker Method?
- **Cross-platform compatibility**: Works reliably on macOS
- **No macOS tool conflicts**: Bypasses BSD vs GNU tool differences
- **Faster builds**: ~30 minutes vs 1-3 hours
- **Proven reliable**: Used by official CI/CD pipeline

### Build Command

```bash
# Navigate to source directory
cd adsb-feeder-image/src

# Build using Docker (single command)
docker run --rm --privileged -v $(pwd):/distro ghcr.io/guysoft/custompios:devel build --download
```

**Build Process:**
1. **Downloads base image**: Official Raspberry Pi OS ARM64 Lite (~400MB)
2. **Configures environment**: Sets up ARM64 architecture for Pi 5
3. **Installs packages**: Docker, networking tools, system utilities
4. **Configures services**: Hotspot, web interface, Docker containers
5. **Creates final image**: Ready-to-flash image file

## Build Output

### Successful Build Results

```
workspace/
├── 2025-05-13-adsb-feeder-dist-default-bookworm-arm64-lite-(stable).img  # Final image (~3GB)
├── 2025-05-13-adsb-feeder-dist-default-bookworm-arm64-lite-(stable).zip  # Compressed (~747MB)
└── build artifacts and logs
```

## Flashing the Image

### Install Raspberry Pi Imager

```bash
brew install --cask raspberry-pi-imager
```

### Flash to SD Card

1. **Insert microSD card** (32GB+ recommended)
2. **Open Raspberry Pi Imager**
3. **Choose "Use custom image"**
4. **Navigate to**: `workspace/` directory
5. **Select**: `2025-05-13-adsb-feeder-dist-default-bookworm-arm64-lite-(stable).img`
6. **Select your SD card**
7. **Flash the image**

## Testing the Image

### Initial Boot Test

1. **Insert SD card** into Raspberry Pi 5
2. **Connect power** (no network needed initially)
3. **Wait 2-3 minutes** for first boot
4. **Look for WiFi hotspot**: `adsb.im-feeder`

### Hotspot Functionality Test

1. **Connect to hotspot**: `adsb.im-feeder`
2. **Open browser**: Navigate to `http://***********`
3. **Configure WiFi**: Enter your network credentials
4. **Test web interface**: Verify all pages load correctly

## Creating Your Own Custom Image

### Key Insights for AI Agents

When creating a custom image based on adsb-feeder-image:

1. **Use the Docker method**: Native macOS builds have tool compatibility issues
2. **Correct CustomPiOS branch**: Use `dirkhh/CustomPiOS` with `adsbim` branch
3. **Path configuration**: `custompios_path` must point to `CustomPiOS/src` directory
4. **Architecture settings**: Ensure ARM64 for Raspberry Pi 5 compatibility
5. **Module structure**: Follow the `modules/` directory pattern for customizations

### Directory Structure for Custom Images

```
your-custom-image/
├── src/
│   ├── config                    # Main configuration file
│   ├── custompios_path          # Points to ../../CustomPiOS/src
│   ├── .secrets                 # Build credentials
│   ├── modules/
│   │   └── your-module/
│   │       ├── start_chroot_script
│   │       ├── filesystem/
│   │       └── config
│   └── tools/
│       └── build-local.sh
└── CustomPiOS/                  # Clone with adsbim branch
```

### Essential Build Requirements

- **Docker Desktop**: Must be running with privileged access
- **CustomPiOS**: Use `dirkhh/CustomPiOS` repository, `adsbim` branch
- **Base architecture**: Set to `arm64` for modern Raspberry Pi boards
- **Module dependencies**: Follow `base(network,your-module)` pattern

### Proven Build Command

```bash
# Single command that works reliably on macOS
docker run --rm --privileged -v $(pwd):/distro ghcr.io/guysoft/custompios:devel build --download
```

## Troubleshooting

### Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| `build_custom_os: No such file` | Wrong CustomPiOS path | Set `custompios_path` to `../../CustomPiOS/src` |
| `BASE_ARCH=armhf` error | Missing board config | Use `adsbim` branch of `dirkhh/CustomPiOS` |
| `sfdisk: command not found` | macOS tool incompatibility | Use Docker method instead of native build |
| Docker permission errors | Insufficient privileges | Add `--privileged` flag to Docker command |

### Build Verification

Successful build produces:
- `.img` file (~3GB) in `workspace/` directory
- `.zip` compressed version (~747MB)
- No error messages in Docker output
- ARM64 architecture confirmed in build logs

## Resources

- [adsb-feeder-image GitHub](https://github.com/dirkhh/adsb-feeder-image)
- [CustomPiOS Documentation](https://github.com/guysoft/CustomPiOS)
- [Docker CustomPiOS Image](https://github.com/guysoft/CustomPiOS/pkgs/container/custompios)
