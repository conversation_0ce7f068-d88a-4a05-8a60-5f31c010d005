# Real-Time Signal Detection

This directory contains real-time implementations of your signal detection algorithm from `signalanalyser.py`. The detection logic, parameters, and thresholds remain exactly the same - only the audio input method has changed from file-based to real-time streaming.

## Files

- `realtime_signal_detector.py` - PyAudio-based implementation (recommended)
- `realtime_signal_detector_sounddevice.py` - SoundDevice-based alternative
- `requirements_realtime.txt` - Python dependencies
- `REALTIME_DETECTION_README.md` - This file

## Quick Start

### Option 1: PyAudio Implementation (Recommended)

1. **Install dependencies:**
```bash
pip install -r requirements_realtime.txt
```

2. **List available audio devices:**
```bash
python realtime_signal_detector.py --list-devices
```

3. **Start detection with default device:**
```bash
python realtime_signal_detector.py
```

4. **Start detection with specific device:**
```bash
python realtime_signal_detector.py --device 0
```

### Option 2: SoundDevice Implementation (Alternative)

If PyAudio installation fails, try the SoundDevice version:

1. **Install SoundDevice:**
```bash
pip install sounddevice numpy scipy matplotlib
```

2. **List devices and start detection:**
```bash
python realtime_signal_detector_sounddevice.py --list-devices
python realtime_signal_detector_sounddevice.py --device 0
```

## Command Line Options

Both implementations support the same command line options:

- `--device N` - Use audio device N (see --list-devices)
- `--list-devices` - Show available audio input devices
- `--no-plot` - Disable real-time plotting (for headless operation)
- `--thigh N` - Set high threshold (default: 50000)
- `--tlow N` - Set low threshold (default: 100)

## Examples

```bash
# Basic usage with real-time plot
python realtime_signal_detector.py

# Use specific USB sound card (device 1) with custom thresholds
python realtime_signal_detector.py --device 1 --thigh 75000 --tlow 200

# Headless operation (no GUI) for Raspberry Pi
python realtime_signal_detector.py --device 0 --no-plot

# List all available audio devices
python realtime_signal_detector.py --list-devices
```

## Features

### Identical Detection Logic
- Same frequency range: 200-3500 Hz
- Same chunk size: 1024 samples
- Same sample rate: 48000 Hz (matching your arecord command)
- Same hysteresis thresholding with Thigh/Tlow
- Same power calculation using Welch's method

### Real-Time Capabilities
- Live audio streaming from USB sound card
- Real-time signal detection with immediate feedback
- Continuous detection state monitoring
- Signal duration measurement

### Visualization
- Real-time power spectrum plot
- Detection state visualization
- Scrolling time-based display
- Same visual style as original analysis

### Output
- Console logging of detected signals with timestamps
- Signal duration measurements
- Detection summary on exit

## Raspberry Pi 5 Usage

For your Raspberry Pi 5 with USB sound card:

1. **Find your USB sound card:**
```bash
python realtime_signal_detector.py --list-devices
```

2. **Start detection (replace N with your device number):**
```bash
python realtime_signal_detector.py --device N
```

3. **For headless operation:**
```bash
python realtime_signal_detector.py --device N --no-plot
```

## Troubleshooting

### PyAudio Installation Issues
If PyAudio fails to install:
- On Ubuntu/Debian: `sudo apt-get install portaudio19-dev python3-pyaudio`
- On macOS: `brew install portaudio`
- Alternative: Use the SoundDevice version instead

### Audio Device Issues
- Run `--list-devices` to see available devices
- Ensure your USB sound card is connected and recognized
- Try different device indices if the default doesn't work

### Permission Issues (Linux)
Add your user to the audio group:
```bash
sudo usermod -a -G audio $USER
```

## Performance Notes

- **Latency**: ~21ms per chunk (1024 samples at 48kHz)
- **CPU Usage**: Low - similar to original file-based analysis
- **Memory Usage**: Fixed buffer size (10 seconds by default)
- **Real-time Factor**: Processes audio faster than real-time

## Integration with Existing Code

The real-time detector maintains the same detection parameters and logic as your original `signalanalyser.py`, making it easy to integrate:

- Same power calculation method
- Same frequency filtering
- Same hysteresis thresholds
- Same detection state logic

You can easily adjust thresholds based on your existing analysis results.
