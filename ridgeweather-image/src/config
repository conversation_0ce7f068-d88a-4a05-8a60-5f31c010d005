SRCDIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
export DIST_NAME=ridgeweather-dist
export DIST_VERSION="1.0.0"

export RPI_IMAGER_NAME="${DIST_NAME}"
export RPI_IMAGER_DESCRIPTION="RidgeWeather - A simple Raspberry Pi weather station image"
export RPI_IMAGER_WEBSITE="https://github.com/tomsuys/ridgeweather"
export RPI_IMAGER_ICON="https://raw.githubusercontent.com/guysoft/CustomPiOS/devel/media/rpi-imager-CustomPiOS.png"

export BASE_USER=pi
export BASE_ADD_USER=yes
export BASE_OVERRIDE_HOSTNAME=ridgeweather

export MODULES="base(network,ridgeweather)"

export BASE_IMAGE_ENLARGEROOT=1500
export BASE_ARCH=arm64
export BASE_BOARD=raspberrypiarm64
export BASE_USER_PASSWORD=ridgeweather
export ROOT_PWD=ridgeweather
export SSH_PUB_KEY='ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIG8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K ridgeweather-local'
export RIDGEWEATHER_IMAGE_NAME=ridgeweather-raspberrypi64-pi-2-3-4-5-v1.0.0.img
