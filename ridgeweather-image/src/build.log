++++ realpath -s /CustomPiOS/build
+++ dirname /CustomPiOS/build
++ BUILD_SCRIPT_PATH=/CustomPiOS
+++ mktemp
++ export EXTRA_BOARD_CONFIG=/tmp/tmp.UJz6gvZ0tI
++ EXTRA_BOARD_CONFIG=/tmp/tmp.UJz6gvZ0tI
++ /CustomPiOS/custompios_core/generate_board_config.py /tmp/tmp.UJz6gvZ0tI
++ echo 'Temp source file: /tmp/tmp.UJz6gvZ0tI'
Temp source file: /tmp/tmp.UJz6gvZ0tI
++ source /CustomPiOS/common.sh
++ install_cleanup_trap
++ set -e
++ trap cleanup SIGINT SIGTERM
++++ realpath -s /CustomPiOS/build
+++ dirname /CustomPiOS/build
++ CUSTOM_OS_PATH=/CustomPiOS
++ source /CustomPiOS/config default /tmp/tmp.UJz6gvZ0tI default
+++++ realpath -s /CustomPiOS/config
++++ dirname /CustomPiOS/config
+++ CONFIG_DIR=/CustomPiOS
+++ source /CustomPiOS/common.sh
+++ WORKSPACE_POSTFIX=
+++ export BUILD_VARIANT=
+++ BUILD_VARIANT=
+++ BUILD_VARIANT=default
+++ : default
+++ EXTRA_BAORD_CONFIG=/tmp/tmp.UJz6gvZ0tI
+++ export BUILD_FLAVOR=
+++ BUILD_FLAVOR=
+++ : default
+++ echo -e '--> Building VARIANT default, FLAVOR default'
--> Building VARIANT default, FLAVOR default
+++ '[' -f /CustomPiOS/config.local ']'
+++ source /distro/config
++++++ dirname /distro/config
+++++ cd /distro
+++++ pwd
++++ SRCDIR=/distro
++++ export DIST_NAME=ridgeweather-dist
++++ DIST_NAME=ridgeweather-dist
++++ export DIST_VERSION=1.0.0
++++ DIST_VERSION=1.0.0
++++ export RPI_IMAGER_NAME=ridgeweather-dist
++++ RPI_IMAGER_NAME=ridgeweather-dist
++++ export 'RPI_IMAGER_DESCRIPTION=RidgeWeather - A simple Raspberry Pi weather station image'
++++ RPI_IMAGER_DESCRIPTION='RidgeWeather - A simple Raspberry Pi weather station image'
++++ export RPI_IMAGER_WEBSITE=https://github.com/tomsuys/ridgeweather
++++ RPI_IMAGER_WEBSITE=https://github.com/tomsuys/ridgeweather
++++ export RPI_IMAGER_ICON=https://raw.githubusercontent.com/guysoft/CustomPiOS/devel/media/rpi-imager-CustomPiOS.png
++++ RPI_IMAGER_ICON=https://raw.githubusercontent.com/guysoft/CustomPiOS/devel/media/rpi-imager-CustomPiOS.png
++++ export BASE_USER=pi
++++ BASE_USER=pi
++++ export BASE_ADD_USER=yes
++++ BASE_ADD_USER=yes
++++ export BASE_OVERRIDE_HOSTNAME=ridgeweather
++++ BASE_OVERRIDE_HOSTNAME=ridgeweather
++++ export 'MODULES=base(network,ridgeweather)'
++++ MODULES='base(network,ridgeweather)'
++++ export BASE_IMAGE_ENLARGEROOT=1500
++++ BASE_IMAGE_ENLARGEROOT=1500
++++ export BASE_ARCH=arm64
++++ BASE_ARCH=arm64
++++ export BASE_BOARD=raspberrypiarm64
++++ BASE_BOARD=raspberrypiarm64
++++ export BASE_USER_PASSWORD=ridgeweather
++++ BASE_USER_PASSWORD=ridgeweather
++++ export ROOT_PWD=ridgeweather
++++ ROOT_PWD=ridgeweather
++++ export 'SSH_PUB_KEY=ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIG8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K ridgeweather-local'
++++ SSH_PUB_KEY='ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIG8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K ridgeweather-local'
++++ export RIDGEWEATHER_IMAGE_NAME=ridgeweather-raspberrypi64-pi-2-3-4-5-v1.0.0.img
++++ RIDGEWEATHER_IMAGE_NAME=ridgeweather-raspberrypi64-pi-2-3-4-5-v1.0.0.img
+++ '[' default '!=' default ']'
+++ echo Import the variant config if we have one
Import the variant config if we have one
+++ '[' -n '' ']'
+++ '[' -n '' ']'
+++ '[' -f /distro/config.local ']'
+++ TMP='base,network,ridgeweather)'
+++ TMP='base,network,ridgeweather)'
+++ MODULES_LIST=base,network,ridgeweather,
+++ '[' -n /distro/workspace ']'
+++ '[' -n /distro/workspace/mount ']'
+++ '[' -f /tmp/tmp.UJz6gvZ0tI ']'
+++ source /tmp/tmp.UJz6gvZ0tI
++++ export BASE_ARCH=arm64
++++ BASE_ARCH=arm64
+++ export REMOTE_AND_META_CONFIG=/distro/workspace/remote_and_meta_config
+++ REMOTE_AND_META_CONFIG=/distro/workspace/remote_and_meta_config
+++ '[' -f /distro/workspace/remote_and_meta_config ']'
+++ load_module_config base,network,ridgeweather,
+++ MODULES_AFTER=base,network,ridgeweather,
++++ echo base,network,ridgeweather,
++++ tr , '\n'
+++ for module in $(echo "${MODULES_AFTER}" | tr "," "\n")
+++ '[' -d /distro/modules/base ']'
+++ '[' -d /CustomPiOS/modules/base ']'
+++ export MODULE_PATH=/CustomPiOS/modules/base
+++ MODULE_PATH=/CustomPiOS/modules/base
+++ echo 'loading base config at /CustomPiOS/modules/base/config'
loading base config at /CustomPiOS/modules/base/config
+++ '[' -f /CustomPiOS/modules/base/config ']'
+++ source /CustomPiOS/modules/base/config
++++ BASE_VERSION=2.0.0
++++ '[' -n '' ']'
++++ BASE_PRESCRIPT=
++++ '[' -n '' ']'
++++ BASE_POSTSCRIPT=
++++ '[' -n raspberrypiarm64 ']'
++++ '[' -n debian_bookworm ']'
++++ echo 'GOT BASE BOARD raspberrypiarm64'
GOT BASE BOARD raspberrypiarm64
++++ '[' raspberrypiarm64 == raspberrypiarmhf ']'
++++ '[' -n /distro/image-raspberrypiarm64 ']'
+++++ head -n 1
+++++ ls -t '/distro/image-raspberrypiarm64/*.zip' '/distro/image-raspberrypiarm64/*.7z' /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz
ls: cannot access '/distro/image-raspberrypiarm64/*.zip': No such file or directory
ls: cannot access '/distro/image-raspberrypiarm64/*.7z': No such file or directory
++++ BASE_ZIP_IMG=/distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz
++++ '[' -n yes ']'
++++ '[' -n raspbian ']'
++++ '[' raspbian = ubuntu ']'
++++ '[' raspbian == raspios64 ']'
++++ '[' -n /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz ']'
++++ '[' -n pi ']'
++++ '[' -n yes ']'
++++ '[' -n ridgeweather ']'
++++ '[' -n yes ']'
++++ '[' -n default ']'
++++ '[' -n default ']'
++++ '[' -n /distro/workspace ']'
++++ '[' -n /distro/workspace/mount ']'
++++ '[' -n boot/firmware ']'
++++ '[' -n 2 ']'
++++ '[' -n 1500 ']'
++++ '[' -n 200 ']'
++++ '[' -n /distro/workspace/aptcache ']'
++++ '[' -n '' ']'
++++ BASE_APT_PROXY=
++++ '[' -n '' ']'
++++ BASE_APT_MIRROR=
++++ '[' -n '' ']'
++++ BASE_PYPI_INDEX=
++++ '[' -n ridgeweather ']'
++++ '[' -n '' ']'
++++ BASE_USE_ALT_DNS=
++++ '[' -n '' ']'
++++ BASE_BUILD_REPO_MIRROR=
++++ '[' -n yes ']'
++++ '[' -n '' ']'
+++++ git -C /CustomPiOS rev-parse HEAD
fatal: not a git repository (or any of the parent directories): .git
++++ BASE_COMMIT=
++++ true
++++ '[' -n default ']'
++++ '[' -n default ']'
++++ '[' -n default ']'
++++ '[' -n default ']'
++++ '[' -n arm64 ']'
++++ '[' -n no ']'
++++ '[' -n no ']'
++++ : yes
+++ echo ================================================================
================================================================
+++ echo 'Using the following config:'
Using the following config:
+++ module_up=BASE
+++ module_up=BASE_
+++ IFS=
+++ read -r var
++++ compgen -A variable BASE_
+++ export BASE_ADD_USER
+++ echo BASE_ADD_USER
BASE_ADD_USER
+++ IFS=
+++ read -r var
+++ export BASE_APT_CACHE
+++ echo BASE_APT_CACHE
BASE_APT_CACHE
+++ IFS=
+++ read -r var
+++ export BASE_APT_CLEAN
+++ echo BASE_APT_CLEAN
BASE_APT_CLEAN
+++ IFS=
+++ read -r var
+++ export BASE_APT_MIRROR
+++ echo BASE_APT_MIRROR
BASE_APT_MIRROR
+++ IFS=
+++ read -r var
+++ export BASE_APT_PROXY
+++ echo BASE_APT_PROXY
BASE_APT_PROXY
+++ IFS=
+++ read -r var
+++ export BASE_ARCH
+++ echo BASE_ARCH
BASE_ARCH
+++ IFS=
+++ read -r var
+++ export BASE_BOARD
+++ echo BASE_BOARD
BASE_BOARD
+++ IFS=
+++ read -r var
+++ export BASE_BOOT_MOUNT_PATH
+++ echo BASE_BOOT_MOUNT_PATH
BASE_BOOT_MOUNT_PATH
+++ IFS=
+++ read -r var
+++ export BASE_BUILD_REPO_MIRROR
+++ echo BASE_BUILD_REPO_MIRROR
BASE_BUILD_REPO_MIRROR
+++ IFS=
+++ read -r var
+++ export BASE_COMMIT
+++ echo BASE_COMMIT
BASE_COMMIT
+++ IFS=
+++ read -r var
+++ export BASE_CONFIG_KEYBOARD
+++ echo BASE_CONFIG_KEYBOARD
BASE_CONFIG_KEYBOARD
+++ IFS=
+++ read -r var
+++ export BASE_CONFIG_LOCALE
+++ echo BASE_CONFIG_LOCALE
BASE_CONFIG_LOCALE
+++ IFS=
+++ read -r var
+++ export BASE_CONFIG_MEMSPLIT
+++ echo BASE_CONFIG_MEMSPLIT
BASE_CONFIG_MEMSPLIT
+++ IFS=
+++ read -r var
+++ export BASE_CONFIG_TIMEZONE
+++ echo BASE_CONFIG_TIMEZONE
BASE_CONFIG_TIMEZONE
+++ IFS=
+++ read -r var
+++ export BASE_DISTRO
+++ echo BASE_DISTRO
BASE_DISTRO
+++ IFS=
+++ read -r var
+++ export BASE_ENABLE_UART
+++ echo BASE_ENABLE_UART
BASE_ENABLE_UART
+++ IFS=
+++ read -r var
+++ export BASE_IGNORE_VARIANT_NAME
+++ echo BASE_IGNORE_VARIANT_NAME
BASE_IGNORE_VARIANT_NAME
+++ IFS=
+++ read -r var
+++ export BASE_IMAGE_ENLARGEROOT
+++ echo BASE_IMAGE_ENLARGEROOT
BASE_IMAGE_ENLARGEROOT
+++ IFS=
+++ read -r var
+++ export BASE_IMAGE_PATH
+++ echo BASE_IMAGE_PATH
BASE_IMAGE_PATH
+++ IFS=
+++ read -r var
+++ export BASE_IMAGE_RASPBIAN
+++ echo BASE_IMAGE_RASPBIAN
BASE_IMAGE_RASPBIAN
+++ IFS=
+++ read -r var
+++ export BASE_IMAGE_RESIZEROOT
+++ echo BASE_IMAGE_RESIZEROOT
BASE_IMAGE_RESIZEROOT
+++ IFS=
+++ read -r var
+++ export BASE_MOUNT_PATH
+++ echo BASE_MOUNT_PATH
BASE_MOUNT_PATH
+++ IFS=
+++ read -r var
+++ export BASE_OS
+++ echo BASE_OS
BASE_OS
+++ IFS=
+++ read -r var
+++ export BASE_OVERRIDE_HOSTNAME
+++ echo BASE_OVERRIDE_HOSTNAME
BASE_OVERRIDE_HOSTNAME
+++ IFS=
+++ read -r var
+++ export BASE_POSTSCRIPT
+++ echo BASE_POSTSCRIPT
BASE_POSTSCRIPT
+++ IFS=
+++ read -r var
+++ export BASE_PRESCRIPT
+++ echo BASE_PRESCRIPT
BASE_PRESCRIPT
+++ IFS=
+++ read -r var
+++ export BASE_PYPI_INDEX
+++ echo BASE_PYPI_INDEX
BASE_PYPI_INDEX
+++ IFS=
+++ read -r var
+++ export BASE_RELEASE_COMPRESS
+++ echo BASE_RELEASE_COMPRESS
BASE_RELEASE_COMPRESS
+++ IFS=
+++ read -r var
+++ export BASE_RELEASE_IMG_NAME
+++ echo BASE_RELEASE_IMG_NAME
BASE_RELEASE_IMG_NAME
+++ IFS=
+++ read -r var
+++ export BASE_RELEASE_ZIP_NAME
+++ echo BASE_RELEASE_ZIP_NAME
BASE_RELEASE_ZIP_NAME
+++ IFS=
+++ read -r var
+++ export BASE_ROOT_PARTITION
+++ echo BASE_ROOT_PARTITION
BASE_ROOT_PARTITION
+++ IFS=
+++ read -r var
+++ export BASE_SSH_ENABLE
+++ echo BASE_SSH_ENABLE
BASE_SSH_ENABLE
+++ IFS=
+++ read -r var
+++ export BASE_USER
+++ echo BASE_USER
BASE_USER
+++ IFS=
+++ read -r var
+++ export BASE_USER_PASSWORD
+++ echo BASE_USER_PASSWORD
BASE_USER_PASSWORD
+++ IFS=
+++ read -r var
+++ export BASE_USE_ALT_DNS
+++ echo BASE_USE_ALT_DNS
BASE_USE_ALT_DNS
+++ IFS=
+++ read -r var
+++ export BASE_VERSION
+++ echo BASE_VERSION
BASE_VERSION
+++ IFS=
+++ read -r var
+++ export BASE_WORKSPACE
+++ echo BASE_WORKSPACE
BASE_WORKSPACE
+++ IFS=
+++ read -r var
+++ export BASE_ZIP_IMG
+++ echo BASE_ZIP_IMG
BASE_ZIP_IMG
+++ IFS=
+++ read -r var
+++ echo ================================================================
================================================================
+++ for module in $(echo "${MODULES_AFTER}" | tr "," "\n")
+++ '[' -d /distro/modules/network ']'
+++ '[' -d /CustomPiOS/modules/network ']'
+++ export MODULE_PATH=/CustomPiOS/modules/network
+++ MODULE_PATH=/CustomPiOS/modules/network
+++ echo 'loading network config at /CustomPiOS/modules/network/config'
loading network config at /CustomPiOS/modules/network/config
+++ '[' -f /CustomPiOS/modules/network/config ']'
+++ source /CustomPiOS/modules/network/config
++++ '[' -n yes ']'
++++ '[' -n udev ']'
++++ '[' -n no ']'
++++ '[' -n yes ']'
+++ echo ================================================================
================================================================
+++ echo 'Using the following config:'
Using the following config:
+++ module_up=NETWORK
+++ module_up=NETWORK_
+++ IFS=
+++ read -r var
++++ compgen -A variable NETWORK_
+++ export NETWORK_DISABLE_PWRSAVE
+++ echo NETWORK_DISABLE_PWRSAVE
NETWORK_DISABLE_PWRSAVE
+++ IFS=
+++ read -r var
+++ export NETWORK_NETWORK_MANAGER
+++ echo NETWORK_NETWORK_MANAGER
NETWORK_NETWORK_MANAGER
+++ IFS=
+++ read -r var
+++ export NETWORK_PWRSAVE_TYPE
+++ echo NETWORK_PWRSAVE_TYPE
NETWORK_PWRSAVE_TYPE
+++ IFS=
+++ read -r var
+++ export NETWORK_WPA_SUPPLICANT
+++ echo NETWORK_WPA_SUPPLICANT
NETWORK_WPA_SUPPLICANT
+++ IFS=
+++ read -r var
+++ echo ================================================================
================================================================
+++ for module in $(echo "${MODULES_AFTER}" | tr "," "\n")
+++ '[' -d /distro/modules/ridgeweather ']'
+++ export MODULE_PATH=/distro/modules/ridgeweather
+++ MODULE_PATH=/distro/modules/ridgeweather
+++ echo 'loading ridgeweather config at /distro/modules/ridgeweather/config'
loading ridgeweather config at /distro/modules/ridgeweather/config
+++ '[' -f /distro/modules/ridgeweather/config ']'
+++ echo 'WARNING: module ridgeweather has no config file'
WARNING: module ridgeweather has no config file
+++ echo ================================================================
================================================================
+++ echo 'Using the following config:'
Using the following config:
+++ module_up=RIDGEWEATHER
+++ module_up=RIDGEWEATHER_
+++ IFS=
++++ compgen -A variable RIDGEWEATHER_
+++ read -r var
+++ export RIDGEWEATHER_IMAGE_NAME
+++ echo RIDGEWEATHER_IMAGE_NAME
RIDGEWEATHER_IMAGE_NAME
+++ IFS=
+++ read -r var
+++ echo ================================================================
================================================================
++ /CustomPiOS/config_sanity
++ '[' '' == yes ']'
++ source /CustomPiOS/custompios default
+++ set -e
+++ export LC_ALL=C
+++ LC_ALL=C
+++ source /CustomPiOS/common.sh
++++ date
+++ echo_green -e '\nBUILD STARTED @ Mon Jul 14 14:40:55 UTC 2025!\n'
+++ echo -e -n '\e[92m'
[92m+++ echo -e '\nBUILD' STARTED @ Mon Jul 14 14:40:55 UTC '2025!\n'

BUILD STARTED @ Mon Jul 14 14:40:55 UTC 2025!

+++ echo -e -n '\e[0m'
[0m+++ '[' -n 1500 ']'
+++ which sfdisk
+++ mkdir -p /distro/workspace
+++ mkdir -p /distro/workspace/mount
+++ '[' -f /tmp/tmp.UJz6gvZ0tI ']'
+++ mv -v /tmp/tmp.UJz6gvZ0tI /distro/workspace/extra_board_config
copied '/tmp/tmp.UJz6gvZ0tI' -> '/distro/workspace/extra_board_config'
removed '/tmp/tmp.UJz6gvZ0tI'
+++ rm -rf '/distro/workspace/*.tar.gz'
+++ install_cleanup_trap
+++ set -e
+++ trap cleanup SIGINT SIGTERM
+++ install_fail_on_error_trap
+++ set -e
+++ trap 'echo_red "build failed, unmounting image..." && cd $DIST_PATH && ( unmount_image $BASE_MOUNT_PATH force || true ) && echo_red -e "\nBUILD FAILED!\n"' ERR
+++ unmount_image /distro/workspace/mount force
+++ mount_path=/distro/workspace/mount
+++ force=
+++ '[' 2 -gt 1 ']'
+++ force=force
+++ sync
+++ '[' -n force ']'
++++ sudo lsof -t /distro/workspace/mount
++++ sudo mount
++++ grep /distro/workspace/mount
++++ awk -F ' on ' '{print $2}'
++++ awk '{print $1}'
++++ sort -r
+++ pushd /distro/workspace
/distro/workspace /distro
+++ '[' -e '*.img' ']'
+++ '[' '!' -f /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz ']'
+++ '[' /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz == '' ']'
+++ [[ /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz =~ \.img$ ]]
+++ 7za x -aoa /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz

7-Zip (a) [64] 16.02 : Copyright (c) 1999-2016 Igor Pavlov : 2016-05-21
p7zip Version 16.02 (locale=C,Utf16=off,HugeFiles=on,64 bits,10 CPUs LE)

Scanning the drive for archives:
1 file, 443120736 bytes (423 MiB)

Extracting archive: /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz
--
Path = /distro/image-raspberrypiarm64/2025-05-13-raspios-bookworm-arm64-lite.img.xz
Type = xz
Physical Size = 443120736
Method = LZMA2:26 CRC64
Streams = 1
Blocks = 14

Everything is Ok

Size:       2759852032
Compressed: 443120736
++++ ls
++++ grep '.img$\|.raw$'
++++ head -n 1
+++ BASE_IMG_PATH=2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' '!' -f 2025-05-13-raspios-bookworm-arm64-lite.img ']'
++++ basename 2025-05-13-raspios-bookworm-arm64-lite.img
+++ export CUSTOM_PI_OS_BUILDBASE=2025-05-13-raspios-bookworm-arm64-lite.img
+++ CUSTOM_PI_OS_BUILDBASE=2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' -n 1500 ']'
+++ enlarge_ext 2025-05-13-raspios-bookworm-arm64-lite.img 2 1500
+++ image=2025-05-13-raspios-bookworm-arm64-lite.img
+++ partition=2
+++ size=1500
+++ echo_green 'Adding 1500 MB to partition 2 of 2025-05-13-raspios-bookworm-arm64-lite.img'
+++ echo -e -n '\e[92m'
[92m+++ echo Adding 1500 MB to partition 2 of 2025-05-13-raspios-bookworm-arm64-lite.img
Adding 1500 MB to partition 2 of 2025-05-13-raspios-bookworm-arm64-lite.img
+++ echo -e -n '\e[0m'
[0m++++ sfdisk --json 2025-05-13-raspios-bookworm-arm64-lite.img
++++ jq '.partitiontable.partitions[] | select(.node ==  "2025-05-13-raspios-bookworm-arm64-lite.img2").start'
+++ start=1064960
+++ offset=545259520
+++ dd if=/dev/zero bs=1M count=1500
1500+0 records in
1500+0 records out
1572864000 bytes (1.6 GB, 1.5 GiB) copied, 3.18534 s, 494 MB/s
+++ fdisk 2025-05-13-raspios-bookworm-arm64-lite.img

Welcome to fdisk (util-linux 2.38.1).
Changes will remain in memory only, until you decide to write them.
Be careful before using the write command.


Command (m for help): Disk 2025-05-13-raspios-bookworm-arm64-lite.img: 4.04 GiB, 4332716032 bytes, 8462336 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: dos
Disk identifier: 0xd9c86127

Device                                      Boot   Start     End Sectors  Size Id Type
2025-05-13-raspios-bookworm-arm64-lite.img1        16384 1064959 1048576  512M  c W95 FAT32 (LBA)
2025-05-13-raspios-bookworm-arm64-lite.img2      1064960 5390335 4325376  2.1G 83 Linux

Command (m for help): Partition number (1,2, default 2): 
Partition 2 has been deleted.

Command (m for help): Partition type
   p   primary (1 primary, 0 extended, 3 free)
   e   extended (container for logical partitions)
Select (default p): Partition number (2-4, default 2): First sector (2048-8462335, default 2048): Last sector, +/-sectors or +/-size{K,M,G,T,P} (1064960-8462335, default 8462335): 
Created a new partition 2 of type 'Linux' and of size 3.5 GiB.
Partition #2 contains a ext4 signature.

Command (m for help): 
Disk 2025-05-13-raspios-bookworm-arm64-lite.img: 4.04 GiB, 4332716032 bytes, 8462336 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: dos
Disk identifier: 0xd9c86127

Device                                      Boot   Start     End Sectors  Size Id Type
2025-05-13-raspios-bookworm-arm64-lite.img1        16384 1064959 1048576  512M  c W95 FAT32 (LBA)
2025-05-13-raspios-bookworm-arm64-lite.img2      1064960 8462335 7397376  3.5G 83 Linux

Command (m for help): The partition table has been altered.
Syncing disks.

+++ detach_all_loopback 2025-05-13-raspios-bookworm-arm64-lite.img
+++ image_name=2025-05-13-raspios-bookworm-arm64-lite.img
++++ losetup
++++ grep 2025-05-13-raspios-bookworm-arm64-lite.img
++++ awk '{ print $1 }'
+++ test_for_image 2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' '!' -f 2025-05-13-raspios-bookworm-arm64-lite.img ']'
++++ losetup -f --show -o 545259520 2025-05-13-raspios-bookworm-arm64-lite.img
+++ LODEV=/dev/loop7
+++ trap 'losetup -d $LODEV' EXIT
+++ file -Ls /dev/loop7
+++ grep -qi ext
+++ e2fsck -fy /dev/loop7
e2fsck 1.47.0 (5-Feb-2023)
Pass 1: Checking inodes, blocks, and sizes
Pass 2: Checking directory structure
Pass 3: Checking directory connectivity
Pass 4: Checking reference counts
Pass 5: Checking group summary information
rootfs: 61598/135184 files (0.2% non-contiguous), 422901/540672 blocks
+++ resize2fs -p /dev/loop7
resize2fs 1.47.0 (5-Feb-2023)
Resizing the filesystem on /dev/loop7 to 924672 (4k) blocks.
The filesystem on /dev/loop7 is now 924672 (4k) blocks long.

+++ losetup -d /dev/loop7
+++ trap - EXIT
+++ echo_green 'Resized partition 2 of 2025-05-13-raspios-bookworm-arm64-lite.img to +1500 MB'
+++ echo -e -n '\e[92m'
[92m+++ echo Resized partition 2 of 2025-05-13-raspios-bookworm-arm64-lite.img to +1500 MB
Resized partition 2 of 2025-05-13-raspios-bookworm-arm64-lite.img to +1500 MB
+++ echo -e -n '\e[0m'
[0m+++ mount_image 2025-05-13-raspios-bookworm-arm64-lite.img 2 /distro/workspace/mount boot/firmware ''
+++ image_path=2025-05-13-raspios-bookworm-arm64-lite.img
+++ root_partition=2
+++ mount_path=/distro/workspace/mount
+++ boot_mount_path=boot
+++ '[' 5 -gt 3 ']'
+++ boot_mount_path=boot/firmware
+++ '[' 5 -gt 4 ']'
+++ '[' '' '!=' '' ']'
+++ boot_partition=1
++++ sfdisk --json 2025-05-13-raspios-bookworm-arm64-lite.img
+++ fdisk_output='{
   "partitiontable": {
      "label": "dos",
      "id": "0xd9c86127",
      "device": "2025-05-13-raspios-bookworm-arm64-lite.img",
      "unit": "sectors",
      "sectorsize": 512,
      "partitions": [
         {
            "node": "2025-05-13-raspios-bookworm-arm64-lite.img1",
            "start": 16384,
            "size": 1048576,
            "type": "c"
         },{
            "node": "2025-05-13-raspios-bookworm-arm64-lite.img2",
            "start": 1064960,
            "size": 7397376,
            "type": "83"
         }
      ]
   }
}'
++++ jq '.partitiontable.partitions[] | select(.node == "2025-05-13-raspios-bookworm-arm64-lite.img1").start'
+++ boot_offset=8388608
++++ jq '.partitiontable.partitions[] | select(.node == "2025-05-13-raspios-bookworm-arm64-lite.img2").start'
+++ root_offset=545259520
+++ echo_green 'Mounting image 2025-05-13-raspios-bookworm-arm64-lite.img on /distro/workspace/mount, offset for boot partition is 8388608, offset for root partition is 545259520'
+++ echo -e -n '\e[92m'
[92m+++ echo Mounting image 2025-05-13-raspios-bookworm-arm64-lite.img on /distro/workspace/mount, offset for boot partition is 8388608, offset for root partition is 545259520
Mounting image 2025-05-13-raspios-bookworm-arm64-lite.img on /distro/workspace/mount, offset for boot partition is 8388608, offset for root partition is 545259520
+++ echo -e -n '\e[0m'
[0m+++ detach_all_loopback 2025-05-13-raspios-bookworm-arm64-lite.img
+++ image_name=2025-05-13-raspios-bookworm-arm64-lite.img
++++ losetup
++++ grep 2025-05-13-raspios-bookworm-arm64-lite.img
++++ awk '{ print $1 }'
+++ echo_green 'Mounting root partition'
+++ echo -e -n '\e[92m'
[92m+++ echo Mounting root partition
Mounting root partition
+++ echo -e -n '\e[0m'
[0m+++ sudo losetup -f
/dev/loop7
+++ sudo mount -o loop,offset=545259520 2025-05-13-raspios-bookworm-arm64-lite.img /distro/workspace/mount/
+++ [[ 1 != \2 ]]
+++ echo_green 'Mounting boot partition'
+++ echo -e -n '\e[92m'
[92m+++ echo Mounting boot partition
Mounting boot partition
+++ echo -e -n '\e[0m'
[0m+++ sudo losetup -f
/dev/loop8
++++ expr 545259520 - 8388608
+++ sudo mount -o loop,offset=8388608,sizelimit=536870912 2025-05-13-raspios-bookworm-arm64-lite.img /distro/workspace/mount/boot/firmware
+++ sudo mkdir -p /distro/workspace/mount/dev/pts
+++ sudo mkdir -p /distro/workspace/mount/proc
+++ sudo mkdir -p /distro/workspace/mount/sys
+++ sudo mount -o bind /dev /distro/workspace/mount/dev
+++ sudo mount -o bind /dev/pts /distro/workspace/mount/dev/pts
+++ sudo mount -o bind,ro /proc /distro/workspace/mount/proc
+++ sudo mount -o bind,ro /sys /distro/workspace/mount/sys
+++ '[' -n /distro/workspace/aptcache ']'
+++ '[' /distro/workspace/aptcache '!=' no ']'
+++ mkdir -p /distro/workspace/aptcache
+++ mount --bind /distro/workspace/aptcache /distro/workspace/mount/var/cache/apt
+++ pushd /distro/workspace/mount
/distro/workspace/mount /distro/workspace /distro
+++ '[' yes == yes ']'
+++ fixLd
+++ '[' -f etc/ld.so.preload ']'
+++ '[' -n '' ']'
+++ '[' -n '' ']'
+++ CHROOT_SCRIPT=/distro/workspace/chroot_script
+++ MODULES_AFTER_PATH=/distro/workspace/modules_after
+++ MODULES_BEFORE='base(network,ridgeweather)'
+++ /CustomPiOS/custompios_core/execution_order.py 'base(network,ridgeweather)' /distro/workspace/chroot_script /distro/workspace/modules_after /distro/workspace/remote_and_meta_config
Running: /CustomPiOS/modules/base/meta
ENV: raspberrypiarm64
Adding in modules: 
Running: /CustomPiOS/modules/network/meta
ENV: raspberrypiarm64
Adding in modules: 
Running: /CustomPiOS/modules/network/meta
ENV: raspberrypiarm64
Adding in modules: 
Running: /CustomPiOS/modules/base/meta
ENV: raspberrypiarm64
Adding in modules: 
WARNING: No file at - /CustomPiOS/modules/network/end_chroot_script
+++ '[' -f /distro/workspace/remote_and_meta_config ']'
+++ echo 'Sourcing remote and submodules config'
Sourcing remote and submodules config
+++ source /distro/workspace/remote_and_meta_config default
++++ cat /distro/workspace/modules_after
+++ MODULES_AFTER=
+++ load_module_config ''
+++ MODULES_AFTER=
++++ echo ''
++++ tr , '\n'
+++ echo

+++ export -f chroot_correct_qemu
+++ export -f execute_chroot_script
+++ bash -x /distro/workspace/chroot_script
+ set -x
+ set -e
+ execute_chroot_script /CustomPiOS/modules/base /CustomPiOS/modules/base/start_chroot_script
+ '[' -f /.dockerenv ']'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
++ uname -m
+ '[' aarch64 '!=' aarch64 ']'
+ '[' -d /CustomPiOS/modules/base/filesystem ']'
+ cp -vr --preserve=mode,timestamps /CustomPiOS/modules/base/filesystem .
'/CustomPiOS/modules/base/filesystem' -> './filesystem'
'/CustomPiOS/modules/base/filesystem/ubuntu' -> './filesystem/ubuntu'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr' -> './filesystem/ubuntu/usr'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib' -> './filesystem/ubuntu/usr/lib'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib/dhcpcd' -> './filesystem/ubuntu/usr/lib/dhcpcd'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks' -> './filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks/10-wpa_supplicant' -> './filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks/10-wpa_supplicant'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
+ '[' arm64 == armv7l ']'
+ '[' arm64 == armhf ']'
+ '[' arm64 == aarch64 ']'
+ '[' arm64 == arm64 ']'
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ cp /CustomPiOS/modules/base/start_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
++ uname -m
+ chroot_correct_qemu aarch64 arm64 /CustomPiOS/modules/base/start_chroot_script /CustomPiOS
+ local host_arch=aarch64
+ local target_arch=arm64
+ local chroot_script=/CustomPiOS/modules/base/start_chroot_script
+ local custom_pi_os_path=/CustomPiOS
+ [[ -z aarch64 ]]
+ [[ -z arm64 ]]
+ cp /CustomPiOS/modules/base/start_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ [[ arm64 == \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\6\4 ]]
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ aarch64 != \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ echo 'Building on ARM device a armv7l/aarch64/arm64 system, not using qemu'
Building on ARM device a armv7l/aarch64/arm64 system, not using qemu
+ chroot . /bin/bash /chroot_script
+ set -e
+ export LC_ALL=C
+ LC_ALL=C
+ source /common.sh
+ install_cleanup_trap
+ set -e
+ trap cleanup SIGINT SIGTERM
+ '[' -n '' ']'
+ '[' raspbian == ubuntu ']'
+ '[' raspbian '!=' ubuntu ']'
+ '[' -h /etc/resolv.conf ']'
+ mv /etc/resolv.conf /etc/resolv.conf.orig
+ generate_resolvconf
+ '[' -z '' ']'
+ touch /etc/resolv.conf
+ for dns in ******* ******* *******
+ echo 'nameserver *******'
+ for dns in ******* ******* *******
+ echo 'nameserver *******'
+ for dns in ******* ******* *******
+ echo 'nameserver *******'
+ '[' raspberrypiarm64 == debian_lepotato ']'
+ '[' yes == yes ']'
+ '[' raspbian == raspbian ']'
+ echo_green 'Setup default user and password ...'
+ echo -e -n '\e[92m'
[92m+ echo Setup default user and password ...
Setup default user and password ...
+ echo -e -n '\e[0m'
[0m++ get_os_version
++ local os_version
++ grep -c buster /etc/os-release
+ '[' 0 == 0 ']'
+ create_userconf
+ local pw_encrypt
+ '[' -n pi ']'
++ echo ridgeweather
++ openssl passwd -6 -stdin
+ pw_encrypt='$6$K4I46s8AI4SG6RMy$HSH2JnTbXuaNnllUByUMIfyOAUoY84IrLZRBX7wCYu/jB3V6ZoPB9Loa1ctiydAJY9JPMVoY3dVr.R8kRrDu5/'
+ echo 'pi:$6$K4I46s8AI4SG6RMy$HSH2JnTbXuaNnllUByUMIfyOAUoY84IrLZRBX7wCYu/jB3V6ZoPB9Loa1ctiydAJY9JPMVoY3dVr.R8kRrDu5/'
+ apt-get install --yes --only-upgrade userconf-pi
Reading package lists...
Building dependency tree...
Reading state information...
userconf-pi is already the newest version (0.11).
0 upgraded, 0 newly installed, 0 to remove and 0 not upgraded.
+ [[ -f /usr/bin/cancel-rename ]]
+ sed -i 's|do_boot_behaviour B2|do_boot_behaviour B1|g' /usr/bin/cancel-rename
+ '[' -n '' ']'
+ '[' -n '' ']'
+ '[' raspbian == debian ']'
+ '[' yes == yes ']'
+ '[' raspbian == debian ']'
+ touch /boot/firmware/ssh
+ echo 'IPQoS 0x00'
+ echo 'IPQoS 0x00'
+ '[' -f /lib/systemd/system/regenerate_ssh_host_keys.service ']'
+ sed -i 's@ExecStart=/usr/bin/ssh-keygen -A -v@ExecStart=/bin/bash -c '\'' /usr/bin/ssh-keygen -A -v >> /var/log/regenerate_ssh_host_keys.log 2>\&1'\''@g' /lib/systemd/system/regenerate_ssh_host_keys.service
+ sed -i 's@ExecStartPost=/bin/systemctl disable regenerate_ssh_host_keys@ExecStartPost=/bin/bash -c '\''for i in /etc/ssh/ssh_host_*_key*; do actualsize=$(wc -c <"$i") ;if [ $actualsize -eq 0 ]; then echo size is 0 bytes ; exit 1 ; fi ; done ; /bin/systemctl disable regenerate_ssh_host_keys'\''@g' /lib/systemd/system/regenerate_ssh_host_keys.service
+ echo 1.0.0
+ echo default
+ '[' default '!=' default ']'
+ '[' default '!=' default ']'
+ '[' default '!=' default ']'
+ '[' default '!=' default ']'
+ '[' no == yes ']'
+ '[' -d custompios_export ']'
+ rm chroot_script
+ '[' -d filesystem ']'
+ rm -rfv filesystem
removed 'filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks/10-wpa_supplicant'
removed directory 'filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks'
removed directory 'filesystem/ubuntu/usr/lib/dhcpcd'
removed directory 'filesystem/ubuntu/usr/lib'
removed directory 'filesystem/ubuntu/usr'
removed directory 'filesystem/ubuntu'
removed directory 'filesystem'
+ execute_chroot_script /CustomPiOS/modules/network /CustomPiOS/modules/network/start_chroot_script
+ '[' -f /.dockerenv ']'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
++ uname -m
+ '[' aarch64 '!=' aarch64 ']'
+ '[' -d /CustomPiOS/modules/network/filesystem ']'
+ cp -vr --preserve=mode,timestamps /CustomPiOS/modules/network/filesystem .
'/CustomPiOS/modules/network/filesystem' -> './filesystem'
'/CustomPiOS/modules/network/filesystem/etc' -> './filesystem/etc'
'/CustomPiOS/modules/network/filesystem/etc/systemd' -> './filesystem/etc/systemd'
'/CustomPiOS/modules/network/filesystem/etc/systemd/system' -> './filesystem/etc/systemd/system'
'/CustomPiOS/modules/network/filesystem/etc/systemd/system/disable-wifi-pwr-mgmt.service' -> './filesystem/etc/systemd/system/disable-wifi-pwr-mgmt.service'
'/CustomPiOS/modules/network/filesystem/etc/udev' -> './filesystem/etc/udev'
'/CustomPiOS/modules/network/filesystem/etc/udev/rules.d' -> './filesystem/etc/udev/rules.d'
'/CustomPiOS/modules/network/filesystem/etc/udev/rules.d/070-wifi-powersave.rules' -> './filesystem/etc/udev/rules.d/070-wifi-powersave.rules'
'/CustomPiOS/modules/network/filesystem/network-manager' -> './filesystem/network-manager'
'/CustomPiOS/modules/network/filesystem/network-manager/boot' -> './filesystem/network-manager/boot'
'/CustomPiOS/modules/network/filesystem/network-manager/boot/wifi.nmconnection' -> './filesystem/network-manager/boot/wifi.nmconnection'
'/CustomPiOS/modules/network/filesystem/network-manager/root' -> './filesystem/network-manager/root'
'/CustomPiOS/modules/network/filesystem/network-manager/root/etc' -> './filesystem/network-manager/root/etc'
'/CustomPiOS/modules/network/filesystem/network-manager/root/etc/systemd' -> './filesystem/network-manager/root/etc/systemd'
'/CustomPiOS/modules/network/filesystem/network-manager/root/etc/systemd/system' -> './filesystem/network-manager/root/etc/systemd/system'
'/CustomPiOS/modules/network/filesystem/network-manager/root/etc/systemd/system/copy-network-manager-config@.service' -> './filesystem/network-manager/root/etc/systemd/system/copy-network-manager-config@.service'
'/CustomPiOS/modules/network/filesystem/network-manager/root/opt' -> './filesystem/network-manager/root/opt'
'/CustomPiOS/modules/network/filesystem/network-manager/root/opt/custompios' -> './filesystem/network-manager/root/opt/custompios'
'/CustomPiOS/modules/network/filesystem/network-manager/root/opt/custompios/copy-network-manager-config' -> './filesystem/network-manager/root/opt/custompios/copy-network-manager-config'
'/CustomPiOS/modules/network/filesystem/usr' -> './filesystem/usr'
'/CustomPiOS/modules/network/filesystem/usr/local' -> './filesystem/usr/local'
'/CustomPiOS/modules/network/filesystem/usr/local/bin' -> './filesystem/usr/local/bin'
'/CustomPiOS/modules/network/filesystem/usr/local/bin/pwrsave' -> './filesystem/usr/local/bin/pwrsave'
'/CustomPiOS/modules/network/filesystem/usr/local/bin/pwrsave-udev' -> './filesystem/usr/local/bin/pwrsave-udev'
'/CustomPiOS/modules/network/filesystem/wpa-supplicant' -> './filesystem/wpa-supplicant'
'/CustomPiOS/modules/network/filesystem/wpa-supplicant/boot' -> './filesystem/wpa-supplicant/boot'
'/CustomPiOS/modules/network/filesystem/wpa-supplicant/boot/custompios-wpa-supplicant.txt' -> './filesystem/wpa-supplicant/boot/custompios-wpa-supplicant.txt'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
+ '[' arm64 == armv7l ']'
+ '[' arm64 == armhf ']'
+ '[' arm64 == aarch64 ']'
+ '[' arm64 == arm64 ']'
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ cp /CustomPiOS/modules/network/start_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
++ uname -m
+ chroot_correct_qemu aarch64 arm64 /CustomPiOS/modules/network/start_chroot_script /CustomPiOS
+ local host_arch=aarch64
+ local target_arch=arm64
+ local chroot_script=/CustomPiOS/modules/network/start_chroot_script
+ local custom_pi_os_path=/CustomPiOS
+ [[ -z aarch64 ]]
+ [[ -z arm64 ]]
+ cp /CustomPiOS/modules/network/start_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ [[ arm64 == \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\6\4 ]]
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ aarch64 != \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ echo 'Building on ARM device a armv7l/aarch64/arm64 system, not using qemu'
Building on ARM device a armv7l/aarch64/arm64 system, not using qemu
+ chroot . /bin/bash /chroot_script
+ set -e
+ export LC_ALL=C
+ LC_ALL=C
+ source /common.sh
+ install_cleanup_trap
+ set -e
+ trap cleanup SIGINT SIGTERM
+ '[' no == yes ']'
+ '[' yes == yes ']'
+ unpack filesystem/network-manager/root / root
+ from=filesystem/network-manager/root
+ to=/
+ owner=
+ '[' 3 -gt 2 ']'
+ owner=root
+ mkdir -p /tmp/unpack/
+ cp -v -r --preserve=mode,timestamps filesystem/network-manager/root/. /tmp/unpack/
'filesystem/network-manager/root/./etc' -> '/tmp/unpack/./etc'
'filesystem/network-manager/root/./etc/systemd' -> '/tmp/unpack/./etc/systemd'
'filesystem/network-manager/root/./etc/systemd/system' -> '/tmp/unpack/./etc/systemd/system'
'filesystem/network-manager/root/./etc/systemd/system/copy-network-manager-config@.service' -> '/tmp/unpack/./etc/systemd/system/copy-network-manager-config@.service'
'filesystem/network-manager/root/./opt' -> '/tmp/unpack/./opt'
'filesystem/network-manager/root/./opt/custompios' -> '/tmp/unpack/./opt/custompios'
'filesystem/network-manager/root/./opt/custompios/copy-network-manager-config' -> '/tmp/unpack/./opt/custompios/copy-network-manager-config'
+ '[' -n root ']'
+ chown -hR root:root /tmp/unpack/
+ cp -v -r --preserve=mode,ownership,timestamps /tmp/unpack/. /
'/tmp/unpack/./etc/systemd/system/copy-network-manager-config@.service' -> '/./etc/systemd/system/copy-network-manager-config@.service'
'/tmp/unpack/./opt/custompios' -> '/./opt/custompios'
'/tmp/unpack/./opt/custompios/copy-network-manager-config' -> '/./opt/custompios/copy-network-manager-config'
+ rm -r /tmp/unpack
+ unpack filesystem/network-manager/boot /boot/firmware
+ from=filesystem/network-manager/boot
+ to=/boot/firmware
+ owner=
+ '[' 2 -gt 2 ']'
+ mkdir -p /tmp/unpack/
+ cp -v -r --preserve=mode,timestamps filesystem/network-manager/boot/. /tmp/unpack/
'filesystem/network-manager/boot/./wifi.nmconnection' -> '/tmp/unpack/./wifi.nmconnection'
+ '[' -n '' ']'
+ cp -v -r --preserve=mode,ownership,timestamps /tmp/unpack/. /boot/firmware
'/tmp/unpack/./wifi.nmconnection' -> '/boot/firmware/./wifi.nmconnection'
+ rm -r /tmp/unpack
+ '[' raspberrypiarm64 == debian_lepotato ']'
+ systemctl_if_<NAME_EMAIL>
+ hash systemctl
+ <NAME_EMAIL>
Created symlink /etc/systemd/system/multi-user.target.wants/<EMAIL> -> /etc/systemd/system/copy-network-manager-config@.service.
+ '[' raspbian == raspbian ']'
+ rm /var/lib/systemd/rfkill/platform-107d50c000.serial:bluetooth /var/lib/systemd/rfkill/platform-20215040.serial:bluetooth /var/lib/systemd/rfkill/platform-3f215040.serial:bluetooth /var/lib/systemd/rfkill/platform-fe215040.serial:bluetooth /var/lib/systemd/rfkill/platform-soc:bluetooth
+ '[' -f /var/lib/NetworkManager/NetworkManager.state ']'
+ sed -i s/WirelessEnabled=false/WirelessEnabled=true/g /var/lib/NetworkManager/NetworkManager.state
+ '[' '!' -f /etc/rc.local ']'
+ echo 'exit 0'
+ apt_update_skip
+ '[' -f /var/cache/apt/pkgcache.bin ']'
++ date +%s
++ stat -c %Y /var/cache/apt/pkgcache.bin
+ '[' 1 -lt 3600 ']'
+ echo_green 'APT Cache needs no update! [SKIPPED]'
+ echo -e -n '\e[92m'
[92m+ echo APT Cache needs no 'update!' '[SKIPPED]'
APT Cache needs no update! [SKIPPED]
+ echo -e -n '\e[0m'
[0m+ apt-get install -y iptables
Reading package lists...
Building dependency tree...
Reading state information...
The following additional packages will be installed:
  libip6tc2
Suggested packages:
  firewalld
The following NEW packages will be installed:
  iptables libip6tc2
0 upgraded, 2 newly installed, 0 to remove and 0 not upgraded.
Need to get 370 kB of archives.
After this operation, 8730 kB of additional disk space will be used.
Get:1 http://deb.debian.org/debian bookworm/main arm64 libip6tc2 arm64 1.8.9-2 [18.7 kB]
Get:2 http://deb.debian.org/debian bookworm/main arm64 iptables arm64 1.8.9-2 [352 kB]
debconf: unable to initialize frontend: Dialog
debconf: (TERM is not set, so the dialog frontend is not usable.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Teletype
dpkg-preconfigure: unable to re-open stdin: 
Fetched 370 kB in 0s (1586 kB/s)
Selecting previously unselected package libip6tc2:arm64.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 56180 files and directories currently installed.)
Preparing to unpack .../libip6tc2_1.8.9-2_arm64.deb ...
Unpacking libip6tc2:arm64 (1.8.9-2) ...
Selecting previously unselected package iptables.
Preparing to unpack .../iptables_1.8.9-2_arm64.deb ...
Unpacking iptables (1.8.9-2) ...
Setting up libip6tc2:arm64 (1.8.9-2) ...
Setting up iptables (1.8.9-2) ...
update-alternatives: using /usr/sbin/iptables-legacy to provide /usr/sbin/iptables (iptables) in auto mode
update-alternatives: using /usr/sbin/ip6tables-legacy to provide /usr/sbin/ip6tables (ip6tables) in auto mode
update-alternatives: using /usr/sbin/iptables-nft to provide /usr/sbin/iptables (iptables) in auto mode
update-alternatives: using /usr/sbin/ip6tables-nft to provide /usr/sbin/ip6tables (ip6tables) in auto mode
update-alternatives: using /usr/sbin/arptables-nft to provide /usr/sbin/arptables (arptables) in auto mode
update-alternatives: using /usr/sbin/ebtables-nft to provide /usr/sbin/ebtables (ebtables) in auto mode
Processing triggers for man-db (2.11.2-2) ...
Processing triggers for libc-bin (2.36-9+rpt2+deb12u10) ...
+ sed -i 's@exit 0@@' /etc/rc.local
+ echo '/sbin/iptables -t mangle -I POSTROUTING 1 -o wlan0 -p udp --dport 123 -j TOS --set-tos 0x00'
+ echo 'exit 0'
+ '[' yes == yes ']'
+ unpack filesystem/usr/local/bin /usr/local/bin root
+ from=filesystem/usr/local/bin
+ to=/usr/local/bin
+ owner=
+ '[' 3 -gt 2 ']'
+ owner=root
+ mkdir -p /tmp/unpack/
+ cp -v -r --preserve=mode,timestamps filesystem/usr/local/bin/. /tmp/unpack/
'filesystem/usr/local/bin/./pwrsave' -> '/tmp/unpack/./pwrsave'
'filesystem/usr/local/bin/./pwrsave-udev' -> '/tmp/unpack/./pwrsave-udev'
+ '[' -n root ']'
+ chown -hR root:root /tmp/unpack/
+ cp -v -r --preserve=mode,ownership,timestamps /tmp/unpack/. /usr/local/bin
'/tmp/unpack/./pwrsave' -> '/usr/local/bin/./pwrsave'
'/tmp/unpack/./pwrsave-udev' -> '/usr/local/bin/./pwrsave-udev'
+ rm -r /tmp/unpack
+ '[' udev == rclocal ']'
+ '[' udev == service ']'
+ '[' udev == udev ']'
+ echo_green 'Installing WiFi Power Management udev rule ...'
+ echo -e -n '\e[92m'
[92m+ echo Installing WiFi Power Management udev rule ...
Installing WiFi Power Management udev rule ...
+ echo -e -n '\e[0m'
[0m+ unpack filesystem/etc/udev/rules.d /etc/udev/rules.d root
+ from=filesystem/etc/udev/rules.d
+ to=/etc/udev/rules.d
+ owner=
+ '[' 3 -gt 2 ']'
+ owner=root
+ mkdir -p /tmp/unpack/
+ cp -v -r --preserve=mode,timestamps filesystem/etc/udev/rules.d/. /tmp/unpack/
'filesystem/etc/udev/rules.d/./070-wifi-powersave.rules' -> '/tmp/unpack/./070-wifi-powersave.rules'
+ '[' -n root ']'
+ chown -hR root:root /tmp/unpack/
+ cp -v -r --preserve=mode,ownership,timestamps /tmp/unpack/. /etc/udev/rules.d
'/tmp/unpack/./070-wifi-powersave.rules' -> '/etc/udev/rules.d/./070-wifi-powersave.rules'
+ rm -r /tmp/unpack
+ '[' udev '!=' udev ']'
+ rm -f /usr/local/bin/pwrsave
+ '[' -d custompios_export ']'
+ rm chroot_script
+ '[' -d filesystem ']'
+ rm -rfv filesystem
removed 'filesystem/etc/udev/rules.d/070-wifi-powersave.rules'
removed directory 'filesystem/etc/udev/rules.d'
removed directory 'filesystem/etc/udev'
removed 'filesystem/etc/systemd/system/disable-wifi-pwr-mgmt.service'
removed directory 'filesystem/etc/systemd/system'
removed directory 'filesystem/etc/systemd'
removed directory 'filesystem/etc'
removed 'filesystem/wpa-supplicant/boot/custompios-wpa-supplicant.txt'
removed directory 'filesystem/wpa-supplicant/boot'
removed directory 'filesystem/wpa-supplicant'
removed 'filesystem/network-manager/root/etc/systemd/system/copy-network-manager-config@.service'
removed directory 'filesystem/network-manager/root/etc/systemd/system'
removed directory 'filesystem/network-manager/root/etc/systemd'
removed directory 'filesystem/network-manager/root/etc'
removed 'filesystem/network-manager/root/opt/custompios/copy-network-manager-config'
removed directory 'filesystem/network-manager/root/opt/custompios'
removed directory 'filesystem/network-manager/root/opt'
removed directory 'filesystem/network-manager/root'
removed 'filesystem/network-manager/boot/wifi.nmconnection'
removed directory 'filesystem/network-manager/boot'
removed directory 'filesystem/network-manager'
removed 'filesystem/usr/local/bin/pwrsave-udev'
removed 'filesystem/usr/local/bin/pwrsave'
removed directory 'filesystem/usr/local/bin'
removed directory 'filesystem/usr/local'
removed directory 'filesystem/usr'
removed directory 'filesystem'
+ execute_chroot_script /distro/modules/ridgeweather /distro/modules/ridgeweather/start_chroot_script
+ '[' -f /.dockerenv ']'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
++ uname -m
+ '[' aarch64 '!=' aarch64 ']'
+ '[' -d /distro/modules/ridgeweather/filesystem ']'
+ cp -vr --preserve=mode,timestamps /distro/modules/ridgeweather/filesystem .
'/distro/modules/ridgeweather/filesystem' -> './filesystem'
'/distro/modules/ridgeweather/filesystem/root' -> './filesystem/root'
'/distro/modules/ridgeweather/filesystem/root/opt' -> './filesystem/root/opt'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather' -> './filesystem/root/opt/ridgeweather'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web' -> './filesystem/root/opt/ridgeweather/web'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates' -> './filesystem/root/opt/ridgeweather/web/templates'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/index.html' -> './filesystem/root/opt/ridgeweather/web/templates/index.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/hotspot.html' -> './filesystem/root/opt/ridgeweather/web/templates/hotspot.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/hotspot-restarting.html' -> './filesystem/root/opt/ridgeweather/web/templates/hotspot-restarting.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/containers.html' -> './filesystem/root/opt/ridgeweather/web/templates/containers.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/container_logs.html' -> './filesystem/root/opt/ridgeweather/web/templates/container_logs.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/container_env.html' -> './filesystem/root/opt/ridgeweather/web/templates/container_env.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/app.py' -> './filesystem/root/opt/ridgeweather/web/app.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/hotspot-app.py' -> './filesystem/root/opt/ridgeweather/web/hotspot-app.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/wifi_utils.py' -> './filesystem/root/opt/ridgeweather/web/wifi_utils.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/fakedns.py' -> './filesystem/root/opt/ridgeweather/web/fakedns.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/docker_manager.py' -> './filesystem/root/opt/ridgeweather/web/docker_manager.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/scripts' -> './filesystem/root/opt/ridgeweather/scripts'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/scripts/net-or-hotspot.sh' -> './filesystem/root/opt/ridgeweather/scripts/net-or-hotspot.sh'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/scripts/setup-tailscale.sh' -> './filesystem/root/opt/ridgeweather/scripts/setup-tailscale.sh'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint' -> './filesystem/root/opt/ridgeweather/accesspoint'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint/hostapd.conf' -> './filesystem/root/opt/ridgeweather/accesspoint/hostapd.conf'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint/dhcpd.conf' -> './filesystem/root/opt/ridgeweather/accesspoint/dhcpd.conf'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint/isc-dhcp-server' -> './filesystem/root/opt/ridgeweather/accesspoint/isc-dhcp-server'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/tailscale-auth-key.example' -> './filesystem/root/opt/ridgeweather/tailscale-auth-key.example'
'/distro/modules/ridgeweather/filesystem/root/etc' -> './filesystem/root/etc'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd' -> './filesystem/root/etc/systemd'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system' -> './filesystem/root/etc/systemd/system'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system/ridgeweather-web.service' -> './filesystem/root/etc/systemd/system/ridgeweather-web.service'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system/ridgeweather-hotspot.service' -> './filesystem/root/etc/systemd/system/ridgeweather-hotspot.service'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system/ridgeweather-tailscale.service' -> './filesystem/root/etc/systemd/system/ridgeweather-tailscale.service'
'/distro/modules/ridgeweather/filesystem/root/boot' -> './filesystem/root/boot'
'/distro/modules/ridgeweather/filesystem/root/boot/RIDGEWEATHER-README.txt' -> './filesystem/root/boot/RIDGEWEATHER-README.txt'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
+ '[' arm64 == armv7l ']'
+ '[' arm64 == armhf ']'
+ '[' arm64 == aarch64 ']'
+ '[' arm64 == arm64 ']'
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ cp /distro/modules/ridgeweather/start_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
++ uname -m
+ chroot_correct_qemu aarch64 arm64 /distro/modules/ridgeweather/start_chroot_script /CustomPiOS
+ local host_arch=aarch64
+ local target_arch=arm64
+ local chroot_script=/distro/modules/ridgeweather/start_chroot_script
+ local custom_pi_os_path=/CustomPiOS
+ [[ -z aarch64 ]]
+ [[ -z arm64 ]]
+ cp /distro/modules/ridgeweather/start_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ [[ arm64 == \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\6\4 ]]
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ aarch64 != \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ echo 'Building on ARM device a armv7l/aarch64/arm64 system, not using qemu'
Building on ARM device a armv7l/aarch64/arm64 system, not using qemu
+ chroot . /bin/bash /chroot_script
+ set -e
+ source /common.sh
+ install_cleanup_trap
+ set -e
+ trap cleanup SIGINT SIGTERM
+ apt-get update
Get:1 http://deb.debian.org/debian bookworm InRelease [151 kB]
Get:2 http://deb.debian.org/debian-security bookworm-security InRelease [48.0 kB]
Get:3 http://deb.debian.org/debian bookworm-updates InRelease [55.4 kB]
Get:4 http://deb.debian.org/debian bookworm/main arm64 Packages [8693 kB]
Get:5 http://archive.raspberrypi.com/debian bookworm InRelease [55.0 kB]
Get:6 http://archive.raspberrypi.com/debian bookworm/main armhf Packages [548 kB]
Get:7 http://deb.debian.org/debian bookworm/main armhf Packages [8508 kB]
Get:8 http://archive.raspberrypi.com/debian bookworm/main arm64 Packages [547 kB]
Get:9 http://deb.debian.org/debian bookworm/main Translation-en [6109 kB]
Get:10 http://deb.debian.org/debian bookworm/contrib arm64 Packages [45.7 kB]
Get:11 http://deb.debian.org/debian bookworm/contrib Translation-en [48.4 kB]
Get:12 http://deb.debian.org/debian bookworm/non-free armhf Packages [55.9 kB]
Get:13 http://deb.debian.org/debian bookworm/non-free arm64 Packages [75.8 kB]
Get:14 http://deb.debian.org/debian bookworm/non-free Translation-en [68.1 kB]
Get:15 http://deb.debian.org/debian bookworm/non-free-firmware arm64 Packages [5832 B]
Get:16 http://deb.debian.org/debian bookworm/non-free-firmware Translation-en [20.9 kB]
Get:17 http://deb.debian.org/debian-security bookworm-security/main armhf Packages [253 kB]
Get:18 http://deb.debian.org/debian-security bookworm-security/main arm64 Packages [268 kB]
Get:19 http://deb.debian.org/debian-security bookworm-security/main Translation-en [163 kB]
Get:20 http://deb.debian.org/debian bookworm-updates/main arm64 Packages [756 B]
Get:21 http://deb.debian.org/debian bookworm-updates/main armhf Packages [756 B]
Get:22 http://deb.debian.org/debian bookworm-updates/main Translation-en [664 B]
Fetched 25.7 MB in 4s (5806 kB/s)
Reading package lists...
+ apt-get install -y python3-pip python3-flask python3-jinja2 python3-yaml hostapd isc-dhcp-server dnsmasq wireless-tools wpasupplicant avahi-daemon avahi-utils net-tools iproute2 iputils-ping curl gnupg lsb-release jq docker.io docker-compose
Reading package lists...
Building dependency tree...
Reading state information...
wireless-tools is already the newest version (30~pre9-14).
wpasupplicant is already the newest version (2:2.10-12+deb12u2).
avahi-daemon is already the newest version (0.8-10+deb12u1).
iproute2 is already the newest version (6.1.0-3).
iputils-ping is already the newest version (3:20221126-1+deb12u1).
curl is already the newest version (7.88.1-10+deb12u12).
gnupg is already the newest version (2.2.40-1.1).
gnupg set to manually installed.
lsb-release is already the newest version (12.0-1).
lsb-release set to manually installed.
The following additional packages will be installed:
  cgroupfs-mount containerd criu git git-man javascript-common
  libavahi-client3 liberror-perl libexpat1-dev libintl-perl libintl-xs-perl
  libjq1 libjs-jquery libjs-sphinxdoc libjs-underscore libmodule-find-perl
  libnet1 libonig5 libproc-processtable-perl libprotobuf-c1 libprotobuf32
  libpython3-dev libpython3.11 libpython3.11-dev libpython3.11-minimal
  libpython3.11-stdlib libsort-naturally-perl libterm-readkey-perl needrestart
  policycoreutils python-babel-localedata python3-asgiref python3-attr
  python3-babel python3-blinker python3-cffi-backend python3-click
  python3-colorama python3-cryptography python3-dev python3-docker
  python3-dockerpty python3-docopt python3-dotenv python3-itsdangerous
  python3-json-pointer python3-jsonschema python3-markupsafe python3-openssl
  python3-protobuf python3-pyinotify python3-pyrsistent python3-rfc3987
  python3-setuptools python3-simplejson python3-texttable python3-tz
  python3-uritemplate python3-webcolors python3-websocket python3-werkzeug
  python3-wheel python3.11 python3.11-dev python3.11-minimal python3.11-venv
  runc selinux-utils tini zlib1g-dev
Suggested packages:
  containernetworking-plugins resolvconf docker-doc aufs-tools btrfs-progs
  debootstrap rinse rootlesskit xfsprogs zfs-fuse | zfsutils-linux
  git-daemon-run | git-daemon-sysvinit git-doc git-email git-gui gitk gitweb
  git-cvs git-mediawiki git-svn isc-dhcp-server-ldap ieee-data apache2
  | lighttpd | httpd needrestart-session | libnotify-bin iucode-tool
  python-attr-doc python-blinker-doc python-cryptography-doc
  python3-cryptography-vectors python-flask-doc python-jinja2-doc
  python-jsonschema-doc python-openssl-doc python3-openssl-dbg
  python-pyinotify-doc python-setuptools-doc ipython3 python-werkzeug-doc
  python3-lxml python3-watchdog python3.11-doc binfmt-support
The following NEW packages will be installed:
  avahi-utils cgroupfs-mount containerd criu dnsmasq docker-compose docker.io
  git git-man hostapd isc-dhcp-server javascript-common jq libavahi-client3
  liberror-perl libexpat1-dev libintl-perl libintl-xs-perl libjq1 libjs-jquery
  libjs-sphinxdoc libjs-underscore libmodule-find-perl libnet1 libonig5
  libproc-processtable-perl libprotobuf-c1 libprotobuf32 libpython3-dev
  libpython3.11-dev libsort-naturally-perl libterm-readkey-perl needrestart
  policycoreutils python-babel-localedata python3-asgiref python3-attr
  python3-babel python3-blinker python3-cffi-backend python3-click
  python3-colorama python3-cryptography python3-dev python3-docker
  python3-dockerpty python3-docopt python3-dotenv python3-flask
  python3-itsdangerous python3-jinja2 python3-json-pointer python3-jsonschema
  python3-markupsafe python3-openssl python3-pip python3-protobuf
  python3-pyinotify python3-pyrsistent python3-rfc3987 python3-setuptools
  python3-simplejson python3-texttable python3-tz python3-uritemplate
  python3-webcolors python3-websocket python3-werkzeug python3-wheel
  python3-yaml python3.11-dev runc selinux-utils tini zlib1g-dev
The following packages will be upgraded:
  libpython3.11 libpython3.11-minimal libpython3.11-stdlib net-tools
  python3.11 python3.11-minimal python3.11-venv
7 upgraded, 75 newly installed, 0 to remove and 86 not upgraded.
Need to get 83.8 MB of archives.
After this operation, 366 MB of additional disk space will be used.
Get:1 http://deb.debian.org/debian bookworm/main arm64 dnsmasq all 2.90-4~deb12u1 [66.1 kB]
Get:2 http://deb.debian.org/debian bookworm/main arm64 runc arm64 1.1.5+ds1-1+deb12u1 [2337 kB]
Get:3 http://archive.raspberrypi.com/debian bookworm/main arm64 zlib1g-dev arm64 1:1.2.13.dfsg-1+rpt1 [944 kB]
Get:4 http://deb.debian.org/debian bookworm/main arm64 containerd arm64 1.6.20~ds1-1+deb12u1 [15.4 MB]
Get:5 http://archive.raspberrypi.com/debian bookworm/main arm64 python3-pip all 23.0.1+dfsg-1+rpt1 [1325 kB]
Get:6 http://deb.debian.org/debian bookworm/main arm64 tini arm64 0.19.0-1 [209 kB]
Get:7 http://deb.debian.org/debian bookworm/main arm64 docker.io arm64 20.10.24+dfsg1-1+deb12u1+b1 [27.5 MB]
Get:8 http://deb.debian.org/debian bookworm/main arm64 hostapd arm64 2:2.10-12+deb12u2 [799 kB]
Get:9 http://deb.debian.org/debian bookworm/main arm64 python3.11-venv arm64 3.11.2-6+deb12u6 [5896 B]
Get:10 http://deb.debian.org/debian bookworm/main arm64 libpython3.11 arm64 3.11.2-6+deb12u6 [1841 kB]
Get:11 http://deb.debian.org/debian bookworm/main arm64 python3.11 arm64 3.11.2-6+deb12u6 [573 kB]
Get:12 http://deb.debian.org/debian bookworm/main arm64 libpython3.11-stdlib arm64 3.11.2-6+deb12u6 [1746 kB]
Get:13 http://deb.debian.org/debian bookworm/main arm64 python3.11-minimal arm64 3.11.2-6+deb12u6 [1859 kB]
Get:14 http://deb.debian.org/debian bookworm/main arm64 libpython3.11-minimal arm64 3.11.2-6+deb12u6 [810 kB]
Get:15 http://deb.debian.org/debian bookworm/main arm64 libavahi-client3 arm64 0.8-10+deb12u1 [44.3 kB]
Get:16 http://deb.debian.org/debian bookworm/main arm64 avahi-utils arm64 0.8-10+deb12u1 [43.5 kB]
Get:17 http://deb.debian.org/debian bookworm/main arm64 cgroupfs-mount all 1.4 [6276 B]
Get:18 http://deb.debian.org/debian bookworm/main arm64 libprotobuf32 arm64 3.21.12-3 [821 kB]
Get:19 http://deb.debian.org/debian bookworm/main arm64 python3-protobuf arm64 3.21.12-3 [236 kB]
Get:20 http://deb.debian.org/debian bookworm/main arm64 libnet1 arm64 1.1.6+dfsg-3.2 [60.7 kB]
Get:21 http://deb.debian.org/debian bookworm/main arm64 libprotobuf-c1 arm64 1.4.1-1+b1 [26.6 kB]
Get:22 http://deb.debian.org/debian bookworm/main arm64 criu arm64 3.17.1-2+deb12u1 [607 kB]
Get:23 http://deb.debian.org/debian bookworm/main arm64 python3-websocket all 1.2.3-1 [40.4 kB]
Get:24 http://deb.debian.org/debian bookworm/main arm64 python3-docker all 5.0.3-1 [90.2 kB]
Get:25 http://deb.debian.org/debian bookworm/main arm64 python3-dockerpty all 0.4.1-4 [11.4 kB]
Get:26 http://deb.debian.org/debian bookworm/main arm64 python3-docopt all 0.6.2-4.1 [26.2 kB]
Get:27 http://deb.debian.org/debian bookworm/main arm64 python3-dotenv all 0.21.0-1 [24.9 kB]
Get:28 http://deb.debian.org/debian bookworm/main arm64 python3-attr all 22.2.0-1 [65.4 kB]
Get:29 http://deb.debian.org/debian bookworm/main arm64 python3-pyrsistent arm64 0.18.1-1+b3 [59.4 kB]
Get:30 http://deb.debian.org/debian bookworm/main arm64 python3-jsonschema all 4.10.3-1 [67.9 kB]
Get:31 http://deb.debian.org/debian bookworm/main arm64 python3-texttable all 1.6.7-1 [11.9 kB]
Get:32 http://deb.debian.org/debian bookworm/main arm64 python3-yaml arm64 6.0-3+b2 [108 kB]
Get:33 http://deb.debian.org/debian bookworm/main arm64 docker-compose all 1.29.2-3 [123 kB]
Get:34 http://deb.debian.org/debian bookworm/main arm64 liberror-perl all 0.17029-2 [29.0 kB]
Get:35 http://deb.debian.org/debian bookworm/main arm64 git-man all 1:2.39.5-0+deb12u2 [2053 kB]
Get:36 http://deb.debian.org/debian bookworm/main arm64 git arm64 1:2.39.5-0+deb12u2 [7148 kB]
Get:37 http://deb.debian.org/debian bookworm/main arm64 isc-dhcp-server arm64 4.4.3-P1-2 [1356 kB]
Get:38 http://deb.debian.org/debian bookworm/main arm64 javascript-common all 11+nmu1 [6260 B]
Get:39 http://deb.debian.org/debian bookworm/main arm64 libonig5 arm64 6.9.8-1 [179 kB]
Get:40 http://deb.debian.org/debian bookworm/main arm64 libjq1 arm64 1.6-2.1 [121 kB]
Get:41 http://deb.debian.org/debian bookworm/main arm64 jq arm64 1.6-2.1 [64.5 kB]
Get:42 http://deb.debian.org/debian bookworm/main arm64 libexpat1-dev arm64 2.5.0-1+deb12u1 [134 kB]
Get:43 http://deb.debian.org/debian bookworm/main arm64 libintl-perl all 1.33-1 [720 kB]
Get:44 http://deb.debian.org/debian bookworm/main arm64 libintl-xs-perl arm64 1.33-1 [15.2 kB]
Get:45 http://deb.debian.org/debian bookworm/main arm64 libjs-jquery all 3.6.1+dfsg+~3.5.14-1 [326 kB]
Get:46 http://deb.debian.org/debian bookworm/main arm64 libjs-underscore all 1.13.4~dfsg+~1.11.4-3 [116 kB]
Get:47 http://deb.debian.org/debian bookworm/main arm64 libjs-sphinxdoc all 5.3.0-4 [130 kB]
Get:48 http://deb.debian.org/debian bookworm/main arm64 libmodule-find-perl all 0.16-2 [10.6 kB]
Get:49 http://deb.debian.org/debian bookworm/main arm64 libproc-processtable-perl arm64 0.634-1+b2 [42.4 kB]
Get:50 http://deb.debian.org/debian bookworm/main arm64 libpython3.11-dev arm64 3.11.2-6+deb12u6 [4390 kB]
Get:51 http://deb.debian.org/debian bookworm/main arm64 libpython3-dev arm64 3.11.2-1+b1 [9564 B]
Get:52 http://deb.debian.org/debian bookworm/main arm64 libsort-naturally-perl all 1.03-4 [13.1 kB]
Get:53 http://deb.debian.org/debian bookworm/main arm64 libterm-readkey-perl arm64 2.38-2+b1 [24.1 kB]
Get:54 http://deb.debian.org/debian bookworm/main arm64 needrestart all 3.6-4+deb12u3 [60.5 kB]
Get:55 http://deb.debian.org/debian-security bookworm-security/main arm64 net-tools arm64 2.10-0.1+deb12u2 [242 kB]
Get:56 http://deb.debian.org/debian bookworm/main arm64 selinux-utils arm64 3.4-1+b6 [124 kB]
Get:57 http://deb.debian.org/debian bookworm/main arm64 policycoreutils arm64 3.4-1 [160 kB]
Get:58 http://deb.debian.org/debian bookworm/main arm64 python-babel-localedata all 2.10.3-1 [5615 kB]
Get:59 http://deb.debian.org/debian bookworm/main arm64 python3-asgiref all 3.6.0-1 [27.6 kB]
Get:60 http://deb.debian.org/debian bookworm/main arm64 python3-tz all 2022.7.1-4 [30.1 kB]
Get:61 http://deb.debian.org/debian bookworm/main arm64 python3-babel all 2.10.3-1 [103 kB]
Get:62 http://deb.debian.org/debian bookworm/main arm64 python3-blinker all 1.5-1 [14.7 kB]
Get:63 http://deb.debian.org/debian bookworm/main arm64 python3-cffi-backend arm64 1.15.1-5+b1 [84.4 kB]
Get:64 http://deb.debian.org/debian bookworm/main arm64 python3-colorama all 0.4.6-2 [36.8 kB]
Get:65 http://deb.debian.org/debian bookworm/main arm64 python3-click all 8.1.3-2 [92.2 kB]
Get:66 http://deb.debian.org/debian bookworm/main arm64 python3-cryptography arm64 38.0.4-3+deb12u1 [590 kB]
Get:67 http://deb.debian.org/debian bookworm/main arm64 python3.11-dev arm64 3.11.2-6+deb12u6 [617 kB]
Get:68 http://deb.debian.org/debian bookworm/main arm64 python3-dev arm64 3.11.2-1+b1 [26.2 kB]
Get:69 http://deb.debian.org/debian bookworm/main arm64 python3-itsdangerous all 2.1.2-3 [17.0 kB]
Get:70 http://deb.debian.org/debian bookworm/main arm64 python3-markupsafe arm64 2.1.2-1+b1 [13.3 kB]
Get:71 http://deb.debian.org/debian bookworm/main arm64 python3-jinja2 all 3.1.2-1+deb12u2 [120 kB]
Get:72 http://deb.debian.org/debian bookworm/main arm64 python3-werkzeug all 2.2.2-3+deb12u1 [206 kB]
Get:73 http://deb.debian.org/debian bookworm/main arm64 python3-flask all 2.2.2-3 [105 kB]
Get:74 http://deb.debian.org/debian bookworm/main arm64 python3-json-pointer all 2.3-2 [15.1 kB]
Get:75 http://deb.debian.org/debian bookworm/main arm64 python3-openssl all 23.0.0-1 [50.8 kB]
Get:76 http://deb.debian.org/debian bookworm/main arm64 python3-setuptools all 66.1.1-1+deb12u1 [522 kB]
Get:77 http://deb.debian.org/debian bookworm/main arm64 python3-wheel all 0.38.4-2 [30.8 kB]
Get:78 http://deb.debian.org/debian bookworm/main arm64 python3-pyinotify all 0.9.6-2 [27.4 kB]
Get:79 http://deb.debian.org/debian bookworm/main arm64 python3-rfc3987 all 1.3.8-2 [8816 B]
Get:80 http://deb.debian.org/debian bookworm/main arm64 python3-simplejson arm64 3.18.3-1 [58.4 kB]
Get:81 http://deb.debian.org/debian bookworm/main arm64 python3-uritemplate all 4.1.1-2 [10.9 kB]
Get:82 http://deb.debian.org/debian bookworm/main arm64 python3-webcolors all 1.11.1-1 [12.7 kB]
apt-listchanges: Reading changelogs...
debconf: unable to initialize frontend: Dialog
debconf: (TERM is not set, so the dialog frontend is not usable.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Teletype
dpkg-preconfigure: unable to re-open stdin: 
Fetched 83.8 MB in 16s (5406 kB/s)
Selecting previously unselected package dnsmasq.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 56386 files and directories currently installed.)
Preparing to unpack .../00-dnsmasq_2.90-4~deb12u1_all.deb ...
Unpacking dnsmasq (2.90-4~deb12u1) ...
Selecting previously unselected package runc.
Preparing to unpack .../01-runc_1.1.5+ds1-1+deb12u1_arm64.deb ...
Unpacking runc (1.1.5+ds1-1+deb12u1) ...
Selecting previously unselected package containerd.
Preparing to unpack .../02-containerd_1.6.20~ds1-1+deb12u1_arm64.deb ...
Unpacking containerd (1.6.20~ds1-1+deb12u1) ...
Selecting previously unselected package tini.
Preparing to unpack .../03-tini_0.19.0-1_arm64.deb ...
Unpacking tini (0.19.0-1) ...
Selecting previously unselected package docker.io.
Preparing to unpack .../04-docker.io_20.10.24+dfsg1-1+deb12u1+b1_arm64.deb ...
Unpacking docker.io (20.10.24+dfsg1-1+deb12u1+b1) ...
Selecting previously unselected package hostapd.
Preparing to unpack .../05-hostapd_2%3a2.10-12+deb12u2_arm64.deb ...
Unpacking hostapd (2:2.10-12+deb12u2) ...
Preparing to unpack .../06-python3.11-venv_3.11.2-6+deb12u6_arm64.deb ...
Unpacking python3.11-venv (3.11.2-6+deb12u6) over (3.11.2-6+deb12u5) ...
Preparing to unpack .../07-libpython3.11_3.11.2-6+deb12u6_arm64.deb ...
Unpacking libpython3.11:arm64 (3.11.2-6+deb12u6) over (3.11.2-6+deb12u5) ...
Preparing to unpack .../08-python3.11_3.11.2-6+deb12u6_arm64.deb ...
Unpacking python3.11 (3.11.2-6+deb12u6) over (3.11.2-6+deb12u5) ...
Preparing to unpack .../09-libpython3.11-stdlib_3.11.2-6+deb12u6_arm64.deb ...
Unpacking libpython3.11-stdlib:arm64 (3.11.2-6+deb12u6) over (3.11.2-6+deb12u5) ...
Preparing to unpack .../10-python3.11-minimal_3.11.2-6+deb12u6_arm64.deb ...
Unpacking python3.11-minimal (3.11.2-6+deb12u6) over (3.11.2-6+deb12u5) ...
Preparing to unpack .../11-libpython3.11-minimal_3.11.2-6+deb12u6_arm64.deb ...
Unpacking libpython3.11-minimal:arm64 (3.11.2-6+deb12u6) over (3.11.2-6+deb12u5) ...
Selecting previously unselected package libavahi-client3:arm64.
Preparing to unpack .../12-libavahi-client3_0.8-10+deb12u1_arm64.deb ...
Unpacking libavahi-client3:arm64 (0.8-10+deb12u1) ...
Selecting previously unselected package avahi-utils.
Preparing to unpack .../13-avahi-utils_0.8-10+deb12u1_arm64.deb ...
Unpacking avahi-utils (0.8-10+deb12u1) ...
Selecting previously unselected package cgroupfs-mount.
Preparing to unpack .../14-cgroupfs-mount_1.4_all.deb ...
Unpacking cgroupfs-mount (1.4) ...
Selecting previously unselected package libprotobuf32:arm64.
Preparing to unpack .../15-libprotobuf32_3.21.12-3_arm64.deb ...
Unpacking libprotobuf32:arm64 (3.21.12-3) ...
Selecting previously unselected package python3-protobuf.
Preparing to unpack .../16-python3-protobuf_3.21.12-3_arm64.deb ...
Unpacking python3-protobuf (3.21.12-3) ...
Selecting previously unselected package libnet1:arm64.
Preparing to unpack .../17-libnet1_1.1.6+dfsg-3.2_arm64.deb ...
Unpacking libnet1:arm64 (1.1.6+dfsg-3.2) ...
Selecting previously unselected package libprotobuf-c1:arm64.
Preparing to unpack .../18-libprotobuf-c1_1.4.1-1+b1_arm64.deb ...
Unpacking libprotobuf-c1:arm64 (1.4.1-1+b1) ...
Selecting previously unselected package criu.
Preparing to unpack .../19-criu_3.17.1-2+deb12u1_arm64.deb ...
Unpacking criu (3.17.1-2+deb12u1) ...
Selecting previously unselected package python3-websocket.
Preparing to unpack .../20-python3-websocket_1.2.3-1_all.deb ...
Unpacking python3-websocket (1.2.3-1) ...
Selecting previously unselected package python3-docker.
Preparing to unpack .../21-python3-docker_5.0.3-1_all.deb ...
Unpacking python3-docker (5.0.3-1) ...
Selecting previously unselected package python3-dockerpty.
Preparing to unpack .../22-python3-dockerpty_0.4.1-4_all.deb ...
Unpacking python3-dockerpty (0.4.1-4) ...
Selecting previously unselected package python3-docopt.
Preparing to unpack .../23-python3-docopt_0.6.2-4.1_all.deb ...
Unpacking python3-docopt (0.6.2-4.1) ...
Selecting previously unselected package python3-dotenv.
Preparing to unpack .../24-python3-dotenv_0.21.0-1_all.deb ...
Unpacking python3-dotenv (0.21.0-1) ...
Selecting previously unselected package python3-attr.
Preparing to unpack .../25-python3-attr_22.2.0-1_all.deb ...
Unpacking python3-attr (22.2.0-1) ...
Selecting previously unselected package python3-pyrsistent:arm64.
Preparing to unpack .../26-python3-pyrsistent_0.18.1-1+b3_arm64.deb ...
Unpacking python3-pyrsistent:arm64 (0.18.1-1+b3) ...
Selecting previously unselected package python3-jsonschema.
Preparing to unpack .../27-python3-jsonschema_4.10.3-1_all.deb ...
Unpacking python3-jsonschema (4.10.3-1) ...
Selecting previously unselected package python3-texttable.
Preparing to unpack .../28-python3-texttable_1.6.7-1_all.deb ...
Unpacking python3-texttable (1.6.7-1) ...
Selecting previously unselected package python3-yaml.
Preparing to unpack .../29-python3-yaml_6.0-3+b2_arm64.deb ...
Unpacking python3-yaml (6.0-3+b2) ...
Selecting previously unselected package docker-compose.
Preparing to unpack .../30-docker-compose_1.29.2-3_all.deb ...
Unpacking docker-compose (1.29.2-3) ...
Selecting previously unselected package liberror-perl.
Preparing to unpack .../31-liberror-perl_0.17029-2_all.deb ...
Unpacking liberror-perl (0.17029-2) ...
Selecting previously unselected package git-man.
Preparing to unpack .../32-git-man_1%3a2.39.5-0+deb12u2_all.deb ...
Unpacking git-man (1:2.39.5-0+deb12u2) ...
Selecting previously unselected package git.
Preparing to unpack .../33-git_1%3a2.39.5-0+deb12u2_arm64.deb ...
Unpacking git (1:2.39.5-0+deb12u2) ...
Selecting previously unselected package isc-dhcp-server.
Preparing to unpack .../34-isc-dhcp-server_4.4.3-P1-2_arm64.deb ...
Unpacking isc-dhcp-server (4.4.3-P1-2) ...
Selecting previously unselected package javascript-common.
Preparing to unpack .../35-javascript-common_11+nmu1_all.deb ...
Unpacking javascript-common (11+nmu1) ...
Selecting previously unselected package libonig5:arm64.
Preparing to unpack .../36-libonig5_6.9.8-1_arm64.deb ...
Unpacking libonig5:arm64 (6.9.8-1) ...
Selecting previously unselected package libjq1:arm64.
Preparing to unpack .../37-libjq1_1.6-2.1_arm64.deb ...
Unpacking libjq1:arm64 (1.6-2.1) ...
Selecting previously unselected package jq.
Preparing to unpack .../38-jq_1.6-2.1_arm64.deb ...
Unpacking jq (1.6-2.1) ...
Selecting previously unselected package libexpat1-dev:arm64.
Preparing to unpack .../39-libexpat1-dev_2.5.0-1+deb12u1_arm64.deb ...
Unpacking libexpat1-dev:arm64 (2.5.0-1+deb12u1) ...
Selecting previously unselected package libintl-perl.
Preparing to unpack .../40-libintl-perl_1.33-1_all.deb ...
Unpacking libintl-perl (1.33-1) ...
Selecting previously unselected package libintl-xs-perl.
Preparing to unpack .../41-libintl-xs-perl_1.33-1_arm64.deb ...
Unpacking libintl-xs-perl (1.33-1) ...
Selecting previously unselected package libjs-jquery.
Preparing to unpack .../42-libjs-jquery_3.6.1+dfsg+~3.5.14-1_all.deb ...
Unpacking libjs-jquery (3.6.1+dfsg+~3.5.14-1) ...
Selecting previously unselected package libjs-underscore.
Preparing to unpack .../43-libjs-underscore_1.13.4~dfsg+~1.11.4-3_all.deb ...
Unpacking libjs-underscore (1.13.4~dfsg+~1.11.4-3) ...
Selecting previously unselected package libjs-sphinxdoc.
Preparing to unpack .../44-libjs-sphinxdoc_5.3.0-4_all.deb ...
Unpacking libjs-sphinxdoc (5.3.0-4) ...
Selecting previously unselected package libmodule-find-perl.
Preparing to unpack .../45-libmodule-find-perl_0.16-2_all.deb ...
Unpacking libmodule-find-perl (0.16-2) ...
Selecting previously unselected package libproc-processtable-perl:arm64.
Preparing to unpack .../46-libproc-processtable-perl_0.634-1+b2_arm64.deb ...
Unpacking libproc-processtable-perl:arm64 (0.634-1+b2) ...
Selecting previously unselected package zlib1g-dev:arm64.
Preparing to unpack .../47-zlib1g-dev_1%3a1.2.13.dfsg-1+rpt1_arm64.deb ...
Unpacking zlib1g-dev:arm64 (1:1.2.13.dfsg-1+rpt1) ...
Selecting previously unselected package libpython3.11-dev:arm64.
Preparing to unpack .../48-libpython3.11-dev_3.11.2-6+deb12u6_arm64.deb ...
Unpacking libpython3.11-dev:arm64 (3.11.2-6+deb12u6) ...
Selecting previously unselected package libpython3-dev:arm64.
Preparing to unpack .../49-libpython3-dev_3.11.2-1+b1_arm64.deb ...
Unpacking libpython3-dev:arm64 (3.11.2-1+b1) ...
Selecting previously unselected package libsort-naturally-perl.
Preparing to unpack .../50-libsort-naturally-perl_1.03-4_all.deb ...
Unpacking libsort-naturally-perl (1.03-4) ...
Selecting previously unselected package libterm-readkey-perl.
Preparing to unpack .../51-libterm-readkey-perl_2.38-2+b1_arm64.deb ...
Unpacking libterm-readkey-perl (2.38-2+b1) ...
Selecting previously unselected package needrestart.
Preparing to unpack .../52-needrestart_3.6-4+deb12u3_all.deb ...
Unpacking needrestart (3.6-4+deb12u3) ...
Preparing to unpack .../53-net-tools_2.10-0.1+deb12u2_arm64.deb ...
Unpacking net-tools (2.10-0.1+deb12u2) over (2.10-0.1) ...
Selecting previously unselected package selinux-utils.
Preparing to unpack .../54-selinux-utils_3.4-1+b6_arm64.deb ...
Unpacking selinux-utils (3.4-1+b6) ...
Selecting previously unselected package policycoreutils.
Preparing to unpack .../55-policycoreutils_3.4-1_arm64.deb ...
Unpacking policycoreutils (3.4-1) ...
Selecting previously unselected package python-babel-localedata.
Preparing to unpack .../56-python-babel-localedata_2.10.3-1_all.deb ...
Unpacking python-babel-localedata (2.10.3-1) ...
Selecting previously unselected package python3-asgiref.
Preparing to unpack .../57-python3-asgiref_3.6.0-1_all.deb ...
Unpacking python3-asgiref (3.6.0-1) ...
Selecting previously unselected package python3-tz.
Preparing to unpack .../58-python3-tz_2022.7.1-4_all.deb ...
Unpacking python3-tz (2022.7.1-4) ...
Selecting previously unselected package python3-babel.
Preparing to unpack .../59-python3-babel_2.10.3-1_all.deb ...
Unpacking python3-babel (2.10.3-1) ...
Selecting previously unselected package python3-blinker.
Preparing to unpack .../60-python3-blinker_1.5-1_all.deb ...
Unpacking python3-blinker (1.5-1) ...
Selecting previously unselected package python3-cffi-backend:arm64.
Preparing to unpack .../61-python3-cffi-backend_1.15.1-5+b1_arm64.deb ...
Unpacking python3-cffi-backend:arm64 (1.15.1-5+b1) ...
Selecting previously unselected package python3-colorama.
Preparing to unpack .../62-python3-colorama_0.4.6-2_all.deb ...
Unpacking python3-colorama (0.4.6-2) ...
Selecting previously unselected package python3-click.
Preparing to unpack .../63-python3-click_8.1.3-2_all.deb ...
Unpacking python3-click (8.1.3-2) ...
Selecting previously unselected package python3-cryptography.
Preparing to unpack .../64-python3-cryptography_38.0.4-3+deb12u1_arm64.deb ...
Unpacking python3-cryptography (38.0.4-3+deb12u1) ...
Selecting previously unselected package python3.11-dev.
Preparing to unpack .../65-python3.11-dev_3.11.2-6+deb12u6_arm64.deb ...
Unpacking python3.11-dev (3.11.2-6+deb12u6) ...
Selecting previously unselected package python3-dev.
Preparing to unpack .../66-python3-dev_3.11.2-1+b1_arm64.deb ...
Unpacking python3-dev (3.11.2-1+b1) ...
Selecting previously unselected package python3-itsdangerous.
Preparing to unpack .../67-python3-itsdangerous_2.1.2-3_all.deb ...
Unpacking python3-itsdangerous (2.1.2-3) ...
Selecting previously unselected package python3-markupsafe.
Preparing to unpack .../68-python3-markupsafe_2.1.2-1+b1_arm64.deb ...
Unpacking python3-markupsafe (2.1.2-1+b1) ...
Selecting previously unselected package python3-jinja2.
Preparing to unpack .../69-python3-jinja2_3.1.2-1+deb12u2_all.deb ...
Unpacking python3-jinja2 (3.1.2-1+deb12u2) ...
Selecting previously unselected package python3-werkzeug.
Preparing to unpack .../70-python3-werkzeug_2.2.2-3+deb12u1_all.deb ...
Unpacking python3-werkzeug (2.2.2-3+deb12u1) ...
Selecting previously unselected package python3-flask.
Preparing to unpack .../71-python3-flask_2.2.2-3_all.deb ...
Unpacking python3-flask (2.2.2-3) ...
Selecting previously unselected package python3-json-pointer.
Preparing to unpack .../72-python3-json-pointer_2.3-2_all.deb ...
Unpacking python3-json-pointer (2.3-2) ...
Selecting previously unselected package python3-openssl.
Preparing to unpack .../73-python3-openssl_23.0.0-1_all.deb ...
Unpacking python3-openssl (23.0.0-1) ...
Selecting previously unselected package python3-setuptools.
Preparing to unpack .../74-python3-setuptools_66.1.1-1+deb12u1_all.deb ...
Unpacking python3-setuptools (66.1.1-1+deb12u1) ...
Selecting previously unselected package python3-wheel.
Preparing to unpack .../75-python3-wheel_0.38.4-2_all.deb ...
Unpacking python3-wheel (0.38.4-2) ...
Selecting previously unselected package python3-pip.
Preparing to unpack .../76-python3-pip_23.0.1+dfsg-1+rpt1_all.deb ...
Unpacking python3-pip (23.0.1+dfsg-1+rpt1) ...
Selecting previously unselected package python3-pyinotify.
Preparing to unpack .../77-python3-pyinotify_0.9.6-2_all.deb ...
Unpacking python3-pyinotify (0.9.6-2) ...
Selecting previously unselected package python3-rfc3987.
Preparing to unpack .../78-python3-rfc3987_1.3.8-2_all.deb ...
Unpacking python3-rfc3987 (1.3.8-2) ...
Selecting previously unselected package python3-simplejson.
Preparing to unpack .../79-python3-simplejson_3.18.3-1_arm64.deb ...
Unpacking python3-simplejson (3.18.3-1) ...
Selecting previously unselected package python3-uritemplate.
Preparing to unpack .../80-python3-uritemplate_4.1.1-2_all.deb ...
Unpacking python3-uritemplate (4.1.1-2) ...
Selecting previously unselected package python3-webcolors.
Preparing to unpack .../81-python3-webcolors_1.11.1-1_all.deb ...
Unpacking python3-webcolors (1.11.1-1) ...
Setting up python3-dotenv (0.21.0-1) ...
Setting up selinux-utils (3.4-1+b6) ...
Setting up javascript-common (11+nmu1) ...
Setting up python3-attr (22.2.0-1) ...
Setting up python3-texttable (1.6.7-1) ...
Setting up net-tools (2.10-0.1+deb12u2) ...
Setting up python3-docopt (0.6.2-4.1) ...
Setting up python3-setuptools (66.1.1-1+deb12u1) ...
Setting up python3-colorama (0.4.6-2) ...
Setting up policycoreutils (3.4-1) ...
Setting up python3-pyinotify (0.9.6-2) ...
Setting up isc-dhcp-server (4.4.3-P1-2) ...
debconf: unable to initialize frontend: Dialog
debconf: (TERM is not set, so the dialog frontend is not usable.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Teletype
Generating /etc/default/isc-dhcp-server...
Running in chroot, ignoring request.

Setting up python3-itsdangerous (2.1.2-3) ...
Setting up python3-yaml (6.0-3+b2) ...
Setting up libnet1:arm64 (1.1.6+dfsg-3.2) ...
Setting up python3-click (8.1.3-2) ...
Setting up python3-markupsafe (2.1.2-1+b1) ...
Setting up libprotobuf-c1:arm64 (1.4.1-1+b1) ...
Setting up python3-wheel (0.38.4-2) ...
Setting up python3-tz (2022.7.1-4) ...
Setting up runc (1.1.5+ds1-1+deb12u1) ...
Setting up python-babel-localedata (2.10.3-1) ...
Setting up python3-uritemplate (4.1.1-2) ...
Setting up dnsmasq (2.90-4~deb12u1) ...
Running in chroot, ignoring request.

Created symlink /etc/systemd/system/multi-user.target.wants/dnsmasq.service -> /lib/systemd/system/dnsmasq.service.

Setting up liberror-perl (0.17029-2) ...
Setting up python3-simplejson (3.18.3-1) ...
Setting up python3-jinja2 (3.1.2-1+deb12u2) ...
Setting up python3-webcolors (1.11.1-1) ...
Setting up hostapd (2:2.10-12+deb12u2) ...
Running in chroot, ignoring request.

Created symlink /etc/systemd/system/multi-user.target.wants/hostapd.service -> /lib/systemd/system/hostapd.service.

Setting up libexpat1-dev:arm64 (2.5.0-1+deb12u1) ...
Setting up libmodule-find-perl (0.16-2) ...
Setting up python3-rfc3987 (1.3.8-2) ...
Setting up tini (0.19.0-1) ...
Setting up python3-pip (23.0.1+dfsg-1+rpt1) ...
Setting up zlib1g-dev:arm64 (1:1.2.13.dfsg-1+rpt1) ...
Setting up python3-pyrsistent:arm64 (0.18.1-1+b3) ...
Setting up libprotobuf32:arm64 (3.21.12-3) ...
Setting up libproc-processtable-perl:arm64 (0.634-1+b2) ...
Setting up python3-json-pointer (2.3-2) ...
Setting up libintl-perl (1.33-1) ...
Setting up git-man (1:2.39.5-0+deb12u2) ...
Setting up libpython3.11-minimal:arm64 (3.11.2-6+deb12u6) ...
Setting up cgroupfs-mount (1.4) ...
Running in chroot, ignoring request.

Setting up libjs-jquery (3.6.1+dfsg+~3.5.14-1) ...
Setting up libterm-readkey-perl (2.38-2+b1) ...
Setting up python3-protobuf (3.21.12-3) ...
Setting up containerd (1.6.20~ds1-1+deb12u1) ...
Created symlink /etc/systemd/system/multi-user.target.wants/containerd.service -> /lib/systemd/system/containerd.service.

Setting up libsort-naturally-perl (1.03-4) ...
Setting up python3-websocket (1.2.3-1) ...
Setting up libavahi-client3:arm64 (0.8-10+deb12u1) ...
Setting up libonig5:arm64 (6.9.8-1) ...
Setting up python3-asgiref (3.6.0-1) ...
Setting up python3-cffi-backend:arm64 (1.15.1-5+b1) ...
Setting up python3-dockerpty (0.4.1-4) ...
Setting up libjs-underscore (1.13.4~dfsg+~1.11.4-3) ...
Setting up python3-blinker (1.5-1) ...
Setting up python3.11-minimal (3.11.2-6+deb12u6) ...
Setting up needrestart (3.6-4+deb12u3) ...
Setting up python3-babel (2.10.3-1) ...
update-alternatives: using /usr/bin/pybabel-python3 to provide /usr/bin/pybabel (pybabel) in auto mode
Setting up libjq1:arm64 (1.6-2.1) ...
Setting up libpython3.11-stdlib:arm64 (3.11.2-6+deb12u6) ...
Setting up avahi-utils (0.8-10+deb12u1) ...
Setting up python3-docker (5.0.3-1) ...
Setting up docker.io (20.10.24+dfsg1-1+deb12u1+b1) ...
Adding group `docker' (GID 111) ...
Done.
Running in chroot, ignoring request.

Created symlink /etc/systemd/system/multi-user.target.wants/docker.service -> /lib/systemd/system/docker.service.

Created symlink /etc/systemd/system/sockets.target.wants/docker.socket -> /lib/systemd/system/docker.socket.

Setting up libintl-xs-perl (1.33-1) ...
Setting up python3-jsonschema (4.10.3-1) ...
Setting up python3-werkzeug (2.2.2-3+deb12u1) ...
Setting up python3-cryptography (38.0.4-3+deb12u1) ...
Setting up git (1:2.39.5-0+deb12u2) ...
Setting up libjs-sphinxdoc (5.3.0-4) ...
Setting up criu (3.17.1-2+deb12u1) ...
Setting up jq (1.6-2.1) ...
Setting up python3.11 (3.11.2-6+deb12u6) ...
Setting up docker-compose (1.29.2-3) ...
Setting up libpython3.11:arm64 (3.11.2-6+deb12u6) ...
Setting up python3-openssl (23.0.0-1) ...
Setting up python3-flask (2.2.2-3) ...
Setting up python3.11-venv (3.11.2-6+deb12u6) ...
Setting up libpython3.11-dev:arm64 (3.11.2-6+deb12u6) ...
Setting up libpython3-dev:arm64 (3.11.2-1+b1) ...
Setting up python3.11-dev (3.11.2-6+deb12u6) ...
Setting up python3-dev (3.11.2-1+b1) ...
Processing triggers for systemd (252.36-1~deb12u1) ...
Processing triggers for man-db (2.11.2-2) ...
Processing triggers for libc-bin (2.36-9+rpt2+deb12u10) ...
+ curl -fsSL https://pkgs.tailscale.com/stable/debian/bookworm.noarmor.gpg
+ gpg --dearmor -o /usr/share/keyrings/tailscale-archive-keyring.gpg
+ echo 'deb [signed-by=/usr/share/keyrings/tailscale-archive-keyring.gpg] https://pkgs.tailscale.com/stable/debian bookworm main'
+ tee /etc/apt/sources.list.d/tailscale.list
deb [signed-by=/usr/share/keyrings/tailscale-archive-keyring.gpg] https://pkgs.tailscale.com/stable/debian bookworm main
+ apt-get update
Hit:1 http://deb.debian.org/debian bookworm InRelease
Hit:2 http://deb.debian.org/debian-security bookworm-security InRelease
Hit:3 http://deb.debian.org/debian bookworm-updates InRelease
Hit:4 http://archive.raspberrypi.com/debian bookworm InRelease
Get:5 https://pkgs.tailscale.com/stable/debian bookworm InRelease
Get:6 https://pkgs.tailscale.com/stable/debian bookworm/main all Packages [354 B]
Get:7 https://pkgs.tailscale.com/stable/debian bookworm/main armhf Packages [12.4 kB]
Get:8 https://pkgs.tailscale.com/stable/debian bookworm/main arm64 Packages [12.5 kB]
Fetched 31.8 kB in 2s (18.8 kB/s)
Reading package lists...
+ apt-get install -y tailscale
Reading package lists...
Building dependency tree...
Reading state information...
The following additional packages will be installed:
  tailscale-archive-keyring
The following NEW packages will be installed:
  tailscale tailscale-archive-keyring
0 upgraded, 2 newly installed, 0 to remove and 86 not upgraded.
Need to get 30.6 MB of archives.
After this operation, 58.2 MB of additional disk space will be used.
Get:2 https://pkgs.tailscale.com/stable/debian bookworm/main all tailscale-archive-keyring all 1.35.181 [3082 B]
Get:1 https://pkgs.tailscale.com/stable/debian bookworm/main arm64 tailscale arm64 1.84.0 [30.6 MB]
debconf: unable to initialize frontend: Dialog
debconf: (TERM is not set, so the dialog frontend is not usable.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Teletype
dpkg-preconfigure: unable to re-open stdin: 
Fetched 30.6 MB in 8s (3918 kB/s)
Selecting previously unselected package tailscale.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 62108 files and directories currently installed.)
Preparing to unpack .../tailscale_1.84.0_arm64.deb ...
Unpacking tailscale (1.84.0) ...
Selecting previously unselected package tailscale-archive-keyring.
Preparing to unpack .../tailscale-archive-keyring_1.35.181_all.deb ...
Unpacking tailscale-archive-keyring (1.35.181) ...
Setting up tailscale-archive-keyring (1.35.181) ...
Setting up tailscale (1.84.0) ...
Created symlink /etc/systemd/system/multi-user.target.wants/tailscaled.service -> /lib/systemd/system/tailscaled.service.


Pending kernel upgrade!

Running kernel version:
  6.12.5-linuxkit

Diagnostics:
  The currently running kernel version is not the expected kernel version 6.12.25+rpt-rpi-v8.

Restarting the system to load the new kernel will not be handled automatically, so you should consider rebooting. [Return]

The processor microcode seems to be up-to-date.

No services need to be restarted.

No containers need to be restarted.

No user sessions are running outdated binaries.

No VM guests are running outdated hypervisor (qemu) binaries on this host.
+ unpack /filesystem/root /
+ from=/filesystem/root
+ to=/
+ owner=
+ '[' 2 -gt 2 ']'
+ mkdir -p /tmp/unpack/
+ cp -v -r --preserve=mode,timestamps /filesystem/root/. /tmp/unpack/
'/filesystem/root/./opt' -> '/tmp/unpack/./opt'
'/filesystem/root/./opt/ridgeweather' -> '/tmp/unpack/./opt/ridgeweather'
'/filesystem/root/./opt/ridgeweather/web' -> '/tmp/unpack/./opt/ridgeweather/web'
'/filesystem/root/./opt/ridgeweather/web/templates' -> '/tmp/unpack/./opt/ridgeweather/web/templates'
'/filesystem/root/./opt/ridgeweather/web/templates/index.html' -> '/tmp/unpack/./opt/ridgeweather/web/templates/index.html'
'/filesystem/root/./opt/ridgeweather/web/templates/hotspot.html' -> '/tmp/unpack/./opt/ridgeweather/web/templates/hotspot.html'
'/filesystem/root/./opt/ridgeweather/web/templates/hotspot-restarting.html' -> '/tmp/unpack/./opt/ridgeweather/web/templates/hotspot-restarting.html'
'/filesystem/root/./opt/ridgeweather/web/templates/containers.html' -> '/tmp/unpack/./opt/ridgeweather/web/templates/containers.html'
'/filesystem/root/./opt/ridgeweather/web/templates/container_logs.html' -> '/tmp/unpack/./opt/ridgeweather/web/templates/container_logs.html'
'/filesystem/root/./opt/ridgeweather/web/templates/container_env.html' -> '/tmp/unpack/./opt/ridgeweather/web/templates/container_env.html'
'/filesystem/root/./opt/ridgeweather/web/app.py' -> '/tmp/unpack/./opt/ridgeweather/web/app.py'
'/filesystem/root/./opt/ridgeweather/web/hotspot-app.py' -> '/tmp/unpack/./opt/ridgeweather/web/hotspot-app.py'
'/filesystem/root/./opt/ridgeweather/web/wifi_utils.py' -> '/tmp/unpack/./opt/ridgeweather/web/wifi_utils.py'
'/filesystem/root/./opt/ridgeweather/web/fakedns.py' -> '/tmp/unpack/./opt/ridgeweather/web/fakedns.py'
'/filesystem/root/./opt/ridgeweather/web/docker_manager.py' -> '/tmp/unpack/./opt/ridgeweather/web/docker_manager.py'
'/filesystem/root/./opt/ridgeweather/scripts' -> '/tmp/unpack/./opt/ridgeweather/scripts'
'/filesystem/root/./opt/ridgeweather/scripts/net-or-hotspot.sh' -> '/tmp/unpack/./opt/ridgeweather/scripts/net-or-hotspot.sh'
'/filesystem/root/./opt/ridgeweather/scripts/setup-tailscale.sh' -> '/tmp/unpack/./opt/ridgeweather/scripts/setup-tailscale.sh'
'/filesystem/root/./opt/ridgeweather/accesspoint' -> '/tmp/unpack/./opt/ridgeweather/accesspoint'
'/filesystem/root/./opt/ridgeweather/accesspoint/hostapd.conf' -> '/tmp/unpack/./opt/ridgeweather/accesspoint/hostapd.conf'
'/filesystem/root/./opt/ridgeweather/accesspoint/dhcpd.conf' -> '/tmp/unpack/./opt/ridgeweather/accesspoint/dhcpd.conf'
'/filesystem/root/./opt/ridgeweather/accesspoint/isc-dhcp-server' -> '/tmp/unpack/./opt/ridgeweather/accesspoint/isc-dhcp-server'
'/filesystem/root/./opt/ridgeweather/tailscale-auth-key.example' -> '/tmp/unpack/./opt/ridgeweather/tailscale-auth-key.example'
'/filesystem/root/./etc' -> '/tmp/unpack/./etc'
'/filesystem/root/./etc/systemd' -> '/tmp/unpack/./etc/systemd'
'/filesystem/root/./etc/systemd/system' -> '/tmp/unpack/./etc/systemd/system'
'/filesystem/root/./etc/systemd/system/ridgeweather-web.service' -> '/tmp/unpack/./etc/systemd/system/ridgeweather-web.service'
'/filesystem/root/./etc/systemd/system/ridgeweather-hotspot.service' -> '/tmp/unpack/./etc/systemd/system/ridgeweather-hotspot.service'
'/filesystem/root/./etc/systemd/system/ridgeweather-tailscale.service' -> '/tmp/unpack/./etc/systemd/system/ridgeweather-tailscale.service'
'/filesystem/root/./boot' -> '/tmp/unpack/./boot'
'/filesystem/root/./boot/RIDGEWEATHER-README.txt' -> '/tmp/unpack/./boot/RIDGEWEATHER-README.txt'
+ '[' -n '' ']'
+ cp -v -r --preserve=mode,ownership,timestamps /tmp/unpack/. /
'/tmp/unpack/./opt/ridgeweather' -> '/./opt/ridgeweather'
'/tmp/unpack/./opt/ridgeweather/web' -> '/./opt/ridgeweather/web'
'/tmp/unpack/./opt/ridgeweather/web/templates' -> '/./opt/ridgeweather/web/templates'
'/tmp/unpack/./opt/ridgeweather/web/templates/index.html' -> '/./opt/ridgeweather/web/templates/index.html'
'/tmp/unpack/./opt/ridgeweather/web/templates/hotspot.html' -> '/./opt/ridgeweather/web/templates/hotspot.html'
'/tmp/unpack/./opt/ridgeweather/web/templates/hotspot-restarting.html' -> '/./opt/ridgeweather/web/templates/hotspot-restarting.html'
'/tmp/unpack/./opt/ridgeweather/web/templates/containers.html' -> '/./opt/ridgeweather/web/templates/containers.html'
'/tmp/unpack/./opt/ridgeweather/web/templates/container_logs.html' -> '/./opt/ridgeweather/web/templates/container_logs.html'
'/tmp/unpack/./opt/ridgeweather/web/templates/container_env.html' -> '/./opt/ridgeweather/web/templates/container_env.html'
'/tmp/unpack/./opt/ridgeweather/web/app.py' -> '/./opt/ridgeweather/web/app.py'
'/tmp/unpack/./opt/ridgeweather/web/hotspot-app.py' -> '/./opt/ridgeweather/web/hotspot-app.py'
'/tmp/unpack/./opt/ridgeweather/web/wifi_utils.py' -> '/./opt/ridgeweather/web/wifi_utils.py'
'/tmp/unpack/./opt/ridgeweather/web/fakedns.py' -> '/./opt/ridgeweather/web/fakedns.py'
'/tmp/unpack/./opt/ridgeweather/web/docker_manager.py' -> '/./opt/ridgeweather/web/docker_manager.py'
'/tmp/unpack/./opt/ridgeweather/scripts' -> '/./opt/ridgeweather/scripts'
'/tmp/unpack/./opt/ridgeweather/scripts/net-or-hotspot.sh' -> '/./opt/ridgeweather/scripts/net-or-hotspot.sh'
'/tmp/unpack/./opt/ridgeweather/scripts/setup-tailscale.sh' -> '/./opt/ridgeweather/scripts/setup-tailscale.sh'
'/tmp/unpack/./opt/ridgeweather/accesspoint' -> '/./opt/ridgeweather/accesspoint'
'/tmp/unpack/./opt/ridgeweather/accesspoint/hostapd.conf' -> '/./opt/ridgeweather/accesspoint/hostapd.conf'
'/tmp/unpack/./opt/ridgeweather/accesspoint/dhcpd.conf' -> '/./opt/ridgeweather/accesspoint/dhcpd.conf'
'/tmp/unpack/./opt/ridgeweather/accesspoint/isc-dhcp-server' -> '/./opt/ridgeweather/accesspoint/isc-dhcp-server'
'/tmp/unpack/./opt/ridgeweather/tailscale-auth-key.example' -> '/./opt/ridgeweather/tailscale-auth-key.example'
'/tmp/unpack/./etc/systemd/system/ridgeweather-web.service' -> '/./etc/systemd/system/ridgeweather-web.service'
'/tmp/unpack/./etc/systemd/system/ridgeweather-hotspot.service' -> '/./etc/systemd/system/ridgeweather-hotspot.service'
'/tmp/unpack/./etc/systemd/system/ridgeweather-tailscale.service' -> '/./etc/systemd/system/ridgeweather-tailscale.service'
'/tmp/unpack/./boot/RIDGEWEATHER-README.txt' -> '/./boot/RIDGEWEATHER-README.txt'
+ rm -r /tmp/unpack
+ useradd -m -s /bin/bash ridgeweather
+ mkdir -p /opt/ridgeweather
+ chown -R ridgeweather:ridgeweather /opt/ridgeweather
+ usermod -aG docker ridgeweather
+ usermod -aG docker pi
+ echo 1.0.0
+ echo '127.0.0.1 ridgeweather.local'
+ cat
+ touch /opt/ridgeweather/os.ridgeweather.image
+ chmod +x /opt/ridgeweather/web/app.py /opt/ridgeweather/web/docker_manager.py /opt/ridgeweather/web/fakedns.py /opt/ridgeweather/web/hotspot-app.py /opt/ridgeweather/web/wifi_utils.py
+ chmod +x /opt/ridgeweather/scripts/net-or-hotspot.sh /opt/ridgeweather/scripts/setup-tailscale.sh
+ mkdir -p /opt/ridgeweather/docker-templates
+ chown -R ridgeweather:ridgeweather /opt/ridgeweather/docker-templates
+ systemctl mask hostapd.service
Created symlink /etc/systemd/system/hostapd.service -> /dev/null.
+ systemctl mask isc-dhcp-server.service
Created symlink /etc/systemd/system/isc-dhcp-server.service -> /dev/null.
+ apt-get clean
+ rm -rf /var/lib/apt/lists/archive.raspberrypi.com_debian_dists_bookworm_InRelease /var/lib/apt/lists/archive.raspberrypi.com_debian_dists_bookworm_main_binary-arm64_Packages /var/lib/apt/lists/archive.raspberrypi.com_debian_dists_bookworm_main_binary-armhf_Packages /var/lib/apt/lists/auxfiles /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_InRelease /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_contrib_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_contrib_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_contrib_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_main_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_main_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_main_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian-security_dists_bookworm-security_non-free-firmware_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm-updates_InRelease /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm-updates_main_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm-updates_main_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm-updates_main_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_InRelease /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_contrib_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_contrib_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_contrib_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_main_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_main_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_main_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_non-free-firmware_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_non-free-firmware_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_non-free-firmware_i18n_Translation-en /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_non-free_binary-arm64_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_non-free_binary-armhf_Packages /var/lib/apt/lists/deb.debian.org_debian_dists_bookworm_non-free_i18n_Translation-en /var/lib/apt/lists/lock /var/lib/apt/lists/partial /var/lib/apt/lists/pkgs.tailscale.com_stable_debian_dists_bookworm_InRelease /var/lib/apt/lists/pkgs.tailscale.com_stable_debian_dists_bookworm_main_binary-all_Packages /var/lib/apt/lists/pkgs.tailscale.com_stable_debian_dists_bookworm_main_binary-arm64_Packages /var/lib/apt/lists/pkgs.tailscale.com_stable_debian_dists_bookworm_main_binary-armhf_Packages
+ '[' -d custompios_export ']'
+ rm chroot_script
+ '[' -d filesystem ']'
+ rm -rfv filesystem
removed 'filesystem/root/etc/systemd/system/ridgeweather-web.service'
removed 'filesystem/root/etc/systemd/system/ridgeweather-hotspot.service'
removed 'filesystem/root/etc/systemd/system/ridgeweather-tailscale.service'
removed directory 'filesystem/root/etc/systemd/system'
removed directory 'filesystem/root/etc/systemd'
removed directory 'filesystem/root/etc'
removed 'filesystem/root/boot/RIDGEWEATHER-README.txt'
removed directory 'filesystem/root/boot'
removed 'filesystem/root/opt/ridgeweather/scripts/setup-tailscale.sh'
removed 'filesystem/root/opt/ridgeweather/scripts/net-or-hotspot.sh'
removed directory 'filesystem/root/opt/ridgeweather/scripts'
removed 'filesystem/root/opt/ridgeweather/web/app.py'
removed 'filesystem/root/opt/ridgeweather/web/templates/container_env.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/container_logs.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/containers.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/hotspot-restarting.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/hotspot.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/index.html'
removed directory 'filesystem/root/opt/ridgeweather/web/templates'
removed 'filesystem/root/opt/ridgeweather/web/docker_manager.py'
removed 'filesystem/root/opt/ridgeweather/web/fakedns.py'
removed 'filesystem/root/opt/ridgeweather/web/wifi_utils.py'
removed 'filesystem/root/opt/ridgeweather/web/hotspot-app.py'
removed directory 'filesystem/root/opt/ridgeweather/web'
removed 'filesystem/root/opt/ridgeweather/tailscale-auth-key.example'
removed 'filesystem/root/opt/ridgeweather/accesspoint/hostapd.conf'
removed 'filesystem/root/opt/ridgeweather/accesspoint/dhcpd.conf'
removed 'filesystem/root/opt/ridgeweather/accesspoint/isc-dhcp-server'
removed directory 'filesystem/root/opt/ridgeweather/accesspoint'
removed directory 'filesystem/root/opt/ridgeweather'
removed directory 'filesystem/root/opt'
removed directory 'filesystem/root'
removed directory 'filesystem'
+ execute_chroot_script /distro/modules/ridgeweather /distro/modules/ridgeweather/end_chroot_script
+ '[' -f /.dockerenv ']'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
++ uname -m
+ '[' aarch64 '!=' aarch64 ']'
+ '[' -d /distro/modules/ridgeweather/filesystem ']'
+ cp -vr --preserve=mode,timestamps /distro/modules/ridgeweather/filesystem .
'/distro/modules/ridgeweather/filesystem' -> './filesystem'
'/distro/modules/ridgeweather/filesystem/root' -> './filesystem/root'
'/distro/modules/ridgeweather/filesystem/root/opt' -> './filesystem/root/opt'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather' -> './filesystem/root/opt/ridgeweather'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web' -> './filesystem/root/opt/ridgeweather/web'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates' -> './filesystem/root/opt/ridgeweather/web/templates'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/index.html' -> './filesystem/root/opt/ridgeweather/web/templates/index.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/hotspot.html' -> './filesystem/root/opt/ridgeweather/web/templates/hotspot.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/hotspot-restarting.html' -> './filesystem/root/opt/ridgeweather/web/templates/hotspot-restarting.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/containers.html' -> './filesystem/root/opt/ridgeweather/web/templates/containers.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/container_logs.html' -> './filesystem/root/opt/ridgeweather/web/templates/container_logs.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/templates/container_env.html' -> './filesystem/root/opt/ridgeweather/web/templates/container_env.html'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/app.py' -> './filesystem/root/opt/ridgeweather/web/app.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/hotspot-app.py' -> './filesystem/root/opt/ridgeweather/web/hotspot-app.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/wifi_utils.py' -> './filesystem/root/opt/ridgeweather/web/wifi_utils.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/fakedns.py' -> './filesystem/root/opt/ridgeweather/web/fakedns.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/docker_manager.py' -> './filesystem/root/opt/ridgeweather/web/docker_manager.py'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/scripts' -> './filesystem/root/opt/ridgeweather/scripts'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/scripts/net-or-hotspot.sh' -> './filesystem/root/opt/ridgeweather/scripts/net-or-hotspot.sh'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/scripts/setup-tailscale.sh' -> './filesystem/root/opt/ridgeweather/scripts/setup-tailscale.sh'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint' -> './filesystem/root/opt/ridgeweather/accesspoint'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint/hostapd.conf' -> './filesystem/root/opt/ridgeweather/accesspoint/hostapd.conf'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint/dhcpd.conf' -> './filesystem/root/opt/ridgeweather/accesspoint/dhcpd.conf'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/accesspoint/isc-dhcp-server' -> './filesystem/root/opt/ridgeweather/accesspoint/isc-dhcp-server'
'/distro/modules/ridgeweather/filesystem/root/opt/ridgeweather/tailscale-auth-key.example' -> './filesystem/root/opt/ridgeweather/tailscale-auth-key.example'
'/distro/modules/ridgeweather/filesystem/root/etc' -> './filesystem/root/etc'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd' -> './filesystem/root/etc/systemd'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system' -> './filesystem/root/etc/systemd/system'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system/ridgeweather-web.service' -> './filesystem/root/etc/systemd/system/ridgeweather-web.service'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system/ridgeweather-hotspot.service' -> './filesystem/root/etc/systemd/system/ridgeweather-hotspot.service'
'/distro/modules/ridgeweather/filesystem/root/etc/systemd/system/ridgeweather-tailscale.service' -> './filesystem/root/etc/systemd/system/ridgeweather-tailscale.service'
'/distro/modules/ridgeweather/filesystem/root/boot' -> './filesystem/root/boot'
'/distro/modules/ridgeweather/filesystem/root/boot/RIDGEWEATHER-README.txt' -> './filesystem/root/boot/RIDGEWEATHER-README.txt'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
+ '[' arm64 == armv7l ']'
+ '[' arm64 == armhf ']'
+ '[' arm64 == aarch64 ']'
+ '[' arm64 == arm64 ']'
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ cp /distro/modules/ridgeweather/end_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
++ uname -m
+ chroot_correct_qemu aarch64 arm64 /distro/modules/ridgeweather/end_chroot_script /CustomPiOS
+ local host_arch=aarch64
+ local target_arch=arm64
+ local chroot_script=/distro/modules/ridgeweather/end_chroot_script
+ local custom_pi_os_path=/CustomPiOS
+ [[ -z aarch64 ]]
+ [[ -z arm64 ]]
+ cp /distro/modules/ridgeweather/end_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ [[ arm64 == \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\6\4 ]]
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ aarch64 != \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ echo 'Building on ARM device a armv7l/aarch64/arm64 system, not using qemu'
Building on ARM device a armv7l/aarch64/arm64 system, not using qemu
+ chroot . /bin/bash /chroot_script
+ set -e
+ source /common.sh
+ systemctl enable ridgeweather-web.service
Created symlink /etc/systemd/system/multi-user.target.wants/ridgeweather-web.service -> /etc/systemd/system/ridgeweather-web.service.
+ systemctl enable ridgeweather-hotspot.service
Created symlink /etc/systemd/system/multi-user.target.wants/ridgeweather-hotspot.service -> /etc/systemd/system/ridgeweather-hotspot.service.
+ systemctl enable ridgeweather-tailscale.service
Created symlink /etc/systemd/system/multi-user.target.wants/ridgeweather-tailscale.service -> /etc/systemd/system/ridgeweather-tailscale.service.
+ systemctl enable avahi-daemon.service
+ systemctl enable docker.service
Synchronizing state of docker.service with SysV service script with /lib/systemd/systemd-sysv-install.
Executing: /lib/systemd/systemd-sysv-install enable docker
+ echo 'RidgeWeather module installation completed'
RidgeWeather module installation completed
+ '[' -d custompios_export ']'
+ rm chroot_script
+ '[' -d filesystem ']'
+ rm -rfv filesystem
removed 'filesystem/root/etc/systemd/system/ridgeweather-web.service'
removed 'filesystem/root/etc/systemd/system/ridgeweather-hotspot.service'
removed 'filesystem/root/etc/systemd/system/ridgeweather-tailscale.service'
removed directory 'filesystem/root/etc/systemd/system'
removed directory 'filesystem/root/etc/systemd'
removed directory 'filesystem/root/etc'
removed 'filesystem/root/boot/RIDGEWEATHER-README.txt'
removed directory 'filesystem/root/boot'
removed 'filesystem/root/opt/ridgeweather/scripts/setup-tailscale.sh'
removed 'filesystem/root/opt/ridgeweather/scripts/net-or-hotspot.sh'
removed directory 'filesystem/root/opt/ridgeweather/scripts'
removed 'filesystem/root/opt/ridgeweather/web/app.py'
removed 'filesystem/root/opt/ridgeweather/web/templates/container_env.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/container_logs.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/containers.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/hotspot-restarting.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/hotspot.html'
removed 'filesystem/root/opt/ridgeweather/web/templates/index.html'
removed directory 'filesystem/root/opt/ridgeweather/web/templates'
removed 'filesystem/root/opt/ridgeweather/web/docker_manager.py'
removed 'filesystem/root/opt/ridgeweather/web/fakedns.py'
removed 'filesystem/root/opt/ridgeweather/web/wifi_utils.py'
removed 'filesystem/root/opt/ridgeweather/web/hotspot-app.py'
removed directory 'filesystem/root/opt/ridgeweather/web'
removed 'filesystem/root/opt/ridgeweather/tailscale-auth-key.example'
removed 'filesystem/root/opt/ridgeweather/accesspoint/hostapd.conf'
removed 'filesystem/root/opt/ridgeweather/accesspoint/dhcpd.conf'
removed 'filesystem/root/opt/ridgeweather/accesspoint/isc-dhcp-server'
removed directory 'filesystem/root/opt/ridgeweather/accesspoint'
removed directory 'filesystem/root/opt/ridgeweather'
removed directory 'filesystem/root/opt'
removed directory 'filesystem/root'
removed directory 'filesystem'
+ execute_chroot_script /CustomPiOS/modules/base /CustomPiOS/modules/base/end_chroot_script
+ '[' -f /.dockerenv ']'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
++ uname -m
+ '[' aarch64 '!=' aarch64 ']'
+ '[' -d /CustomPiOS/modules/base/filesystem ']'
+ cp -vr --preserve=mode,timestamps /CustomPiOS/modules/base/filesystem .
'/CustomPiOS/modules/base/filesystem' -> './filesystem'
'/CustomPiOS/modules/base/filesystem/ubuntu' -> './filesystem/ubuntu'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr' -> './filesystem/ubuntu/usr'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib' -> './filesystem/ubuntu/usr/lib'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib/dhcpcd' -> './filesystem/ubuntu/usr/lib/dhcpcd'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks' -> './filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks'
'/CustomPiOS/modules/base/filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks/10-wpa_supplicant' -> './filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks/10-wpa_supplicant'
++ uname -m
+ '[' aarch64 '!=' armv7l ']'
+ '[' arm64 == armv7l ']'
+ '[' arm64 == armhf ']'
+ '[' arm64 == aarch64 ']'
+ '[' arm64 == arm64 ']'
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ cp /CustomPiOS/modules/base/end_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
++ uname -m
+ chroot_correct_qemu aarch64 arm64 /CustomPiOS/modules/base/end_chroot_script /CustomPiOS
+ local host_arch=aarch64
+ local target_arch=arm64
+ local chroot_script=/CustomPiOS/modules/base/end_chroot_script
+ local custom_pi_os_path=/CustomPiOS
+ [[ -z aarch64 ]]
+ [[ -z arm64 ]]
+ cp /CustomPiOS/modules/base/end_chroot_script chroot_script
+ chmod 755 chroot_script
+ cp /CustomPiOS/common.sh common.sh
+ chmod 755 common.sh
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ [[ arm64 == \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\6\4 ]]
+ grep -q gentoo /etc/os-release
++ which qemu-aarch64-static
+ cp /usr/bin/qemu-aarch64-static usr/bin/qemu-aarch64-static
+ [[ aarch64 != \a\r\m\v\7\l ]]
+ [[ aarch64 != \a\a\r\c\h\6\4 ]]
+ [[ arm64 == \a\r\m\v\7\l ]]
+ [[ arm64 == \a\r\m\h\f ]]
+ echo 'Building on ARM device a armv7l/aarch64/arm64 system, not using qemu'
Building on ARM device a armv7l/aarch64/arm64 system, not using qemu
+ chroot . /bin/bash /chroot_script
+ '[' -n '' ']'
+ '[' -n '' ']'
+ '[' -n '' ']'
+ '[' -f /etc/hostname ']'
+ read FILE_HOST_NAME
+ FILE_HOST_NAME=raspberrypi
+ echo ridgeweather
+ sed -i -e s@raspberrypi@ridgeweather@g /etc/hosts
+ '[' raspbian == ubuntu ']'
+ '[' -f /.resolvconf_link ']'
+ '[' -f /etc/resolv.conf.orig ']'
+ mv /etc/resolv.conf.orig /etc/resolv.conf
+ '[' yes = yes ']'
+ apt-get clean
+ apt-get autoremove -y
Reading package lists...
Building dependency tree...
Reading state information...
0 upgraded, 0 newly installed, 0 to remove and 0 not upgraded.
+ '[' -d custompios_export ']'
+ rm chroot_script
+ '[' -d filesystem ']'
+ rm -rfv filesystem
removed 'filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks/10-wpa_supplicant'
removed directory 'filesystem/ubuntu/usr/lib/dhcpcd/dhcpcd-hooks'
removed directory 'filesystem/ubuntu/usr/lib/dhcpcd'
removed directory 'filesystem/ubuntu/usr/lib'
removed directory 'filesystem/ubuntu/usr'
removed directory 'filesystem/ubuntu'
removed directory 'filesystem'
+++ '[' -n '' ']'
+++ '[' -n '' ']'
+++ '[' yes == yes ']'
+++ restoreLd
+++ '[' -f etc/ld.so.preload ']'
+++ popd
/distro/workspace /distro
+++ unmount_image /distro/workspace/mount
+++ mount_path=/distro/workspace/mount
+++ force=
+++ '[' 1 -gt 1 ']'
+++ sync
+++ '[' -n '' ']'
++++ sudo mount
++++ grep /distro/workspace/mount
++++ awk -F ' on ' '{print $2}'
++++ awk '{print $1}'
++++ sort -r
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount/var/cache/apt...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount/var/cache/apt...
Unmounting /distro/workspace/mount/var/cache/apt...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount/var/cache/apt
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount/sys...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount/sys...
Unmounting /distro/workspace/mount/sys...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount/sys
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount/proc...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount/proc...
Unmounting /distro/workspace/mount/proc...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount/proc
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount/dev/pts...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount/dev/pts...
Unmounting /distro/workspace/mount/dev/pts...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount/dev/pts
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount/dev...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount/dev...
Unmounting /distro/workspace/mount/dev...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount/dev
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount/boot/firmware...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount/boot/firmware...
Unmounting /distro/workspace/mount/boot/firmware...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount/boot/firmware
+++ for m in $(sudo mount | grep $mount_path | awk -F " on " '{print $2}' | awk '{print $1}' | sort -r)
+++ echo_green 'Unmounting /distro/workspace/mount...'
+++ echo -e -n '\e[92m'
[92m+++ echo Unmounting /distro/workspace/mount...
Unmounting /distro/workspace/mount...
+++ echo -e -n '\e[0m'
[0m+++ sudo umount /distro/workspace/mount
+++ chmod 644 2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' -n 200 ']'
+++ minimize_ext 2025-05-13-raspios-bookworm-arm64-lite.img 2 200
+++ image=2025-05-13-raspios-bookworm-arm64-lite.img
+++ partition=2
+++ buffer=200
+++ echo_green 'Resizing partition 2 on 2025-05-13-raspios-bookworm-arm64-lite.img to minimal size + 200 MB'
+++ echo -e -n '\e[92m'
[92m+++ echo Resizing partition 2 on 2025-05-13-raspios-bookworm-arm64-lite.img to minimal size + 200 MB
Resizing partition 2 on 2025-05-13-raspios-bookworm-arm64-lite.img to minimal size + 200 MB
+++ echo -e -n '\e[0m'
[0m++++ sfdisk --json 2025-05-13-raspios-bookworm-arm64-lite.img
+++ fdisk_output='{
   "partitiontable": {
      "label": "dos",
      "id": "0xd9c86127",
      "device": "2025-05-13-raspios-bookworm-arm64-lite.img",
      "unit": "sectors",
      "sectorsize": 512,
      "partitions": [
         {
            "node": "2025-05-13-raspios-bookworm-arm64-lite.img1",
            "start": 16384,
            "size": 1048576,
            "type": "c"
         },{
            "node": "2025-05-13-raspios-bookworm-arm64-lite.img2",
            "start": 1064960,
            "size": 7397376,
            "type": "83"
         }
      ]
   }
}'
++++ jq '.partitiontable.partitions[] | select(.node == "2025-05-13-raspios-bookworm-arm64-lite.img2").start'
+++ start=1064960
++++ jq '.partitiontable.partitions[] | select(.node == "2025-05-13-raspios-bookworm-arm64-lite.img2").size'
+++ e2fsize_blocks=7397376
+++ offset=545259520
+++ detach_all_loopback 2025-05-13-raspios-bookworm-arm64-lite.img
+++ image_name=2025-05-13-raspios-bookworm-arm64-lite.img
++++ losetup
++++ grep 2025-05-13-raspios-bookworm-arm64-lite.img
++++ awk '{ print $1 }'
+++ test_for_image 2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' '!' -f 2025-05-13-raspios-bookworm-arm64-lite.img ']'
++++ losetup -f --show -o 545259520 2025-05-13-raspios-bookworm-arm64-lite.img
+++ LODEV=/dev/loop7
+++ trap 'losetup -d $LODEV' EXIT
+++ file -Ls /dev/loop7
+++ grep -qi ext
+++ e2fsck -fy /dev/loop7
e2fsck 1.47.0 (5-Feb-2023)
Pass 1: Checking inodes, blocks, and sizes
Pass 2: Checking directory structure
Pass 3: Checking directory connectivity
Pass 4: Checking reference counts
Pass 5: Checking group summary information
rootfs: 69465/230608 files (0.2% non-contiguous), 513082/924672 blocks
+++ resize2fs -p /dev/loop7
resize2fs 1.47.0 (5-Feb-2023)
The filesystem is already 924672 (4k) blocks long.  Nothing to do!

++++ tune2fs -l /dev/loop7
++++ grep -i 'block size'
++++ awk -F: '{print $2-0}'
+++ e2fblocksize=4096
++++ resize2fs -P /dev/loop7
++++ awk -F: '{print $2-0}'
++++ grep -i 'minimum size'
+++ e2fminsize=708526
+++ e2fminsize_bytes=2902122496
+++ e2ftarget_bytes=3111837696
+++ e2fsize_bytes=3787456000
+++ e2fminsize_mb=2767
+++ e2fminsize_blocks=5668209
+++ e2ftarget_mb=2967
+++ e2ftarget_blocks=6077809
+++ e2fsize_mb=3611
+++ size_offset_mb=644
+++ echo_green 'Actual size is 3611 MB (7397376 blocks), Minimum size is 2767 MB (708526 file system blocks, 5668209 blocks)'
+++ echo -e -n '\e[92m'
[92m+++ echo Actual size is 3611 MB '(7397376' 'blocks),' Minimum size is 2767 MB '(708526' file system blocks, 5668209 'blocks)'
Actual size is 3611 MB (7397376 blocks), Minimum size is 2767 MB (708526 file system blocks, 5668209 blocks)
+++ echo -e -n '\e[0m'
[0m+++ echo_green 'Resizing to 2967 MB (6077809 blocks)'
+++ echo -e -n '\e[92m'
[92m+++ echo Resizing to 2967 MB '(6077809' 'blocks)'
Resizing to 2967 MB (6077809 blocks)
+++ echo -e -n '\e[0m'
[0m+++ '[' 644 -gt 0 ']'
+++ echo_green 'Partition size is bigger then the desired size, shrinking'
+++ echo -e -n '\e[92m'
[92m+++ echo Partition size is bigger then the desired size, shrinking
Partition size is bigger then the desired size, shrinking
+++ echo -e -n '\e[0m'
[0m+++ shrink_ext 2025-05-13-raspios-bookworm-arm64-lite.img 2 2966
+++ image=2025-05-13-raspios-bookworm-arm64-lite.img
+++ partition=2
+++ size=2966
+++ echo_green 'Resizing file system to 2966 MB...'
+++ echo -e -n '\e[92m'
[92m+++ echo Resizing file system to 2966 MB...
Resizing file system to 2966 MB...
+++ echo -e -n '\e[0m'
[0m++++ sfdisk --json 2025-05-13-raspios-bookworm-arm64-lite.img
++++ jq '.partitiontable.partitions[] | select(.node ==  "2025-05-13-raspios-bookworm-arm64-lite.img2").start'
+++ start=1064960
+++ offset=545259520
+++ detach_all_loopback 2025-05-13-raspios-bookworm-arm64-lite.img
+++ image_name=2025-05-13-raspios-bookworm-arm64-lite.img
++++ losetup
++++ grep 2025-05-13-raspios-bookworm-arm64-lite.img
++++ awk '{ print $1 }'
+++ for img in $(losetup | grep $1 | awk '{ print $1 }')
++++ printf %s /dev/loop7
++++ sed s/2025-05-13-raspios-bookworm-arm64-lite.img//g
+++ '[' /dev/loop7 '!=' /dev/loop7 ']'
+++ test_for_image 2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' '!' -f 2025-05-13-raspios-bookworm-arm64-lite.img ']'
++++ losetup -f --show -o 545259520 2025-05-13-raspios-bookworm-arm64-lite.img
+++ LODEV=/dev/loop8
+++ trap 'losetup -d $LODEV' EXIT
+++ e2fsck -fy /dev/loop8
e2fsck 1.47.0 (5-Feb-2023)
Pass 1: Checking inodes, blocks, and sizes
Pass 2: Checking directory structure
Pass 3: Checking directory connectivity
Pass 4: Checking reference counts
Pass 5: Checking group summary information
rootfs: 69465/230608 files (0.2% non-contiguous), 513082/924672 blocks
+++ e2ftarget_bytes=3110076416
+++ e2ftarget_blocks=6074369
+++ echo_green 'Resizing file system to 6074369 blocks...'
+++ echo -e -n '\e[92m'
[92m+++ echo Resizing file system to 6074369 blocks...
Resizing file system to 6074369 blocks...
+++ echo -e -n '\e[0m'
[0m+++ resize2fs /dev/loop8 6074369s
resize2fs 1.47.0 (5-Feb-2023)
Resizing the filesystem on /dev/loop8 to 759296 (4k) blocks.
The filesystem on /dev/loop8 is now 759296 (4k) blocks long.

+++ losetup -d /dev/loop8
+++ trap - EXIT
+++ new_end=7139329
+++ echo_green 'Resizing partition to end at 1064960 + 6074369 = 7139329 blocks...'
+++ echo -e -n '\e[92m'
[92m+++ echo Resizing partition to end at 1064960 + 6074369 = 7139329 blocks...
Resizing partition to end at 1064960 + 6074369 = 7139329 blocks...
+++ echo -e -n '\e[0m'
[0m+++ fdisk 2025-05-13-raspios-bookworm-arm64-lite.img

Welcome to fdisk (util-linux 2.38.1).
Changes will remain in memory only, until you decide to write them.
Be careful before using the write command.


Command (m for help): Disk 2025-05-13-raspios-bookworm-arm64-lite.img: 4.04 GiB, 4332716032 bytes, 8462336 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: dos
Disk identifier: 0xd9c86127

Device                                      Boot   Start     End Sectors  Size Id Type
2025-05-13-raspios-bookworm-arm64-lite.img1        16384 1064959 1048576  512M  c W95 FAT32 (LBA)
2025-05-13-raspios-bookworm-arm64-lite.img2      1064960 8462335 7397376  3.5G 83 Linux

Command (m for help): Partition number (1,2, default 2): 
Partition 2 has been deleted.

Command (m for help): Partition type
   p   primary (1 primary, 0 extended, 3 free)
   e   extended (container for logical partitions)
Select (default p): Partition number (2-4, default 2): First sector (2048-8462335, default 2048): Last sector, +/-sectors or +/-size{K,M,G,T,P} (1064960-8462335, default 8462335): 
Created a new partition 2 of type 'Linux' and of size 2.9 GiB.
Partition #2 contains a ext4 signature.

Command (m for help): 
Disk 2025-05-13-raspios-bookworm-arm64-lite.img: 4.04 GiB, 4332716032 bytes, 8462336 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: dos
Disk identifier: 0xd9c86127

Device                                      Boot   Start     End Sectors  Size Id Type
2025-05-13-raspios-bookworm-arm64-lite.img1        16384 1064959 1048576  512M  c W95 FAT32 (LBA)
2025-05-13-raspios-bookworm-arm64-lite.img2      1064960 7139329 6074370  2.9G 83 Linux

Command (m for help): The partition table has been altered.
Syncing disks.

+++ new_size=3655336960
+++ echo_green 'Truncating image to 3655336960 bytes...'
+++ echo -e -n '\e[92m'
[92m+++ echo Truncating image to 3655336960 bytes...
Truncating image to 3655336960 bytes...
+++ echo -e -n '\e[0m'
[0m+++ truncate --size=3655336960 2025-05-13-raspios-bookworm-arm64-lite.img
+++ fdisk -l 2025-05-13-raspios-bookworm-arm64-lite.img
Disk 2025-05-13-raspios-bookworm-arm64-lite.img: 3.4 GiB, 3655336960 bytes, 7139330 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: dos
Disk identifier: 0xd9c86127

Device                                      Boot   Start     End Sectors  Size Id Type
2025-05-13-raspios-bookworm-arm64-lite.img1        16384 1064959 1048576  512M  c W95 FAT32 (LBA)
2025-05-13-raspios-bookworm-arm64-lite.img2      1064960 7139329 6074370  2.9G 83 Linux
+++ echo_green 'Resizing filesystem ...'
+++ echo -e -n '\e[92m'
[92m+++ echo Resizing filesystem ...
Resizing filesystem ...
+++ echo -e -n '\e[0m'
[0m+++ detach_all_loopback 2025-05-13-raspios-bookworm-arm64-lite.img
+++ image_name=2025-05-13-raspios-bookworm-arm64-lite.img
++++ losetup
++++ grep 2025-05-13-raspios-bookworm-arm64-lite.img
++++ awk '{ print $1 }'
+++ for img in $(losetup | grep $1 | awk '{ print $1 }')
++++ printf %s /dev/loop7
++++ sed s/2025-05-13-raspios-bookworm-arm64-lite.img//g
+++ '[' /dev/loop7 '!=' /dev/loop7 ']'
+++ test_for_image 2025-05-13-raspios-bookworm-arm64-lite.img
+++ '[' '!' -f 2025-05-13-raspios-bookworm-arm64-lite.img ']'
++++ losetup -f --show -o 545259520 2025-05-13-raspios-bookworm-arm64-lite.img
+++ LODEV=/dev/loop8
+++ trap 'losetup -d $LODEV' EXIT
+++ e2fsck -fy /dev/loop8
e2fsck 1.47.0 (5-Feb-2023)
Pass 1: Checking inodes, blocks, and sizes
Pass 2: Checking directory structure
Pass 3: Checking directory connectivity
Pass 4: Checking reference counts
Pass 5: Checking group summary information
rootfs: 69465/190848 files (0.2% non-contiguous), 510327/759296 blocks
+++ resize2fs -p /dev/loop8
resize2fs 1.47.0 (5-Feb-2023)
The filesystem is already 759296 (4k) blocks long.  Nothing to do!

+++ losetup -d /dev/loop8
+++ trap - EXIT
+++ popd
/distro
++++ date
+++ echo_green -e '\nBUILD SUCCEEDED @ Mon Jul 14 14:42:29 UTC 2025!\n'
+++ echo -e -n '\e[92m'
[92m+++ echo -e '\nBUILD' SUCCEEDED @ Mon Jul 14 14:42:29 UTC '2025!\n'

BUILD SUCCEEDED @ Mon Jul 14 14:42:29 UTC 2025!

+++ echo -e -n '\e[0m'
[0m