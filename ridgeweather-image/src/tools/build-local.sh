#!/bin/bash
# RidgeWeather build script
# Based on adsb-feeder build process

set -e

echo "Building RidgeWeather image using Docker..."

# Check if Docker is running
if ! docker ps &>/dev/null; then
    echo "Error: Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

# Navigate to the source directory
cd "$(dirname "$0")/.."

# Build using Docker
echo "Starting Docker build process..."
docker run --rm --privileged -v $(pwd):/distro ghcr.io/guysoft/custompios:devel build --download

echo "Build completed! Check the workspace/ directory for the image file."
