#!/usr/bin/env bash
# RidgeWeather module installation script
set -x
set -e

source /common.sh
install_cleanup_trap

# Install required packages
apt-get update
apt-get install -y \
    python3-pip python3-flask python3-jinja2 python3-yaml \
    hostapd isc-dhcp-server dnsmasq \
    wireless-tools wpasupplicant \
    avahi-daemon avahi-utils \
    net-tools iproute2 iputils-ping \
    curl gnupg lsb-release jq \
    docker.io docker-compose

# Install Tailscale
curl -fsSL https://pkgs.tailscale.com/stable/debian/bookworm.noarmor.gpg | gpg --dearmor -o /usr/share/keyrings/tailscale-archive-keyring.gpg
echo 'deb [signed-by=/usr/share/keyrings/tailscale-archive-keyring.gpg] https://pkgs.tailscale.com/stable/debian bookworm main' | tee /etc/apt/sources.list.d/tailscale.list
apt-get update
apt-get install -y tailscale

# Unpack filesystem files first
unpack /filesystem/root /

# Create ridgeweather user and directories
useradd -m -s /bin/bash ridgeweather || true
mkdir -p /opt/ridgeweather
chown -R ridgeweather:ridgeweather /opt/ridgeweather

# Add ridgeweather user to docker group
usermod -aG docker ridgeweather
usermod -aG docker pi

# Create version file
echo "1.0.0" > /opt/ridgeweather/ridgeweather.version

# Configure hostname resolution
echo "127.0.0.1 ridgeweather.local" >> /etc/hosts

# Configure avahi for .local domain resolution
cat > /etc/avahi/avahi-daemon.conf << 'EOF'
[server]
host-name=ridgeweather
domain-name=local
use-ipv4=yes
use-ipv6=no
allow-interfaces=wlan0,eth0
deny-interfaces=lo

[wide-area]
enable-wide-area=yes

[publish]
publish-addresses=yes
publish-hinfo=yes
publish-workstation=yes
publish-domain=yes

[reflector]
enable-reflector=no

[rlimits]
rlimit-core=0
rlimit-data=4194304
rlimit-fsize=0
rlimit-nofile=768
rlimit-stack=4194304
rlimit-nproc=3
EOF

# Create flag file to indicate this is a RidgeWeather image
touch /opt/ridgeweather/os.ridgeweather.image

# Make scripts executable
chmod +x /opt/ridgeweather/web/*.py
chmod +x /opt/ridgeweather/scripts/*.sh

# Create docker templates directory
mkdir -p /opt/ridgeweather/docker-templates
chown -R ridgeweather:ridgeweather /opt/ridgeweather/docker-templates

# Mask hotspot services by default (they will be unmasked when needed)
systemctl mask hostapd.service
systemctl mask isc-dhcp-server.service

# Clean up
apt-get clean
rm -rf /var/lib/apt/lists/*
