<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RidgeWeather WiFi Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2c5aa0;
            margin: 0;
            font-size: 2em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[list] {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path d="M6 9L1.5 4.5h9z" fill="%23666"/></svg>');
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        .btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover {
            background: #1e3d6f;
        }
        .comment {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }
        .comment.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RidgeWeather WiFi Setup</h1>
            <p>Configure your WiFi connection</p>
        </div>

        {% if comment == "" %}
        <div class="instructions">
            <p>RidgeWeather couldn't connect to a network. Please enter your WiFi credentials below to connect to your network and continue setup.</p>
        </div>
        {% else %}
        <div class="comment {% if 'Failed' in comment %}error{% endif %}">
            {{ comment }}
        </div>
        {% endif %}

        {% if not comment.startswith("Success") %}
        <form method="post">
            <div class="form-group">
                <label for="ssidinput">WiFi Network (SSID)</label>
                <input class="form-control" list="ssidlist" id="ssidinput" name="ssid" placeholder="Select or enter WiFi network name" required>
                <datalist id="ssidlist">
                    {% for ssid in ssids %}
                    <option value="{{ ssid }}">{{ ssid }}</option>
                    {% endfor %}
                </datalist>
            </div>

            <div class="form-group">
                <label for="passwd">WiFi Password</label>
                <input type="password" id="passwd" name="passwd" placeholder="Enter WiFi password" required>
            </div>

            <button type="submit" name="submit" value="go" class="btn">Connect to WiFi</button>
        </form>

        <div class="instructions" style="margin-top: 30px;">
            <p><strong>What happens next:</strong></p>
            <ul>
                <li>Click "Connect to WiFi" to test your credentials</li>
                <li>The hotspot connection will drop temporarily</li>
                <li>If successful, RidgeWeather will be available at <strong>http://ridgeweather.local/</strong></li>
                <li>If connection fails, the hotspot will restart for you to try again</li>
            </ul>
        </div>
        {% endif %}

        <div class="footer">
            <p>RidgeWeather v{{ version }}</p>
        </div>
    </div>
</body>
</html>
