#!/usr/bin/env python3
"""
RidgeWeather Main Web Application
Simple Flask app that displays a static page at ridgeweather.local
"""

import subprocess
import sys
import json
import os
from flask import Flask, render_template, request, redirect, flash, jsonify
from docker_manager import DockerManager

def print_err(*args, **kwargs):
    """Print to stderr for logging"""
    print(*args, file=sys.stderr, **kwargs)

class RidgeWeatherApp:
    def __init__(self):
        self.app = Flask(__name__)
        self.app.secret_key = "ridgeweather-secret-key"
        self.docker_manager = DockerManager()

        # Set up routes
        self.app.add_url_rule("/", "index", self.index, methods=["GET"])
        self.app.add_url_rule("/index", "index", self.index, methods=["GET"])

        # Docker management routes
        self.app.add_url_rule("/containers", "containers", self.containers, methods=["GET"])
        self.app.add_url_rule("/containers/<container_id>/action", "container_action", self.container_action, methods=["POST"])
        self.app.add_url_rule("/containers/<container_id>/logs", "container_logs", self.container_logs, methods=["GET"])
        self.app.add_url_rule("/containers/<container_id>/env", "container_env", self.container_env, methods=["GET", "POST"])

        # Tailscale routes
        self.app.add_url_rule("/tailscale/auth", "tailscale_auth", self.tailscale_auth, methods=["POST"])

        # API routes
        self.app.add_url_rule("/api/containers", "api_containers", self.api_containers, methods=["GET"])
        self.app.add_url_rule("/api/system", "api_system", self.api_system, methods=["GET"])
        
    def get_system_info(self):
        """Get basic system information"""
        info = {}
        
        # Get hostname
        try:
            result = subprocess.run(['hostname'], capture_output=True, text=True, timeout=5)
            info['hostname'] = result.stdout.strip() if result.returncode == 0 else "unknown"
        except:
            info['hostname'] = "unknown"
            
        # Get uptime
        try:
            result = subprocess.run(['uptime', '-p'], capture_output=True, text=True, timeout=5)
            info['uptime'] = result.stdout.strip() if result.returncode == 0 else "unknown"
        except:
            info['uptime'] = "unknown"

        # Get Tailscale status
        try:
            result = subprocess.run(['tailscale', 'status', '--json'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                ts_data = json.loads(result.stdout)
                backend_state = ts_data.get('BackendState', 'Unknown')
                info['tailscale_status'] = backend_state
                info['tailscale_ip'] = ts_data.get('TailscaleIPs', ['N/A'])[0] if ts_data.get('TailscaleIPs') else 'N/A'
                info['tailscale_needs_auth'] = backend_state in ['NeedsLogin', 'NeedsMachineAuth']
            else:
                info['tailscale_status'] = 'Not connected'
                info['tailscale_ip'] = 'N/A'
                info['tailscale_needs_auth'] = True
        except:
            info['tailscale_status'] = 'Unknown'
            info['tailscale_ip'] = 'N/A'
            info['tailscale_needs_auth'] = True

        return info
    
    def index(self):
        """Main page - simple static page"""
        system_info = self.get_system_info()
        container_count = len(self.docker_manager.list_containers())

        return render_template(
            "index.html",
            system_info=system_info,
            container_count=container_count,
            version="1.0.0"
        )

    def containers(self):
        """Container management page"""
        containers = self.docker_manager.list_containers()
        return render_template("containers.html", containers=containers)

    def container_action(self, container_id):
        """Handle container actions (start/stop/restart)"""
        action = request.form.get('action')

        if action == 'start':
            success = self.docker_manager.start_container(container_id)
        elif action == 'stop':
            success = self.docker_manager.stop_container(container_id)
        elif action == 'restart':
            success = self.docker_manager.restart_container(container_id)
        elif action == 'remove':
            success = self.docker_manager.remove_container(container_id)
        else:
            flash(f"Unknown action: {action}", "error")
            return redirect("/containers")

        if success:
            flash(f"Container {action} successful", "success")
        else:
            flash(f"Container {action} failed", "error")

        return redirect("/containers")

    def container_logs(self, container_id):
        """View container logs"""
        logs = self.docker_manager.get_container_logs(container_id)
        container = self.docker_manager.get_container_info(container_id)
        return render_template("container_logs.html", logs=logs, container=container)

    def container_env(self, container_id):
        """Manage container environment variables"""
        container = self.docker_manager.get_container_info(container_id)

        if request.method == 'POST':
            env_vars = {}
            for key, value in request.form.items():
                if key.startswith('env_') and value.strip():
                    env_key = key[4:]  # Remove 'env_' prefix
                    env_vars[env_key] = value.strip()

            if self.docker_manager.update_container_env(container_id, env_vars):
                flash("Environment variables updated successfully", "success")
            else:
                flash("Failed to update environment variables", "error")

            return redirect(f"/containers/{container_id}/env")

        return render_template("container_env.html", container=container)

    def tailscale_auth(self):
        """Handle Tailscale authentication with pre-auth key"""
        auth_key = request.form.get('auth_key', '').strip()

        if not auth_key:
            flash("Please provide a valid auth key", "error")
            return redirect("/")

        try:
            # Write auth key to file
            auth_key_file = "/opt/ridgeweather/tailscale-auth-key"
            with open(auth_key_file, 'w') as f:
                f.write(auth_key)

            # Set proper permissions
            os.chmod(auth_key_file, 0o600)

            # Start/restart Tailscale service
            result = subprocess.run(['sudo', 'systemctl', 'restart', 'ridgeweather-tailscale.service'],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                flash("Tailscale authentication started. Please wait a moment for connection.", "success")
            else:
                flash(f"Failed to start Tailscale service: {result.stderr}", "error")

        except Exception as e:
            flash(f"Error setting up Tailscale authentication: {str(e)}", "error")

        return redirect("/")



    def api_containers(self):
        """API endpoint for container data"""
        containers = self.docker_manager.list_containers()
        return jsonify(containers)

    def api_system(self):
        """API endpoint for system information"""
        system_info = self.get_system_info()
        return jsonify(system_info)

def main():
    print_err("Starting RidgeWeather web application")

    app = RidgeWeatherApp()

    # Run the Flask app
    app.app.run(
        host="0.0.0.0",
        port=80,
        debug=False
    )

if __name__ == "__main__":
    main()
