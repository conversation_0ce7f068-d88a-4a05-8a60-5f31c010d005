<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RidgeWeather</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2c5aa0;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .info-section {
            margin-bottom: 30px;
        }
        .info-section h2 {
            color: #2c5aa0;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2c5aa0;
        }
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #2c5aa0;
            font-size: 1.1em;
        }
        .info-card p {
            margin: 0;
            font-family: monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .welcome-message {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 0.9em;
        }
        .tailscale-auth {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }
        .tailscale-auth h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .auth-form {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .auth-form input[type="text"] {
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .auth-form button {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .auth-form button:hover {
            background: #1e3d6f;
        }
        .status-connected {
            color: #28a745;
            font-weight: bold;
        }
        .status-disconnected {
            color: #dc3545;
            font-weight: bold;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RidgeWeather</h1>
            <p>Simple Raspberry Pi System</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="welcome-message">
            <h2>Welcome to RidgeWeather!</h2>
            <p>A Raspberry Pi platform with WiFi hotspot, Tailscale connectivity, and Docker container management.</p>
            <p>Access this page at <strong>http://ridgeweather.local/</strong> when connected to your network.</p>
        </div>

        <div class="info-section">
            <h2>System Information</h2>
            <div class="system-info">
                <div class="info-card">
                    <h3>Hostname</h3>
                    <p>{{ system_info.hostname }}</p>
                </div>
                <div class="info-card">
                    <h3>Uptime</h3>
                    <p>{{ system_info.uptime }}</p>
                </div>
                <div class="info-card">
                    <h3>Tailscale Status</h3>
                    <p class="{% if system_info.tailscale_needs_auth %}status-disconnected{% else %}status-connected{% endif %}">
                        {{ system_info.tailscale_status }}
                    </p>
                    {% if system_info.tailscale_ip != 'N/A' %}
                    <small>IP: {{ system_info.tailscale_ip }}</small>
                    {% endif %}

                    {% if system_info.tailscale_needs_auth %}
                    <div class="tailscale-auth">
                        <h4>🔑 Connect to Tailscale</h4>
                        <p style="margin: 0 0 10px 0; font-size: 14px;">Enter your pre-authorized key to connect:</p>
                        <form method="post" action="/tailscale/auth" class="auth-form">
                            <input type="text" name="auth_key" placeholder="tskey-auth-..." required>
                            <button type="submit">Connect</button>
                        </form>
                    </div>
                    {% endif %}
                </div>
                <div class="info-card">
                    <h3>Docker Containers</h3>
                    <p>{{ container_count }} containers</p>
                    <a href="/containers" style="color: #2c5aa0; text-decoration: none;">Manage →</a>
                </div>
            </div>
        </div>

        <div class="info-section">
            <h2>Quick Actions</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="/containers" style="text-decoration: none;">
                    <div class="info-card" style="text-align: center; cursor: pointer; border-left-color: #28a745;">
                        <h3>🐳 Containers</h3>
                        <p>Manage Docker containers</p>
                    </div>
                </a>
            </div>
        </div>

        <div class="footer">
            <p>RidgeWeather v{{ version }} | Powered by Raspberry Pi</p>
        </div>
    </div>
</body>
</html>
