#!/bin/bash

# RidgeWeather network management script
# Based on adsb-feeder net-or-hotspot.sh

set -e

function print_err() {
    echo "$@" >&2
}

function test_network() {
    local timeout=${1:-5}
    timeout $timeout bash -c 'cat < /dev/null > /dev/tcp/*******/53' 2>/dev/null
}

function check_network() {
    TOTAL_TIMEOUT=$1
    TIMEOUT=5
    ITER=$(( TOTAL_TIMEOUT / TIMEOUT ))
    for i in $(seq $ITER); do
        if test_network $TIMEOUT; then
            return 0
        fi
    done
    return 1
}



# Set wireless regulatory domain (raspbian)
if [[ -f /etc/rpi-issue ]]; then
    iw reg set US
fi

if check_network 30; then
    echo "network reachable, no need to start an access point"
    # out of an abundance of caution make sure these services are not enabled:
    for service in hostapd.service isc-dhcp-server.service; do
        if systemctl is-enabled "$service" &>/dev/null || ! [[ -L /etc/systemd/system/hostapd.service ]]; then
            echo "stopping / disabling / masking $service"
            systemctl stop "$service"
            systemctl disable "$service"
            systemctl mask "$service"
        fi
    done
    exit 0
fi

# that's not good, let's try to start an access point

wlan=$(iw dev | grep Interface | cut -d' ' -f2)
if [[ $wlan == "" ]] ; then
    echo "No wireless interface detected, giving up"
    exit 1
fi

cp /opt/ridgeweather/accesspoint/hostapd.conf /etc/hostapd/hostapd.conf
cp /opt/ridgeweather/accesspoint/dhcpd.conf /etc/dhcp/dhcpd.conf
cp /opt/ridgeweather/accesspoint/isc-dhcp-server /etc/default/isc-dhcp-server

if [[ $wlan != "wlan0" ]] ; then
    sed -i "s/wlan0/$wlan/g" /etc/default/isc-dhcp-server
    sed -i "s/wlan0/$wlan/g" /etc/hostapd/hostapd.conf
fi

systemctl unmask hostapd.service isc-dhcp-server.service &>/dev/null
for service in hostapd.service isc-dhcp-server.service; do
    if systemctl is-enabled "$service" &>/dev/null; then
        systemctl disable "$service"
    fi
done

while true; do
    echo "No internet connection detected, starting access point"
    python3 /opt/ridgeweather/web/hotspot-app.py "$wlan"
    if check_network 30; then
        break
    fi
done
