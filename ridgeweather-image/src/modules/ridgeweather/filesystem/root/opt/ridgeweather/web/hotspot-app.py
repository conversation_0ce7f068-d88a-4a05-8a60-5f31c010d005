#!/usr/bin/env python3
"""
RidgeWeather Hotspot Configuration App
Simple Flask app for WiFi configuration when in hotspot mode
"""

import math
import os
import signal
import socketserver
import subprocess
import sys
import threading
import time
from flask import Flask, render_template, request, redirect
from wifi_utils import WiFiManager
from fakedns import DNSHandler

def print_err(*args, **kwargs):
    """Print to stderr for logging"""
    timestamp = time.strftime("%Y-%m-%dT%H:%M:%S", time.gmtime()) + ".{0:03.0f}Z".format(
        math.modf(time.time())[0] * 1000
    )
    print(*((timestamp,) + args), file=sys.stderr, **kwargs)

class HotspotApp:
    def __init__(self, interface="wlan0"):
        self.app = Flask(__name__)
        self.app.secret_key = "ridgeweather-hotspot-key"
        self.interface = interface
        self.wifi = WiFiManager(interface)
        self.version = "1.0.0"
        self.comment = ""
        self.restart_state = "done"
        self.ssid = ""
        self.passwd = ""
        self._dnsserver = None
        self._dns_thread = None

        # Check base OS like adsb-feeder
        if self.wifi.baseos == "unknown":
            print_err("unknown baseos - giving up")
            sys.exit(1)

        # Scan for SSIDs like adsb-feeder
        print_err("trying to scan for SSIDs")
        self.wifi.ssids = []
        startTime = time.time()
        while time.time() - startTime < 20:
            self.wifi.scan_ssids()
            if len(self.wifi.ssids) > 0:
                break
            time.sleep(1)
        
        # Set up routes
        self.app.add_url_rule("/hotspot", view_func=self.hotspot, methods=["GET"])
        self.app.add_url_rule("/restarting", view_func=self.restarting)
        self.app.add_url_rule("/restart", view_func=self.restart, methods=["POST", "GET"])
        self.app.add_url_rule("/", "/", view_func=self.catch_all, defaults={"path": ""}, methods=["GET", "POST"])
        self.app.add_url_rule("/<path:path>", view_func=self.catch_all, methods=["GET", "POST"])

    def restart(self):
        return self.restart_state

    def hotspot(self):
        return render_template(
            "hotspot.html", 
            version=self.version, 
            comment=self.comment,
            ssids=self.wifi.ssids
        )

    def restarting(self):
        return render_template("hotspot-restarting.html")

    def catch_all(self, path):
        """Handle all requests - redirect to hotspot configuration or process WiFi credentials"""
        if self.restart_state == "restarting":
            return redirect("/restarting")

        if self._request_looks_like_wifi_credentials():
            self.restart_state = "restarting"

            self.ssid = request.form.get("ssid")
            self.passwd = request.form.get("passwd")

            threading.Thread(target=self.test_wifi, args=(self.ssid, self.passwd)).start()
            print_err("started wifi test thread")

            return redirect("/restarting")

        return self.hotspot()

    def _request_looks_like_wifi_credentials(self):
        """Check if the request contains WiFi credentials"""
        return (request.method == "POST" and 
                request.form.get("ssid") and 
                request.form.get("passwd") is not None)

    def setup_hotspot(self):
        """Set up hotspot and fake DNS server for captive portal"""
        if not self._dnsserver and not self._dns_thread:
            print_err("Creating DNS server")
            try:
                self._dnsserver = socketserver.ThreadingUDPServer(("", 53), DNSHandler)
                print_err("Starting DNS server")
                self._dns_thread = threading.Thread(target=self._dnsserver.serve_forever)
                self._dns_thread.start()
            except OSError as e:
                print_err(f"Failed to create DNS server: {e}")

        # Stop any existing network services that might interfere
        if self.wifi.baseos == "dietpi":
            subprocess.run("systemctl stop networking.service", shell=True)
        elif self.wifi.baseos == "raspbian":
            subprocess.run("systemctl stop NetworkManager wpa_supplicant; iw reg set 00", shell=True)

        # Configure network interface and start services (like adsb-feeder)
        subprocess.run(
            f"ip li set {self.interface} up && ip ad add *************/24 broadcast *************** dev {self.interface} && systemctl start hostapd.service",
            shell=True,
        )
        time.sleep(2)
        subprocess.run(
            f"systemctl start isc-dhcp-server.service",
            shell=True,
        )
        print_err("RidgeWeather hotspot started")

    def teardown_hotspot(self):
        """Tear down the hotspot and DNS server"""
        # Stop hotspot services and clean up network (like adsb-feeder)
        subprocess.run(
            f"systemctl stop isc-dhcp-server.service; systemctl stop hostapd.service; ip ad del *************/24 dev {self.interface}; ip addr flush {self.interface}; ip link set dev {self.interface} down",
            shell=True,
        )
        if self.wifi.baseos == "dietpi":
            output = subprocess.run(
                f"systemctl restart --no-block networking.service",
                shell=True,
                capture_output=True,
            )
            print_err(
                f"restarted networking.service: {output.returncode}\n{output.stderr.decode()}\n{output.stdout.decode()}"
            )
        elif self.wifi.baseos == "raspbian":
            subprocess.run(
                f"iw reg set PA; systemctl restart wpa_supplicant NetworkManager",
                shell=True,
            )
        print_err("turned off hotspot")

    def test_wifi(self, ssid, passwd):
        """Test WiFi connection with provided credentials (exactly like adsb-feeder)"""
        # the parent process needs to return from the call to POST
        time.sleep(1.0)
        self.teardown_hotspot()

        print_err(f"testing the '{ssid}' network")

        success = self.wifi.wifi_connect(ssid, passwd)

        if success:
            print_err(f"successfully connected to '{ssid}'")
        else:
            print_err(f"test_wifi failed to connect to '{ssid}'")

            self.comment = "Failed to connect, wrong SSID or password, please try again."
            # now we bring back up the hotspot in order to deliver the result to the user
            # and have them try again
            self.setup_hotspot()
            self.restart_state = "done"
            return

        self.setup_wifi()
        self.restart_state = "done"

    def setup_wifi(self):
        """Complete WiFi setup and exit (exactly like adsb-feeder)"""
        if self._dnsserver:
            print_err("shutting down DNS server")
            self._dnsserver.shutdown()

        print_err(f"connected to wifi: '{self.ssid}'")

        # the shell script that launched this app will do a final connectivity check
        # if there is no connectivity despite being able to join the wifi, it will re-launch this app (unlikely)

        print_err("exiting the hotspot app")
        signal.raise_signal(signal.SIGTERM)
        os._exit(0)

    def run(self):
        """Run the hotspot application"""
        self.setup_hotspot()
        
        # Auto-exit after 5 minutes of inactivity
        def idle_exit():
            time.sleep(300)  # 5 minutes
            print_err("Exiting hotspot app after 5 minutes")
            signal.raise_signal(signal.SIGTERM)
        
        threading.Thread(target=idle_exit).start()
        
        self.app.run(host="0.0.0.0", port=80)

if __name__ == "__main__":
    interface = sys.argv[1] if len(sys.argv) > 1 else "wlan0"
    print_err(f"Starting RidgeWeather hotspot for {interface}")
    
    hotspot = HotspotApp(interface)
    hotspot.run()
