#!/usr/bin/env python3
"""
Docker Manager for RidgeWeather
Handles Docker container management operations
"""

import subprocess
import json
import sys
import os
from datetime import datetime

def print_err(*args, **kwargs):
    """Print to stderr for logging"""
    print(*args, file=sys.stderr, **kwargs)

class DockerManager:
    def __init__(self):
        pass
    
    def list_containers(self):
        """List all Docker containers"""
        try:
            result = subprocess.run([
                'docker', 'ps', '-a', '--format', 
                '{{.ID}}\t{{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                print_err(f"Failed to list containers: {result.stderr}")
                return []
            
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 6:
                        containers.append({
                            'id': parts[0],
                            'name': parts[1],
                            'image': parts[2],
                            'status': parts[3],
                            'ports': parts[4],
                            'created': parts[5],
                            'running': 'Up' in parts[3]
                        })
            
            return containers
        except Exception as e:
            print_err(f"Error listing containers: {e}")
            return []
    
    def get_container_info(self, container_id):
        """Get detailed container information"""
        try:
            result = subprocess.run([
                'docker', 'inspect', container_id
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return None
            
            data = json.loads(result.stdout)[0]
            
            # Extract environment variables
            env_vars = {}
            for env in data.get('Config', {}).get('Env', []):
                if '=' in env:
                    key, value = env.split('=', 1)
                    env_vars[key] = value
            
            return {
                'id': data['Id'][:12],
                'name': data['Name'].lstrip('/'),
                'image': data['Config']['Image'],
                'status': data['State']['Status'],
                'running': data['State']['Running'],
                'created': data['Created'],
                'env_vars': env_vars,
                'ports': data.get('NetworkSettings', {}).get('Ports', {}),
                'mounts': data.get('Mounts', [])
            }
        except Exception as e:
            print_err(f"Error getting container info: {e}")
            return None
    
    def start_container(self, container_id):
        """Start a container"""
        try:
            result = subprocess.run(['docker', 'start', container_id], 
                                  capture_output=True, text=True, timeout=30)
            return result.returncode == 0
        except Exception as e:
            print_err(f"Error starting container: {e}")
            return False
    
    def stop_container(self, container_id):
        """Stop a container"""
        try:
            result = subprocess.run(['docker', 'stop', container_id], 
                                  capture_output=True, text=True, timeout=30)
            return result.returncode == 0
        except Exception as e:
            print_err(f"Error stopping container: {e}")
            return False
    
    def restart_container(self, container_id):
        """Restart a container"""
        try:
            result = subprocess.run(['docker', 'restart', container_id], 
                                  capture_output=True, text=True, timeout=30)
            return result.returncode == 0
        except Exception as e:
            print_err(f"Error restarting container: {e}")
            return False
    
    def remove_container(self, container_id):
        """Remove a container"""
        try:
            # Stop first if running
            subprocess.run(['docker', 'stop', container_id], 
                         capture_output=True, text=True, timeout=30)
            
            # Then remove
            result = subprocess.run(['docker', 'rm', container_id], 
                                  capture_output=True, text=True, timeout=30)
            return result.returncode == 0
        except Exception as e:
            print_err(f"Error removing container: {e}")
            return False
    
    def get_container_logs(self, container_id, lines=100):
        """Get container logs"""
        try:
            result = subprocess.run([
                'docker', 'logs', '--tail', str(lines), container_id
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return f"Error getting logs: {result.stderr}"
            
            return result.stdout
        except Exception as e:
            print_err(f"Error getting container logs: {e}")
            return f"Error getting logs: {e}"
    
    def update_container_env(self, container_id, env_vars):
        """Update container environment variables by recreating it with full config"""
        try:
            # Get current container info with full docker inspect
            result = subprocess.run([
                'docker', 'inspect', container_id
            ], capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                return False

            data = json.loads(result.stdout)[0]

            # Extract all necessary configuration
            container_name = data['Name'].lstrip('/')
            image = data['Config']['Image']

            # Stop and remove current container
            self.stop_container(container_id)
            self.remove_container(container_id)

            # Build docker run command with full configuration
            cmd = ['docker', 'run', '-d', '--name', container_name]

            # Add restart policy
            restart_policy = data.get('HostConfig', {}).get('RestartPolicy', {})
            if restart_policy.get('Name'):
                cmd.extend(['--restart', restart_policy['Name']])

            # Add privileged mode if needed
            if data.get('HostConfig', {}).get('Privileged', False):
                cmd.append('--privileged')

            # Add devices (with improved audio device handling)
            devices = data.get('HostConfig', {}).get('Devices', [])
            for device in devices:
                path_on_host = device.get('PathOnHost')
                path_in_container = device.get('PathInContainer')

                if path_on_host and path_in_container:
                    # Standard device mapping
                    cmd.extend(['--device', f"{path_on_host}:{path_in_container}"])
                elif path_on_host:
                    # Fallback for devices that only have PathOnHost (common with audio devices)
                    cmd.extend(['--device', path_on_host])

            # Special handling for audio devices - ensure /dev/snd is always mounted for audio containers
            # Check if this container was using audio devices by looking for sound-related images or existing /dev/snd mounts
            image_name = data.get('Config', {}).get('Image', '').lower()
            has_audio_mount = any('/dev/snd' in str(device.get('PathOnHost', '')) for device in devices)

            if 'sound' in image_name or has_audio_mount:
                # Ensure audio device access is preserved
                if not any('--device' in cmd and '/dev/snd' in cmd[cmd.index('--device') + 1] for i in range(len(cmd) - 1) if cmd[i] == '--device'):
                    cmd.extend(['--device', '/dev/snd'])
                    print_err(f"Added audio device access for container recreation")

            # Add volumes/mounts
            mounts = data.get('Mounts', [])
            for mount in mounts:
                if mount.get('Type') == 'bind':
                    source = mount.get('Source')
                    destination = mount.get('Destination')
                    options = []
                    if mount.get('RW') is False:
                        options.append('ro')
                    else:
                        options.append('rw')

                    volume_spec = f"{source}:{destination}"
                    if options:
                        volume_spec += f":{','.join(options)}"
                    cmd.extend(['-v', volume_spec])

            # Add port mappings
            port_bindings = data.get('HostConfig', {}).get('PortBindings', {})
            for container_port, host_configs in port_bindings.items():
                if host_configs:
                    for host_config in host_configs:
                        host_port = host_config.get('HostPort')
                        if host_port:
                            cmd.extend(['-p', f"{host_port}:{container_port}"])

            # Add environment variables (merge existing with new)
            existing_env = {}
            for env in data.get('Config', {}).get('Env', []):
                if '=' in env:
                    key, value = env.split('=', 1)
                    existing_env[key] = value

            # Update with new environment variables
            existing_env.update(env_vars)

            # Add all environment variables to command
            for key, value in existing_env.items():
                cmd.extend(['-e', f'{key}={value}'])

            # Add network mode if specified
            network_mode = data.get('HostConfig', {}).get('NetworkMode')
            if network_mode and network_mode != 'default':
                cmd.extend(['--network', network_mode])

            # Add the image
            cmd.append(image)

            print_err(f"Recreating container with command: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                print_err(f"Successfully recreated container '{container_name}' with updated environment")
                return True
            else:
                print_err(f"Failed to recreate container: {result.stderr}")
                return False

        except Exception as e:
            print_err(f"Error updating container environment: {e}")
            return False

