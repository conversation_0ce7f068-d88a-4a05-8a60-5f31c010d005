<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RidgeWeather - Container Management</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2c5aa0;
            margin: 0;
        }
        .btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }
        .btn:hover { background: #1e3d6f; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-sm { padding: 4px 8px; font-size: 12px; }
        
        .container-grid {
            display: grid;
            gap: 20px;
        }
        .container-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            background: #f8f9fa;
        }
        .container-card.running {
            border-left: 4px solid #28a745;
        }
        .container-card.stopped {
            border-left: 4px solid #dc3545;
        }
        .container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .container-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c5aa0;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-running { background: #d4edda; color: #155724; }
        .status-stopped { background: #f8d7da; color: #721c24; }
        
        .container-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        .info-item {
            font-size: 14px;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        
        .container-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .nav-link {
            color: #2c5aa0;
            text-decoration: none;
            margin-right: 15px;
        }
        .nav-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>🐳 Container Management</h1>
                <nav>
                    <a href="/" class="nav-link">← Home</a>
                    <a href="/deploy" class="nav-link">Deploy New</a>
                </nav>
            </div>
            <a href="/deploy" class="btn btn-success">+ Deploy Container</a>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% if containers %}
            <div class="container-grid">
                {% for container in containers %}
                <div class="container-card {{ 'running' if container.running else 'stopped' }}">
                    <div class="container-header">
                        <div class="container-name">{{ container.name }}</div>
                        <div class="status-badge {{ 'status-running' if container.running else 'status-stopped' }}">
                            {{ 'RUNNING' if container.running else 'STOPPED' }}
                        </div>
                    </div>
                    
                    <div class="container-info">
                        <div class="info-item">
                            <div class="info-label">Image:</div>
                            <div>{{ container.image }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">ID:</div>
                            <div>{{ container.id }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Ports:</div>
                            <div>{{ container.ports or 'None' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Created:</div>
                            <div>{{ container.created }}</div>
                        </div>
                    </div>
                    
                    <div class="container-actions">
                        {% if container.running %}
                            <form method="post" action="/containers/{{ container.id }}/action" style="display: inline;">
                                <input type="hidden" name="action" value="stop">
                                <button type="submit" class="btn btn-warning btn-sm">Stop</button>
                            </form>
                            <form method="post" action="/containers/{{ container.id }}/action" style="display: inline;">
                                <input type="hidden" name="action" value="restart">
                                <button type="submit" class="btn btn-warning btn-sm">Restart</button>
                            </form>
                        {% else %}
                            <form method="post" action="/containers/{{ container.id }}/action" style="display: inline;">
                                <input type="hidden" name="action" value="start">
                                <button type="submit" class="btn btn-success btn-sm">Start</button>
                            </form>
                        {% endif %}
                        
                        <a href="/containers/{{ container.id }}/logs" class="btn btn-sm">View Logs</a>
                        <a href="/containers/{{ container.id }}/env" class="btn btn-sm">Environment</a>
                        
                        <form method="post" action="/containers/{{ container.id }}/action" style="display: inline;">
                            <input type="hidden" name="action" value="remove">
                            <button type="submit" class="btn btn-danger btn-sm" 
                                    onclick="return confirm('Are you sure you want to remove this container?')">
                                Remove
                            </button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div style="text-align: center; padding: 40px; color: #666;">
                <h3>No containers found</h3>
                <p>Deploy your first container to get started.</p>
                <a href="/deploy" class="btn btn-success">Deploy Container</a>
            </div>
        {% endif %}
    </div>
</body>
</html>
