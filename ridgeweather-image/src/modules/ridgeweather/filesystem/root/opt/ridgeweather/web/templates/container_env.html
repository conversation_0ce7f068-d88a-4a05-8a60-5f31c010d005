<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RidgeWeather - Container Environment</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2c5aa0;
            margin: 0 0 10px 0;
        }
        .nav-link {
            color: #2c5aa0;
            text-decoration: none;
            margin-right: 15px;
        }
        .nav-link:hover {
            text-decoration: underline;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .env-var-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #1e3d6f;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ Environment Variables</h1>
            <nav>
                <a href="/" class="nav-link">← Home</a>
                <a href="/containers" class="nav-link">Containers</a>
                {% if container %}
                <a href="/containers/{{ container.id }}/logs" class="nav-link">Logs</a>
                {% endif %}
            </nav>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% if container %}
        <div class="warning">
            <strong>⚠️ Warning:</strong> Updating environment variables will recreate the container. 
            Any data not stored in volumes will be lost.
        </div>

        <h3>Container: {{ container.name }}</h3>
        <p><strong>Image:</strong> {{ container.image }}</p>

        <form method="post">
            <h4>Environment Variables</h4>
            
            {% if container.env_vars %}
                {% for key, value in container.env_vars.items() %}
                <div class="env-var-grid">
                    <label for="env_{{ key }}">{{ key }}</label>
                    <input type="text" id="env_{{ key }}" name="env_{{ key }}" value="{{ value }}" 
                           placeholder="Enter value for {{ key }}">
                </div>
                {% endfor %}
            {% else %}
                <p style="color: #666; font-style: italic;">No environment variables found for this container.</p>
            {% endif %}

            <div style="margin-top: 30px;">
                <button type="submit" class="btn">Update Environment</button>
                <a href="/containers" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>Container not found</h3>
            <a href="/containers" class="btn">Back to Containers</a>
        </div>
        {% endif %}
    </div>
</body>
</html>
