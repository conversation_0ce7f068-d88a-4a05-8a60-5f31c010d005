<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RidgeWeather - Container Logs</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2c5aa0;
            margin: 0 0 10px 0;
        }
        .nav-link {
            color: #2c5aa0;
            text-decoration: none;
            margin-right: 15px;
        }
        .nav-link:hover {
            text-decoration: underline;
        }
        
        .container-info {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .info-item {
            font-size: 14px;
        }
        .info-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        
        .logs-container {
            background: #1e1e1e;
            color: #f8f8f2;
            border-radius: 4px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #1e3d6f;
        }
        
        .actions {
            margin-bottom: 20px;
        }
        
        .empty-logs {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Container Logs</h1>
            <nav>
                <a href="/" class="nav-link">← Home</a>
                <a href="/containers" class="nav-link">Containers</a>
            </nav>
        </div>

        {% if container %}
        <div class="container-info">
            <div class="info-item">
                <div class="info-label">Container Name:</div>
                <div>{{ container.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Image:</div>
                <div>{{ container.image }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Status:</div>
                <div>{{ container.status }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Container ID:</div>
                <div>{{ container.id }}</div>
            </div>
        </div>
        {% endif %}

        <div class="actions">
            <button onclick="refreshLogs()" class="btn">🔄 Refresh Logs</button>
            <a href="/containers/{{ container.id }}/env" class="btn">⚙️ Environment</a>
        </div>

        <div class="logs-container" id="logsContainer">
            {% if logs and logs.strip() %}
                {{ logs }}
            {% else %}
                <div class="empty-logs">No logs available</div>
            {% endif %}
        </div>
    </div>

    <script>
        function refreshLogs() {
            // Simple page refresh for now
            window.location.reload();
        }
        
        // Auto-scroll to bottom of logs
        const logsContainer = document.getElementById('logsContainer');
        logsContainer.scrollTop = logsContainer.scrollHeight;
        
        // Auto-refresh every 10 seconds
        setInterval(refreshLogs, 10000);
    </script>
</body>
</html>
