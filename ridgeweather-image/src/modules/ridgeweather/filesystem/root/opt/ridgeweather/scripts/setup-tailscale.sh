#!/bin/bash

# RidgeWeather Tailscale Setup Script
# This script connects the Pi to Tailscale when internet is available

set -e

function print_err() {
    echo "$@" >&2
}

function check_network() {
    timeout 5 bash -c 'cat < /dev/null > /dev/tcp/*******/53' 2>/dev/null
}

function setup_tailscale() {
    local auth_key_file="/opt/ridgeweather/tailscale-auth-key"
    
    # Check if we have internet connectivity
    if ! check_network; then
        print_err "No internet connection, skipping Tailscale setup"
        return 1
    fi
    
    # Check if already connected to Tailscale
    if tailscale status --json >/dev/null 2>&1; then
        local status=$(tailscale status --json | jq -r '.BackendState')
        if [[ "$status" == "Running" ]]; then
            print_err "Already connected to Tailscale"
            return 0
        fi
    fi
    
    # Check if we have an auth key
    if [[ ! -f "$auth_key_file" ]]; then
        print_err "No Tailscale auth key found at $auth_key_file"
        print_err "To enable Tailscale, create this file with your pre-auth key"
        return 1
    fi
    
    local auth_key=$(cat "$auth_key_file" | tr -d '\n\r ')
    
    if [[ -z "$auth_key" ]]; then
        print_err "Empty Tailscale auth key"
        return 1
    fi
    
    print_err "Connecting to Tailscale..."
    
    # Connect to Tailscale with the auth key
    if tailscale up --authkey="$auth_key" --ssh --accept-routes; then
        print_err "Successfully connected to Tailscale"
        
        # Enable SSH access through Tailscale
        print_err "Tailscale SSH enabled"
        
        # Remove the auth key file for security
        rm -f "$auth_key_file"
        print_err "Auth key file removed for security"
        
        return 0
    else
        print_err "Failed to connect to Tailscale"
        return 1
    fi
}

# Main execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    setup_tailscale
fi
