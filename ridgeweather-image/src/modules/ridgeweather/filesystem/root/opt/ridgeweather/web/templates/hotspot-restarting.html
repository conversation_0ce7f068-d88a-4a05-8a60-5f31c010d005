<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RidgeWeather - Connecting to WiFi</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
        }
        .header h1 {
            color: #2c5aa0;
            margin: 0 0 20px 0;
            font-size: 2em;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2c5aa0;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .message {
            font-size: 1.1em;
            margin: 20px 0;
            color: #666;
        }
        .instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 20px;
            margin: 30px 0;
            color: #0c5460;
        }
        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }
    </style>
    <script>
        // Auto-refresh every 5 seconds to check connection status
        setTimeout(function() {
            window.location.href = '/';
        }, 5000);
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RidgeWeather</h1>
        </div>

        <div class="spinner"></div>

        <div class="message">
            <h2>Connecting to WiFi...</h2>
            <p>Please wait while RidgeWeather connects to your WiFi network.</p>
        </div>

        <div class="instructions">
            <p><strong>What's happening:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <li>Testing your WiFi credentials</li>
                <li>Connecting to your network</li>
                <li>Setting up network services</li>
            </ul>
            <p style="margin-top: 20px;">
                <strong>If successful:</strong> RidgeWeather will be available at 
                <strong>http://ridgeweather.local/</strong>
            </p>
            <p>
                <strong>If connection fails:</strong> The hotspot will restart automatically 
                for you to try again.
            </p>
        </div>

        <div class="footer">
            <p>This page will automatically refresh in a few seconds...</p>
        </div>
    </div>
</body>
</html>
