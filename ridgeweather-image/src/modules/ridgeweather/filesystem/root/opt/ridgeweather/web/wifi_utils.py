#!/usr/bin/env python3
"""
WiFi utilities for RidgeWeather
Based on adsb-feeder WiFi management
"""

import os
import subprocess
import time
import sys
import traceback

def print_err(*args, **kwargs):
    """Print to stderr for logging"""
    print(*args, file=sys.stderr, **kwargs)

class WiFiManager:
    def __init__(self, interface="wlan0"):
        self.interface = interface
        self.ssids = []

        # Detect base OS (exactly like adsb-feeder)
        if os.path.exists("/boot/dietpi"):
            self.baseos = "dietpi"
        elif os.path.exists("/etc/rpi-issue"):
            self.baseos = "raspbian"
        else:
            print_err("unknown baseos - no idea what to do")
            self.baseos = "unknown"
    
    def scan_ssids(self):
        """Scan for available WiFi networks (exactly like adsb-feeder)"""
        try:
            if self.baseos == "raspbian":
                try:
                    output = subprocess.run(
                        "nmcli --terse --fields SSID dev wifi",
                        shell=True,
                        capture_output=True,
                    )
                except subprocess.CalledProcessError as e:
                    print_err(f"error scanning for SSIDs: {e}")
                    return

                ssids = []
                for line in output.stdout.decode().split("\n"):
                    if line and line != "--" and line not in ssids:
                        ssids.append(line)
            elif self.baseos == "dietpi":
                ssids = self.wpa_cli_scan()

            if len(ssids) > 0:
                print_err(f"found SSIDs: {ssids}")
                self.ssids = ssids
            else:
                print_err("no SSIDs found")

        except Exception as e:
            print_err(f"ERROR in scan_ssids(): {e}")

    def wpa_cli_scan(self):
        """Scan using wpa_cli (for DietPi)"""
        ssids = []
        try:
            proc = subprocess.Popen(
                ["wpa_cli", f"-i{self.interface}"],
                stderr=subprocess.STDOUT,
                stdout=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
            )
            os.set_blocking(proc.stdout.fileno(), False)

            output = ""

            startTime = time.time()
            while time.time() - startTime < 15:
                line = proc.stdout.readline()
                if not line:
                    time.sleep(0.01)
                    continue

                output += line
                if line.count("Interactive mode"):
                    proc.stdin.write("scan\n")
                    proc.stdin.flush()
                if line.count("CTRL-EVENT-SCAN-RESULTS"):
                    proc.stdin.write("scan_results\n")
                    proc.stdin.flush()
                    break

            startTime = time.time()
            while time.time() - startTime < 1:
                line = proc.stdout.readline()
                if not line:
                    time.sleep(0.01)
                    continue

                output += line
                if line.count("\t"):
                    fields = line.rstrip("\n").split("\t")
                    if len(fields) == 5:
                        ssids.append(fields[4])

        except:
            print_err(f"ERROR in wpa_cli_scan(), wpa_cli ouput: {output}")
        finally:
            if proc:
                proc.terminate()

        return ssids
    
    def wifi_connect(self, ssid, password, country_code="PA"):
        """Connect to WiFi network (exactly like adsb-feeder)"""
        success = False

        if self.baseos == "dietpi":
            self.dietpi_add_wifi_hotplug()

            # test wifi
            success = self.writeWpaConf(
                ssid=ssid, passwd=password, path="/etc/wpa_supplicant/wpa_supplicant.conf", country_code=country_code
            )
            if success:
                if not self.wait_wpa_supplicant():
                    print_err("ERROR: wait_wpa_supplicant didn't work, restarting networking and re-trying")
                    self.restart_networking_noblock()
                    self.wait_wpa_supplicant()

                success = self.wpa_cli_reconfigure()

        elif self.baseos == "raspbian":
            # do a wifi scan to ensure the following connect works
            self.scan_ssids()
            # try for a while because it takes a bit for NetworkManager to come back up
            startTime = time.time()
            while time.time() - startTime < 20:
                try:
                    result = subprocess.run(
                        [
                            "nmcli",
                            "d",
                            "wifi",
                            "connect",
                            f"{ssid}",
                            "password",
                            f"{password}",
                            "ifname",
                            f"{self.interface}",
                        ],
                        capture_output=True,
                        timeout=20.0,
                    )
                except subprocess.SubprocessError as e:
                    # something went wrong
                    output = ""
                    if e.stdout:
                        output += e.stdout.decode()
                    if e.stderr:
                        output += e.stderr.decode()
                else:
                    output = result.stdout.decode() + result.stderr.decode()

                success = "successfully activated" in output

                if success:
                    break
                else:
                    # just to safeguard against super fast spin, sleep a bit
                    print_err(f"failed to connect to '{ssid}': {output}")
                    time.sleep(2)
                    continue

        return success

    def writeWpaConf(self, ssid, passwd, path, country_code="PA"):
        """Write wpa_supplicant configuration"""
        try:
            netblocks = {}
            netblocks[ssid] = f"""
network={{
    ssid="{ssid}"
    psk="{passwd}"
    priority=1
}}
"""

            with open(path, "w") as conf:
                conf.write(
                    f"""
# WiFi country code, set here in case the access point does not send one
country={country_code}
# Grant all members of group "netdev" permissions to configure WiFi, e.g. via wpa_cli or wpa_gui
ctrl_interface=DIR=/run/wpa_supplicant GROUP=netdev
# Allow wpa_cli/wpa_gui to overwrite this config file
update_config=1
# disable p2p as it can cause errors
p2p_disabled=1
"""
                )
                for k in netblocks.keys():
                    conf.write(netblocks[k])
        except:
            print_err(traceback.format_exc())
            print_err(f"ERROR when writing wpa supplicant config to {path}")
            return False
        print_err("wpa supplicant config written to " + path)
        return True

    def wait_wpa_supplicant(self):
        """Wait for wpa_supplicant to be ready"""
        startTime = time.time()
        while time.time() - startTime < 60:
            try:
                result = subprocess.run(
                    ["wpa_cli", f"-i{self.interface}", "status"],
                    capture_output=True,
                    timeout=2.0,
                )
                if result.returncode == 0:
                    return True
            except:
                pass
            time.sleep(1)
        return False

    def restart_networking_noblock(self):
        """Restart networking service"""
        subprocess.run("systemctl restart --no-block networking.service", shell=True)

    def wpa_cli_reconfigure(self):
        """Reconfigure wpa_supplicant"""
        connected = False
        output = ""
        proc = None

        try:
            proc = subprocess.Popen(
                ["wpa_cli", f"-i{self.interface}"],
                stderr=subprocess.STDOUT,
                stdout=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
            )
            os.set_blocking(proc.stdout.fileno(), False)

            startTime = time.time()
            reconfigureSent = False
            reconfigured = False
            while time.time() - startTime < 20:
                line = proc.stdout.readline()
                if not line:
                    time.sleep(0.01)
                    continue

                output += line
                if not reconfigureSent and line.startswith(">"):
                    proc.stdin.write("reconfigure\n")
                    proc.stdin.flush()
                    reconfigureSent = True
                if "reconfigure" in line:
                    reconfigured = True
                if reconfigured and "CTRL-EVENT-CONNECTED" in line:
                    connected = True
                    break
        except:
            print_err(traceback.format_exc())
        finally:
            if proc:
                proc.terminate()

        if not connected:
            print_err(f"Couldn't connect after wpa_cli reconfigure: ouput: {output}")

        return connected

    def dietpi_add_wifi_hotplug(self):
        """Enable dietpi wifi hotplug"""
        changedInterfaces = False
        with open("/etc/network/interfaces", "r") as current, open("/etc/network/interfaces.new", "w") as update:
            lines = current.readlines()
            for line in lines:
                if line.startswith("#") and "allow-hotplug" in line and self.interface in line:
                    changedInterfaces = True
                    update.write(f"allow-hotplug {self.interface}\n")
                else:
                    update.write(f"{line}")

        if changedInterfaces:
            print_err(f"uncommenting allow-hotplug for {self.interface}")
            os.rename("/etc/network/interfaces.new", "/etc/network/interfaces")
            self.restart_networking_noblock()
        else:
            os.remove("/etc/network/interfaces.new")
