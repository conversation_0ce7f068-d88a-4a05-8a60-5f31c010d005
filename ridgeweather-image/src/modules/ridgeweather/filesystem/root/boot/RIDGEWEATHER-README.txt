RidgeWeather Raspberry Pi Image
================================

Welcome to RidgeWeather! A Raspberry Pi platform with WiFi hotspot, Tailscale connectivity,
and Docker container management.

FIRST BOOT:
-----------
1. Insert the SD card into your Raspberry Pi and power it on
2. Wait 2-3 minutes for the system to boot
3. Look for a WiFi hotspot named "ridgeweather-feeder" (open, no password)
4. Connect to the hotspot and open a web browser
5. You'll be automatically redirected to the WiFi setup page
6. Enter your WiFi credentials to connect to your network

AFTER WIFI SETUP:
-----------------
Once connected to your WiFi network, you can access RidgeWeather at:
http://ridgeweather.local/

The web interface provides:
- System information and status
- Docker container management
- Container deployment from templates
- Container logs and environment variable management

TAILSCALE SETUP (OPTIONAL):
---------------------------
To enable Tailscale connectivity for remote access:

1. Generate a pre-auth key in your Tailscale admin console
2. SSH into the Pi or access it locally
3. Create the file: /opt/ridgeweather/tailscale-auth-key
4. Put your pre-auth key in that file (one line, no extra spaces)
5. Restart the Pi or run: sudo systemctl start ridgeweather-tailscale.service

Once connected, you can access the Pi remotely via Tailscale SSH.

DOCKER CONTAINER MANAGEMENT:
----------------------------
RidgeWeather includes Docker for running containerized applications:

- Deploy containers from built-in templates (nginx, redis, postgres)
- Manage container lifecycle (start/stop/restart/remove)
- View container logs in real-time
- Configure environment variables
- Access via the web interface at http://ridgeweather.local/containers

MANUAL WIFI CONFIGURATION:
--------------------------
If you need to configure WiFi manually, you can edit the wpa_supplicant configuration:

1. Mount the SD card on a computer
2. Edit the file: /etc/wpa_supplicant/wpa_supplicant.conf
3. Add your network configuration:

network={
    ssid="YourNetworkName"
    psk="YourPassword"
    priority=1
}

DEFAULT CREDENTIALS:
-------------------
- Username: pi
- Password: ridgeweather
- SSH is enabled by default

SUPPORT:
--------
For more information, visit: https://github.com/tomsuys/ridgeweather

RidgeWeather v1.0.0
