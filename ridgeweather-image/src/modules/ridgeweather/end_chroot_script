#!/usr/bin/env bash
# RidgeWeather module post-installation script
set -x
set -e

source /common.sh

# Enable services
systemctl enable ridgeweather-web.service
systemctl enable ridgeweather-hotspot.service
systemctl enable ridgeweather-tailscale.service
systemctl enable avahi-daemon.service
systemctl enable docker.service

# Final configuration and cleanup
echo "RidgeWeather module installation completed"
