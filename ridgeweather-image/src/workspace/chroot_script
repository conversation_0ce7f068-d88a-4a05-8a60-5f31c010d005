#!/usr/bin/env bash
set -x
set -e
# Defined execution order: [('base', 'start'), ('network', 'start'), ('network', 'end'), ('ridgeweather', 'start'), ('ridgeweather', 'end'), ('base', 'end')]
# With meta modules order: [('base', 'start'), ('network', 'start'), ('network', 'end'), ('ridgeweather', 'start'), ('ridgeweather', 'end'), ('base', 'end')]
# start_base
execute_chroot_script '/CustomPiOS/modules/base' '/CustomPiOS/modules/base/start_chroot_script'
# start_network
execute_chroot_script '/CustomPiOS/modules/network' '/CustomPiOS/modules/network/start_chroot_script'
# end_network
# start_ridgeweather
execute_chroot_script '/distro/modules/ridgeweather' '/distro/modules/ridgeweather/start_chroot_script'
# end_ridgeweather
execute_chroot_script '/distro/modules/ridgeweather' '/distro/modules/ridgeweather/end_chroot_script'
# end_base
execute_chroot_script '/CustomPiOS/modules/base' '/CustomPiOS/modules/base/end_chroot_script'
