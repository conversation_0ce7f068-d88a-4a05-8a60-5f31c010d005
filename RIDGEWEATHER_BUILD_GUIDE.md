# Building RidgeWeather Image for Raspberry Pi

This guide documents the complete process for building the RidgeWeather custom Raspberry Pi image using Docker on macOS.

## Overview

RidgeWeather is a custom Raspberry Pi image based on the adsb-feeder-image architecture that provides:
- **Automatic WiFi Hotspot**: Creates `ridgeweather` hotspot when WiFi connection fails
- **Web Configuration Interface**: Simple web UI for system management at `ridgeweather.local`
- **Tailscale Integration**: Built-in VPN connectivity with web-based authentication
- **Docker Container Management**: Web interface for managing Docker containers
- **Simplified Architecture**: Removed deployment functionality, focused on core features

## Prerequisites

### System Requirements
- **macOS** (tested on macOS Sequoia)
- **Docker Desktop** (must be running with privileged access)
- **50+ GB free disk space** (for build process and images)
- **Stable internet connection** (downloads ~400MB base image)

### Required Tools Installation

```bash
# Install Docker Desktop (if not already installed)
brew install --cask docker

# Start Docker Desktop and ensure it's running
open -a "Docker Desktop"

# Verify Docker is running
docker ps
```

## Project Structure

The RidgeWeather project follows the CustomPiOS module structure:

```
ridgeweather-image/
├── src/
│   ├── config                    # Main configuration file
│   ├── custompios_path          # Points to ../../CustomPiOS/src
│   ├── modules/
│   │   └── ridgeweather/
│   │       ├── start_chroot_script
│   │       ├── filesystem/
│   │       │   └── root/
│   │       │       ├── opt/ridgeweather/
│   │       │       │   ├── web/           # Flask web application
│   │       │       │   ├── scripts/      # System scripts
│   │       │       │   └── accesspoint/  # Hotspot configuration
│   │       │       ├── etc/systemd/system/ # Service files
│   │       │       └── boot/             # Boot configuration
│   │       └── end_chroot_script
│   └── workspace/               # Build output directory
└── CustomPiOS/                  # CustomPiOS framework
```

## Build Process

### Step 1: Verify Project Setup

Ensure the project structure is correct:

```bash
# Navigate to the project root
cd /path/to/SayWeather_ridge

# Verify CustomPiOS is available
ls -la CustomPiOS/src

# Verify ridgeweather-image structure
ls -la ridgeweather-image/src/
```

### Step 2: Check Configuration

Verify the build configuration:

```bash
# Check the main config file
cat ridgeweather-image/src/config

# Verify CustomPiOS path
cat ridgeweather-image/src/custompios_path
```

Expected configuration values:
- `DIST_NAME=ridgeweather-dist`
- `DIST_VERSION=1.0.0`
- `BASE_ARCH=arm64`
- `BASE_BOARD=raspberrypiarm64`
- `MODULES=base(network,ridgeweather)`

### Step 3: Execute Build

Run the Docker-based build process:

```bash
# Navigate to the source directory
cd ridgeweather-image/src

# Clean any previous build artifacts (optional)
rm -rf workspace

# Execute the build with Docker
docker run --rm --privileged --device-cgroup-rule='c *:* rmw' \
  -v $(pwd):/distro ghcr.io/guysoft/custompios:devel build --download
```

### Build Process Details

The build process performs the following steps:

1. **Download Base Image**: Downloads official Raspberry Pi OS ARM64 Lite (~400MB)
2. **Configure Environment**: Sets up ARM64 architecture for Pi 5 compatibility
3. **Install Base Packages**: Installs Docker, networking tools, system utilities
4. **Apply Network Module**: Configures WiFi, hotspot, and network management
5. **Apply RidgeWeather Module**: Installs custom web interface and services
6. **Configure Services**: Sets up systemd services for web interface and hotspot
7. **Create Final Image**: Generates ready-to-flash image file

## Build Output

### Successful Build Results

After a successful build (typically 15-30 minutes), you'll find:

```
ridgeweather-image/src/workspace/
├── 2025-05-13-ridgeweather-dist-default-bookworm-arm64-lite-1.0.0.img  # Final image (~3.4GB)
├── 2025-05-13-ridgeweather-dist-default-bookworm-arm64-lite-1.0.0.zip  # Compressed (~779MB)
└── build artifacts and logs
```

### File Verification

```bash
# Check file sizes
ls -lh ridgeweather-image/src/workspace/*.img *.zip

# Expected output:
# -rwxrwxrwx  3.4G  ...ridgeweather-dist-default-bookworm-arm64-lite-1.0.0.img
# -rwxrwxrwx  779M  ...ridgeweather-dist-default-bookworm-arm64-lite-1.0.0.zip
```

## Flashing the Image

### Install Raspberry Pi Imager

```bash
brew install --cask raspberry-pi-imager
```

### Flash to SD Card

1. **Insert microSD card** (32GB+ recommended)
2. **Open Raspberry Pi Imager**
3. **Choose "Use custom image"**
4. **Navigate to**: `ridgeweather-image/src/workspace/` directory
5. **Select**: `2025-05-13-ridgeweather-dist-default-bookworm-arm64-lite-1.0.0.img`
6. **Select your SD card**
7. **Flash the image**

## Testing the Image

### Initial Boot Test

1. **Insert SD card** into Raspberry Pi 5
2. **Connect power** (no network needed initially)
3. **Wait 2-3 minutes** for first boot
4. **Look for WiFi hotspot**: `ridgeweather`

### Hotspot Functionality Test

1. **Connect to hotspot**: `ridgeweather` (no password)
2. **Open browser**: Navigate to `http://***********`
3. **Configure WiFi**: Enter your network credentials
4. **Test web interface**: Verify all pages load correctly

### Network Mode Test

1. **After WiFi configuration**: Pi should connect to your network
2. **Access via hostname**: Navigate to `http://ridgeweather.local`
3. **Test Tailscale**: Use the web interface to enter a pre-auth key
4. **Test containers**: Verify Docker container management works

## Key Features

### Web Interface Features

- **System Information**: Hostname, uptime, Tailscale status
- **Tailscale Authentication**: Web form for pre-auth key entry
- **Container Management**: Start, stop, restart, view logs, manage environment variables
- **Responsive Design**: Works on desktop and mobile devices

### System Services

- `ridgeweather-web.service`: Main web interface (port 80)
- `ridgeweather-hotspot.service`: Hotspot management
- `ridgeweather-tailscale.service`: Tailscale VPN connectivity

### Default Credentials

- **Username**: `pi`
- **Password**: `ridgeweather`
- **SSH**: Enabled by default
- **Hostname**: `ridgeweather.local`

## Troubleshooting

### Common Build Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| `build failed, unmounting image...` | Loop device mounting issue | Use `--device-cgroup-rule='c *:* rmw'` flag |
| `No BASE_BOARD defined` | Configuration issue | Verify `config` file has `BASE_BOARD=raspberrypiarm64` |
| `Docker permission errors` | Insufficient privileges | Ensure `--privileged` flag is used |
| `CustomPiOS path not found` | Wrong path in custompios_path | Verify path points to `../../CustomPiOS/src` |

### Build Verification

Successful build indicators:
- Return code 0 from Docker command
- `.img` file (~3.4GB) in `workspace/` directory
- `.zip` compressed version (~779MB)
- No error messages in build output
- ARM64 architecture confirmed in build logs

### Runtime Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| Hotspot not appearing | Service not started | Check `systemctl status ridgeweather-hotspot` |
| Web interface not accessible | Service not running | Check `systemctl status ridgeweather-web` |
| Tailscale not connecting | Auth key issue | Use web interface to re-enter pre-auth key |
| Container management fails | Docker not running | Check `systemctl status docker` |

## Customization

### Modifying the Web Interface

Web interface files are located in:
```
ridgeweather-image/src/modules/ridgeweather/filesystem/root/opt/ridgeweather/web/
├── app.py              # Main Flask application
├── docker_manager.py   # Docker container management
├── templates/          # HTML templates
│   ├── index.html      # Home page
│   ├── containers.html # Container management
│   └── ...
```

### Adding New Services

1. Add service files to: `ridgeweather-image/src/modules/ridgeweather/filesystem/root/etc/systemd/system/`
2. Enable services in: `ridgeweather-image/src/modules/ridgeweather/start_chroot_script`
3. Rebuild the image

### Configuration Changes

Modify `ridgeweather-image/src/config` for:
- Version numbers
- Default passwords
- Image naming
- Module dependencies

## Resources

- [CustomPiOS Documentation](https://github.com/guysoft/CustomPiOS)
- [Docker CustomPiOS Image](https://github.com/guysoft/CustomPiOS/pkgs/container/custompios)
- [Raspberry Pi OS Images](https://downloads.raspberrypi.org/raspios_lite_arm64/images/)
- [Original adsb-feeder-image](https://github.com/dirkhh/adsb-feeder-image)

## Build History

- **v1.0.0**: Initial RidgeWeather release
  - Removed deployment functionality
  - Added Tailscale web authentication
  - Simplified container management
  - Enhanced web interface with flash messages

  
