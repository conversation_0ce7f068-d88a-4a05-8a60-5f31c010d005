#!/bin/bash

# Build and push script for Weather API Container
# Usage: ./build-and-push.sh [tag]

set -e

# Default tag
TAG=${1:-latest}
IMAGE_NAME="ghcr.io/devtomsuys/weather-api-container"

echo "Building Weather API Container..."
echo "Image: $IMAGE_NAME:$TAG"

# Build the Docker image
docker build -t "$IMAGE_NAME:$TAG" .

echo "Build completed successfully!"

# Ask if user wants to push
read -p "Do you want to push the image to the registry? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Pushing image to registry..."
    docker push "$IMAGE_NAME:$TAG"
    echo "Push completed successfully!"
    
    echo ""
    echo "Docker run command for deployment:"
    echo "docker run -d --name weather-api-container \\"
    echo "  -p 8000:8000 \\"
    echo "  -e STATION_ID=185807 \\"
    echo "  -e TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c \\"
    echo "  -e AIRPORT_ADVISORY=\"Ridge Landing Airpark automated advisory\" \\"
    echo "  -e TTS_VOICE=en-US-JennyNeural \\"
    echo "  -e WORD_GAP=-0.8 \\"
    echo "  -e PHRASE_GAP=0.2 \\"
    echo "  -e UPDATE_INTERVAL_SECONDS=120 \\"
    echo "  -e BASE_URL=https://your-domain.com \\"
    echo "  --restart unless-stopped \\"
    echo "  $IMAGE_NAME:$TAG"
else
    echo "Skipping push to registry."
fi

echo ""
echo "To test locally:"
echo "docker-compose up -d"
echo ""
echo "API endpoints will be available at:"
echo "- Health check: http://localhost:8000/health"
echo "- AWOS MP3: http://localhost:8000/4FL5/awos.mp3"
echo "- Twilio TwiML: http://localhost:8000/4FL5/twiml"
echo "- Weather data: http://localhost:8000/4FL5/weather"
