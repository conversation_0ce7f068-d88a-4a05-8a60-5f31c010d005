#!/usr/bin/env python3
"""
AWOS Text-to-Speech Generator

Generates AWOS audio from JSON weather data.
"""

import json
import os
import time
from pydub import AudioSegment
from audio_components import get_audio_mapping, DIGITS
import logging

logger = logging.getLogger(__name__)

class AWOSGenerator:
    def __init__(self, audio_dir="audio_components", word_gap=-0.85, phrase_gap=0.4):
        self.audio_dir = audio_dir
        self.word_gap = word_gap
        self.phrase_gap = phrase_gap
        self._audio_cache = {}

        # Get component to filename mapping from audio_components module
        self._components = get_audio_mapping()

        # Create digit to word mapping from DIGITS list
        self._digits = {str(i): DIGITS[i] for i in range(10)}

    def warm_cache(self):
        """Pre-load all audio files into cache for optimal performance."""
        logger.info("Warming audio cache...")
        start_time = time.perf_counter()

        for filename in self._components.values():
            if filename not in self._audio_cache:
                filepath = os.path.join(self.audio_dir, filename)
                if os.path.exists(filepath):
                    self._audio_cache[filename] = AudioSegment.from_file(filepath)

        warm_time = (time.perf_counter() - start_time) * 1000
        logger.info(f"Cache warmed: {len(self._audio_cache)} files loaded in {warm_time:.1f}ms")
        return len(self._audio_cache)
    
    def generate(self, json_data, output_file):
        """Generate AWOS audio from JSON data."""
        start_time = time.perf_counter()
        
        # Parse JSON
        if isinstance(json_data, str):
            data = json.loads(json_data)
        else:
            data = json_data
        
        # Build audio sequence
        sequence = []

        # Airport name + automated advisory (configurable)
        from audio_components import AIRPORT_ADVISORY
        sequence.append(AIRPORT_ADVISORY)
        sequence.append("PHRASE_BREAK")

        # Wind
        sequence.extend(self._parse_wind(data))
        sequence.append("PHRASE_BREAK")

        # Temperature
        sequence.append("Temperature")
        sequence.extend(self._parse_digits(data['temperature']))
        sequence.append("PHRASE_BREAK")

        # Dew point
        sequence.append("Dew Point")
        sequence.extend(self._parse_digits(data['dew_point']))
        sequence.append("PHRASE_BREAK")

        # Altimeter
        sequence.append("Altimeter")
        sequence.extend(self._parse_digits(data['altimeter']))
        sequence.append("PHRASE_BREAK")

        # Density altitude
        sequence.append("Density altitude")
        sequence.extend(self._parse_density_altitude(data['density_altitude']))
        
        # Build audio
        combined = AudioSegment.empty()

        for i, component in enumerate(sequence):
            if component == "PHRASE_BREAK":
                if combined and self.phrase_gap > 0:  # Only add silence if phrase_gap > 0
                    combined += AudioSegment.silent(duration=int(self.phrase_gap * 1000))
            else:
                # Load audio file
                filename = self._components[component]
                if filename not in self._audio_cache:
                    filepath = os.path.join(self.audio_dir, filename)
                    self._audio_cache[filename] = AudioSegment.from_file(filepath)

                audio = self._audio_cache[filename]._spawn(self._audio_cache[filename].raw_data)

                # Apply word gap (clip end) unless it's the last component or followed by phrase break with phrase_gap=0
                should_clip = (i < len(sequence) - 1 and
                              not (sequence[i + 1] == "PHRASE_BREAK" and self.phrase_gap == 0))

                if should_clip:
                    clip_ms = int(abs(self.word_gap) * 1000)
                    if len(audio) > clip_ms:
                        audio = audio[:-clip_ms]

                combined += audio
        
        # Export
        combined.export(output_file, format="wav")
        
        end_time = time.perf_counter()
        generation_time = (end_time - start_time) * 1000
        
        return {
            "file": output_file,
            "duration": len(combined) / 1000,
            "generation_time": generation_time,
            "components": len([c for c in sequence if c != "PHRASE_BREAK"])
        }
    
    def _parse_wind(self, data):
        """Parse wind conditions."""
        components = ["Wind"]
        
        wind_speed = data.get('wind_speed')
        wind_direction = data.get('wind_direction')
        wind_variable = data.get('wind_variable', False)
        wind_gust = data.get('wind_gust')
        wind_var_min = data.get('wind_var_min')
        wind_var_max = data.get('wind_var_max')
        
        # Calm
        if wind_speed == 'calm' or (isinstance(wind_speed, int) and wind_speed <= 2):
            components.append("calm")
            return components
        
        # Variable light wind
        if isinstance(wind_speed, int) and wind_speed <= 6 and wind_variable:
            components.append("variable")
            components.append("at")
            components.extend(self._parse_digits(wind_speed))
            return components
        
        # Wind direction
        if wind_direction and wind_direction != 'variable':
            direction_str = f"{wind_direction:03d}"
            components.extend(self._parse_digits(direction_str))
        else:
            components.append("variable")
        
        # Wind speed
        components.append("at")
        components.extend(self._parse_digits(wind_speed))

        # Gusts
        if wind_gust is not None:
            components.append("gusting")
            components.extend(self._parse_digits(wind_gust))
        
        # Variable range
        if wind_var_min is not None and wind_var_max is not None:
            components.append("variable")
            components.append("between")
            min_str = f"{wind_var_min:03d}"
            components.extend(self._parse_digits(min_str))
            components.append("and")
            max_str = f"{wind_var_max:03d}"
            components.extend(self._parse_digits(max_str))
        
        return components
    
    def _parse_digits(self, value):
        """Parse digits individually, handling negative numbers."""
        if value is None:
            return []

        components = []
        value_str = str(value)

        # Handle negative numbers
        if value_str.startswith('-'):
            components.append("minus")
            value_str = value_str[1:]  # Remove the minus sign

        # Parse each digit
        for digit in value_str:
            if digit.isdigit():
                components.append(self._digits[digit])

        return components

    def _parse_positive_digits(self, value):
        """Parse digits individually for positive numbers only."""
        if value is None:
            return []

        components = []
        for digit in str(value):
            if digit.isdigit():
                components.append(self._digits[digit])

        return components
    
    def _parse_density_altitude(self, altitude):
        """Parse density altitude with thousands/hundreds, handling negative values.
        Rounds to nearest 100 feet per aviation standards."""
        if altitude is None:
            return []

        components = []

        # Handle negative altitude
        is_negative = altitude < 0
        if is_negative:
            components.append("minus")
            altitude = abs(altitude)

        # Round to nearest 100 feet per aviation standards
        altitude = round(altitude / 100) * 100

        if altitude >= 1000:
            thousands = altitude // 1000
            components.extend(self._parse_positive_digits(thousands))
            components.append("thousand")

            remainder = altitude % 1000
            if remainder >= 100:
                hundreds = remainder // 100
                components.extend(self._parse_positive_digits(hundreds))
                components.append("hundred")
        else:
            components.extend(self._parse_positive_digits(altitude))

        return components
