# Coolify Deployment Guide

## Quick Deployment Steps

1. **Create New Service in Coolify**
   - Go to your Coolify dashboard
   - Click "New Service" → "Docker Image"
   - Name: `weather-api-container`

2. **Configure Docker Image**
   - Image: `ghcr.io/devtomsuys/weather-api-container:latest`
   - Port: `8000`

3. **Environment Variables**
   Add these environment variables in Coolify:

   ```
   STATION_ID=185807
   TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c
   AIRPORT_ADVISORY=Ridge Landing Airpark automated advisory
   TTS_VOICE=en-US-JennyNeural
   WORD_GAP=-0.8
   PHRASE_GAP=0.2
   UPDATE_INTERVAL_SECONDS=120
   HOST=0.0.0.0
   PORT=8000
   BASE_URL=https://awos.skytraces.com
   ```

   **Note**: The BASE_URL is already set to your Coolify-generated domain.

4. **Health Check Configuration**
   - Health Check Path: `/health`
   - Health Check Port: `8000`
   - Health Check Interval: `30s`

5. **Deploy**
   - Click "Deploy"
   - Wait for the container to start (may take 1-2 minutes for initial audio download)

## Verification

After deployment, test these endpoints:

- **Health Check**: `https://awos.skytraces.com/health`
- **Weather Data**: `https://awos.skytraces.com/4FL5/weather`
- **AWOS Audio**: `https://awos.skytraces.com/4FL5/awos.mp3`
- **Twilio TwiML**: `https://awos.skytraces.com/4FL5/twiml`

## Twilio Configuration

1. In your Twilio Console, configure your phone number webhook:
   - Webhook URL: `https://awos.skytraces.com/4FL5/twiml`
   - HTTP Method: `GET` or `POST` (both supported)

2. Test by calling your Twilio number - it should play the current weather report.

## Monitoring

- Check container logs in Coolify for any issues
- The service updates weather data every 2 minutes
- Health endpoint shows last update time and file status

## Troubleshooting

- If no audio is available initially, wait 2-3 minutes for first weather update
- Check that BASE_URL environment variable matches your actual domain
- Verify WeatherFlow API credentials are correct
- Check container logs for detailed error information
