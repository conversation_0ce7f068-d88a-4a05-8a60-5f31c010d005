# Use Python slim base image for optimal size
FROM python:3.11-slim-bookworm

# Install system dependencies for audio processing and FFmpeg
RUN apt-get update && apt-get install -y \
    ffmpeg \
    sox \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create working directory
WORKDIR /app

# Copy requirements and install Python packages
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY main.py /app/main.py
COPY weather_service.py /app/weather_service.py
COPY weather_fetcher.py /app/weather_fetcher.py
COPY awos_generator.py /app/awos_generator.py
COPY audio_components.py /app/audio_components.py
COPY download_audio.py /app/download_audio.py

# Create directories for runtime data
RUN mkdir -p /app/audio_components /app/output

# Create non-root user for security
RUN useradd -r -u 1000 weatherapi && \
    chown -R weatherapi:weatherapi /app

# Switch to non-root user
USER weatherapi

# Expose port
EXPOSE 8000

# Environment variables with defaults
ENV HOST=0.0.0.0
ENV PORT=8000
ENV STATION_ID=185807
ENV TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c
ENV AIRPORT_ADVISORY="Ridge Landing Airpark automated advisory"
ENV TTS_VOICE=en-US-JennyNeural
ENV WORD_GAP=-0.8
ENV PHRASE_GAP=0.2
ENV UPDATE_INTERVAL_SECONDS=120
ENV BASE_URL=https://awos.skytraces.com

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the FastAPI application
CMD ["python", "main.py"]
