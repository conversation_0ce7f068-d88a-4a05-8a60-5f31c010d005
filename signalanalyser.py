import numpy as np
import matplotlib.pyplot as plt
from scipy.io import wavfile
from scipy.signal import welch
from matplotlib.ticker import Percent<PERSON><PERSON>atter

def plot_power_probability_distribution(file_path='r45.wav', start_time_s=0, end_time_s=10):
    """
    Analyzes a specific segment of a WAV file to plot a probability
    distribution of signal power and a plot of power over time with hysteresis thresholding.

    The audio is processed in high-resolution chunks of 1024 samples. For each chunk, 
    the power within the 200-3500 Hz range is calculated in a way that is independent
    of the chunk size. Two graphs are generated:
    1. A high-resolution histogram of the power distribution on a logarithmic scale.
    2. A plot of power over time, with detected signal regions highlighted using hysteresis.

    Args:
        file_path (str): The path to the input WAV file.
        start_time_s (int): The start time of the segment to analyze in seconds.
        end_time_s (int): The end time of the segment to analyze in seconds.
    """
    # --- 1. Constants and Configuration ---
    CHUNK_SIZE_SAMPLES = 1024 # Smaller chunk size for higher resolution
    FREQ_MIN_HZ = 200         # Minimum frequency of interest
    FREQ_MAX_HZ = 3500        # Maximum frequency of interest
    HIST_BINS = 400           # More bins for higher resolution histogram
    
    # NOTE: These thresholds are now based on power units (Amplitude Units Squared) that are
    # independent of the CHUNK_SIZE_SAMPLES. You may need to adjust these values
    # based on the new power levels shown in the graphs.
    THIGH = 50000              # High threshold for hysteresis
    TLOW = 100            # Low threshold for hysteresis

    # --- 2. Load Audio Data ---
    try:
        # Read the sample rate and audio data from the WAV file
        sample_rate, data = wavfile.read(file_path)
        print(f"Successfully loaded '{file_path}'.")
        print(f"Sample Rate: {sample_rate} Hz")
        print(f"Data Shape: {data.shape}")

    except FileNotFoundError: #TODO: remove this file not found stuff, not needed
        # If the file is not found, create a dummy signal for demonstration
        print(f"Warning: '{file_path}' not found.")
        print("Generating a dummy audio signal for demonstration purposes.")
        sample_rate = 480000  # A common sample rate
        duration = end_time_s + 10 # A bit longer than needed
        t = np.linspace(0., duration, int(sample_rate * duration), endpoint=False)
        
        # Create a signal with varying frequencies to simulate voice and silence
        amplitude = np.iinfo(np.int16).max * 0.6
        signal = np.zeros_like(t)
        
        # Simulate speech bursts at different frequencies
        for start, end, freq in [(5, 10, 400), (15, 20, 800), (25, 35, 1200), (45, 55, 300)]:
            idx_start, idx_end = int(start * sample_rate), int(end * sample_rate)
            signal[idx_start:idx_end] = np.sin(2. * np.pi * freq * t[idx_start:idx_end])

        data = (amplitude * signal).astype(np.int16)

    # --- 3. Prepare Audio Data for Analysis ---
    # If the audio is stereo, convert it to mono by taking the first channel
    if data.ndim > 1:
        print("Stereo audio detected. Using the first channel.")
        data = data[:, 0]

    # Calculate start and end sample indices from the given times
    start_sample_index = int(start_time_s * sample_rate)
    end_sample_index = int(end_time_s * sample_rate)

    # Clamp the indices to be within the bounds of the audio data
    start_sample_index = max(0, start_sample_index)
    end_sample_index = min(len(data), end_sample_index)

    if start_sample_index >= end_sample_index:
        print("Error: Start time is after the end time or outside the audio duration.")
        return

    # Slice the data to the desired segment
    data_to_process = data[start_sample_index:end_sample_index]
    print(f"Analyzing audio from {start_time_s}s to {end_time_s}s.")
    
    # Calculate the total number of chunks to process
    num_chunks = len(data_to_process) // CHUNK_SIZE_SAMPLES

    if num_chunks == 0:
        print("Error: Audio segment is shorter than the chunk size. Cannot process.")
        return

    # --- 4. Process Chunks and Calculate Power ---
    power_values = []

    print(f"Processing audio in {num_chunks} chunks of {CHUNK_SIZE_SAMPLES} samples each...")

    for i in range(num_chunks):
        # Extract the current chunk from the audio data
        start_chunk_sample = i * CHUNK_SIZE_SAMPLES
        end_chunk_sample = start_chunk_sample + CHUNK_SIZE_SAMPLES
        chunk = data_to_process[start_chunk_sample:end_chunk_sample]

        # Use Welch's method to get a smoothed power spectrum (units: AU**2).
        # Using 'spectrum' scaling makes the power calculation independent of the chunk size.
        freqs, ps = welch(chunk, fs=sample_rate, nperseg=CHUNK_SIZE_SAMPLES, scaling='spectrum')

        # Find the indices corresponding to the frequency range of interest
        freq_indices = np.where((freqs >= FREQ_MIN_HZ) & (freqs <= FREQ_MAX_HZ))

        # Sum the Power Spectrum over the frequency range to get the total power in the band.
        power_in_range = np.sum(ps[freq_indices])
        power_values.append(power_in_range)

    print("Analysis complete.")
    
    # --- 5. Perform Hysteresis Thresholding ---
    power_values_np = np.array(power_values)
    detected_mask = np.zeros(num_chunks, dtype=bool)
    if num_chunks > 0:
        # Start detection for any chunk above the high threshold
        detected_mask[power_values_np > THIGH] = True
        # Propagate detection forward: keep detecting if power is above low threshold
        for i in range(1, num_chunks):
            if detected_mask[i-1] and power_values_np[i] > TLOW:
                detected_mask[i] = True
        # Propagate detection backward: crucial for signals that start below Thigh but are connected
        for i in range(num_chunks - 2, -1, -1):
            if detected_mask[i+1] and power_values_np[i] > TLOW:
                detected_mask[i] = True

    # --- 6. Calculate and Print Signal Durations ---
    chunk_duration_s = CHUNK_SIZE_SAMPLES / sample_rate
    signal_durations = []
    in_signal = False
    start_chunk = 0

    for i in range(num_chunks):
        if detected_mask[i] and not in_signal:
            in_signal = True
            start_chunk = i
        elif not detected_mask[i] and in_signal:
            in_signal = False
            end_chunk = i - 1
            duration = (end_chunk - start_chunk + 1) * chunk_duration_s
            signal_durations.append(duration)
    
    # Handle case where signal is active at the very end of the segment
    if in_signal:
        end_chunk = num_chunks - 1
        duration = (end_chunk - start_chunk + 1) * chunk_duration_s
        signal_durations.append(duration)

    print("\n--- Detected Signal Durations ---")
    if signal_durations:
        for i, duration in enumerate(signal_durations):
            print(f"Signal {i+1}: {duration:.3f} seconds")
    else:
        print("No signals detected in the specified segment.")
    print("---------------------------------\n")


    # --- 7. Plot the Results ---
    if not power_values:
        print("No power values were calculated. Cannot generate plot.")
        return
        
    plt.style.use('seaborn-v0_8-whitegrid')
    # Create a figure with two subplots, one on top of the other
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), gridspec_kw={'height_ratios': [1, 1]})

    # --- Plot 1: Probability Distribution Histogram ---
    power_values_positive = [p for p in power_values if p > 0]
    if not power_values_positive:
        ax1.text(0.5, 0.5, 'All power values are zero.', horizontalalignment='center', verticalalignment='center', transform=ax1.transAxes)
    else:
        min_log = np.log10(min(power_values_positive))
        max_log = np.log10(max(power_values_positive))
        log_bins = np.logspace(min_log, max_log, HIST_BINS)

        ax1.hist(power_values_positive, bins=log_bins, color='skyblue', edgecolor='black',
                 weights=np.ones(len(power_values_positive)) / len(power_values))
        ax1.set_xscale('log')

    ax1.yaxis.set_major_formatter(PercentFormatter(1))
    ax1.set_title(f'Probability Distribution of Power in {FREQ_MIN_HZ}-{FREQ_MAX_HZ} Hz Band', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Power (Logarithmic Scale, Arbitrary Units Squared)', fontsize=12)
    ax1.set_ylabel(f'Percentage of Chunks', fontsize=12)
    ax1.grid(True, which='both', linestyle='--', linewidth=0.5)

    # --- Plot 2: Power over Time with Hysteresis Thresholding ---
    time_axis = start_time_s + np.arange(num_chunks) * CHUNK_SIZE_SAMPLES / sample_rate

    ax2.plot(time_axis, power_values_np, color='coral', linewidth=1, label='Power')
    ax2.set_yscale('log')
    ax2.set_title('Power vs. Time with Signal Detection', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Time (seconds)', fontsize=12)
    ax2.set_ylabel('Power (Logarithmic Scale, Arbitrary Units Squared)', fontsize=12)
    ax2.set_xlim(start_time_s, end_time_s)
    
    if power_values_positive:
        # Set y-limits after plotting to get the full data range
        min_power = min(power_values_positive)
        max_power = max(power_values_np)
        ax2.set_ylim(bottom=0.2, top=max_power * 2.0)
        
        # Highlight the detected regions across the full y-axis of the plot
        current_ylim = ax2.get_ylim()
        ax2.fill_between(time_axis, current_ylim[0], current_ylim[1], where=detected_mask, 
                         facecolor='springgreen', alpha=0.4, label='Detected Signal')

    ax2.axhline(y=THIGH, color='r', linestyle='--', linewidth=1, label=f'Thigh = {THIGH}')
    ax2.axhline(y=TLOW, color='b', linestyle='--', linewidth=1, label=f'Tlow = {TLOW}')
    ax2.legend()
    ax2.grid(True, which='both', linestyle='--', linewidth=0.5)
    
    plt.tight_layout(pad=3.0)
    plt.show()


if __name__ == '__main__':
    # To run this code, save it as a Python file (e.g., analyze_radio.py)
    # and place 'RecordingPi.wav' in the same directory.
    # Then, run `python analyze_radio.py` from your terminal.

    # Example: Analyze a specific segment, e.g., from 10 to 30 seconds.
    # Change these values to analyze your desired time window.
    plot_power_probability_distribution(start_time_s=0, end_time_s=10)
