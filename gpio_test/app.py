import gpiod
import gpiod.line
import time
import os
import sys

# --- Configuration ---
# Get GPIO chip name from environment variable, default to 'gpiochip0'
# On RPi 4/5, this should be 'gpiochip4'. Check with `ls /dev/gpiochip*` on your Pi.
GPIO_CHIP = os.getenv('GPIO_CHIP', 'gpiochip4')

# Get GPIO pin number from environment variable. This MUST be provided.
try:
    GPIO_PIN = int(os.environ['GPIO_PIN'])
except (KeyError, ValueError):
    print("Error: GPIO_PIN environment variable not set or invalid.", file=sys.stderr)
    print("Please run the container with '-e GPIO_PIN=<pin_number>'.", file=sys.stderr)
    sys.exit(1)

# Get blink interval from environment variable, default to 1 second.
BLINK_INTERVAL_S = float(os.getenv('BLINK_INTERVAL_S', 1.0))

# --- Main Application Logic ---
def main():
    """
    Main function to control the GPIO pin.
    It requests the GPIO line and blinks it indefinitely.
    """
    print(f"Starting GPIO blinker on chip '{GPIO_CHIP}', pin {GPIO_PIN}...")
    print(f"Blink interval: {BLINK_INTERVAL_S} seconds.")

    # The 'with' statement ensures that the GPIO line is properly released
    # when the program exits, even if an error occurs.
    try:
        with gpiod.request_lines(
            f'/dev/{GPIO_CHIP}',
            consumer='docker-blinker',
            config={
                GPIO_PIN: gpiod.LineSettings(
                    direction=gpiod.line.Direction.OUTPUT,
                    # Set the initial state to LOW (inactive)
                    output_value=gpiod.line.Value.INACTIVE
                )
            }
        ) as lines:
            print("GPIO line requested successfully. Starting blink loop...")
            # Start with the line active (on) for the first iteration
            value = gpiod.line.Value.ACTIVE
            while True:
                # Set the pin value using the correct enum object
                lines.set_values({GPIO_PIN: value})
                print(f"Pin {GPIO_PIN} set to {'HIGH' if value == gpiod.line.Value.ACTIVE else 'LOW'}")

                # Flip the value for the next iteration
                if value == gpiod.line.Value.ACTIVE:
                    value = gpiod.line.Value.INACTIVE
                else:
                    value = gpiod.line.Value.ACTIVE

                time.sleep(BLINK_INTERVAL_S)

    except FileNotFoundError:
        print(f"Error: GPIO chip '/dev/{GPIO_CHIP}' not found.", file=sys.stderr)
        print("Ensure the container is run with the correct '--device' flag.", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main()
