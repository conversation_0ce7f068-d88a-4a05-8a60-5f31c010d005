# Use a slim, modern Python base image compatible with Raspberry Pi (ARM64)
# 'bookworm' is the Debian version corresponding to the latest Raspberry Pi OS
FROM python:3.11-slim-bookworm

# Set the working directory inside the container
WORKDIR /app

# Install the runtime system dependency required by the gpiod library.
# Using --no-install-recommends keeps the image size down.
RUN apt-get update && \
    apt-get install -y --no-install-recommends libgpiod2 && \
    # Clean up the apt cache to reduce image size
    rm -rf /var/lib/apt/lists/*

# Copy the requirements file into the container
COPY requirements.txt .

# Install the Python dependencies.
# For linux/arm64, pip can use a pre-compiled "wheel" and doesn't need gcc.
RUN pip install --no-cache-dir -r requirements.txt

# Copy the main application file into the container
COPY app.py .

# The command to run when the container starts.
CMD ["python", "-u", "app.py"]