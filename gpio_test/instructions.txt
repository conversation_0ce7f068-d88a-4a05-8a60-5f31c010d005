1. File Structure
Ensure your project directory is set up with the newly corrected app.py code. The Dockerfile remains the same, and requirements.txt should still have gpiod==2.3.0.

rpi-gpio-demo/
├── app.py          <-- Use the corrected code above
├── Dockerfile
└── requirements.txt

2. Build and Push the Container
After updating app.py, you must rebuild the image. This command specifically targets the linux/arm64 platform for your Raspberry Pi 5.

# Navigate to your project directory
cd /path/to/rpi-gpio-demo

# Rebuild the image with the corrected code and push to your registry.
docker buildx build \
  --platform linux/arm64 \
  -t ghcr.io/devtomsuys/rpi-gpio-demo:latest \
  -t ghcr.io/devtomsuys/rpi-gpio-demo:1.0 \
  --push \
  .

3. Run the Container on Your Raspberry Pi
The steps to run the container on your Pi remain the same. Make sure to pull the newly built image first.

# SSH into your Raspberry Pi 5
ssh pi@<your-pi-ip-address>

# Pull the new image you just pushed
docker pull ghcr.io/devtomsuys/rpi-gpio-demo:latest

# Run the container. This command should now work without errors.
docker run --rm -it \
  --device=/dev/gpiochip4 \
  -e GPIO_CHIP='gpiochip4' \
  -e GPIO_PIN=18 \
  -e BLINK_INTERVAL_S=0.5 \
  ghcr.io/devtomsuys/rpi-gpio-demo:latest

By removing the unnecessary linux/arm/v7 platform from the build, we eliminate the source of the error and simplify the Dockerfile significantly. This should now build successfully.