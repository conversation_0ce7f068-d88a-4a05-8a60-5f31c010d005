# HLS Upload Implementation

This document describes the implementation of HLS (HTTP Live Streaming) file upload from Raspberry Pi to VPS using WebSocket connections.

## Overview

The implementation provides a robust, real-time system for uploading HLS segments and playlists from the Raspberry Pi container to the VPS container. The system uses WebSocket connections for efficient, low-latency file transfer with automatic reconnection and error handling.

## Architecture

```
Raspberry Pi Container                    VPS Container
┌─────────────────────┐                  ┌─────────────────────┐
│ AudioEventManager   │                  │ FastAPI Application │
│        ↓            │                  │                     │
│ HLSAudioSubscriber  │                  │ WebSocket Endpoint  │
│        ↓            │   WebSocket      │        ↓            │
│ HLSVPSUploader      │ ←──────────────→ │ HLS Storage         │
│                     │                  │        ↓            │
│ Local HLS Files     │                  │ HLS Serving         │
└─────────────────────┘                  └─────────────────────┘
```

## Components

### Raspberry Pi Side

#### 1. HLSVPSUploader (`sound-player-container/hls_vps_uploader.py`)
- **Purpose**: WebSocket-based uploader for HLS files
- **Features**:
  - Automatic WebSocket connection management
  - Exponential backoff reconnection strategy
  - Threaded upload queue processing
  - Upload statistics and monitoring
  - Robust error handling

#### 2. Configuration
- **Environment Variables**:
  - `HLS_VPS_UPLOAD_URL`: VPS base URL (e.g., `https://awosnew.skytraces.com`)
  - `STATION_ID`: Station identifier for file organization
  - `ENABLE_HLS_STREAMING`: Enable/disable HLS functionality

### VPS Side

#### 1. FastAPI Endpoints (`weather-api-container-new/main.py`)

##### WebSocket Endpoint
- **URL**: `/hls/ws/{station_id}`
- **Purpose**: Real-time file upload via WebSocket
- **Protocol**:
  1. Client sends JSON metadata: `{"type": "segment|playlist", "filename": "...", "size": 123, "timestamp": 1234567890}`
  2. Client sends binary file data
  3. Server responds with JSON confirmation: `{"status": "success|error", "message": "..."}`

##### HTTP Upload Endpoint
- **URL**: `/hls/upload` (POST)
- **Purpose**: Alternative HTTP-based file upload
- **Parameters**:
  - `file`: File to upload
  - `file_type`: "segment" or "playlist"
  - `station_id`: Station identifier
  - `timestamp`: Upload timestamp

##### HLS Serving Endpoints
- **Playlist**: `/hls/{station_id}/playlist.m3u8`
- **Segments**: `/hls/{station_id}/{segment_name}`
- **Status**: `/hls/{station_id}/status`

#### 2. Storage Management
- **Directory**: `/app/hls_storage/{station_id}/`
- **Files**: Segments (*.ts) and playlist (playlist.m3u8)
- **Organization**: Each station has its own subdirectory

## Configuration

### Raspberry Pi Container

Update the docker run command in `sound-player-container/commands.txt`:

```bash
docker run -d --name hlswos \
  # ... other parameters ...
  -e ENABLE_HLS_STREAMING=true \
  -e HLS_VPS_UPLOAD_URL=https://awosnew.skytraces.com \
  -e STATION_ID=185807 \
  # ... rest of configuration ...
```

### VPS Container

The VPS container automatically creates the necessary directories and endpoints. No additional configuration is required beyond the standard deployment.

## WebSocket Protocol

### Upload Flow

1. **Connection**: Client connects to `wss://domain.com/hls/ws/{station_id}`
2. **Metadata**: Client sends JSON with file information
3. **Data**: Client sends binary file content
4. **Confirmation**: Server responds with success/error status
5. **Repeat**: Process continues for each file

### Message Format

#### Metadata Message (JSON)
```json
{
  "type": "segment",           // "segment" or "playlist"
  "filename": "segment_001.ts", // Filename
  "size": 12345,               // File size in bytes
  "timestamp": 1234567890.123  // Upload timestamp
}
```

#### Response Message (JSON)
```json
{
  "status": "success",         // "success" or "error"
  "file_type": "segment",      // Echo of file type
  "filename": "segment_001.ts", // Echo of filename
  "station_id": "185807",      // Station ID
  "timestamp": 1234567890.123, // Processing timestamp
  "size": 12345                // Processed file size
}
```

## Error Handling

### Connection Management
- **Automatic Reconnection**: Exponential backoff (1s to 60s max)
- **Connection Monitoring**: Ping/pong keepalive every 20 seconds
- **Timeout Handling**: 30-second upload timeout
- **Queue Management**: Failed uploads are retried

### Error Recovery
- **Network Issues**: Automatic reconnection with backoff
- **File Errors**: Logged and skipped, processing continues
- **Server Errors**: Retry with exponential backoff
- **Timeout Errors**: Connection reset and retry

## Testing

### Raspberry Pi Tests
Run the test script to validate WebSocket upload functionality:

```bash
cd sound-player-container
python test_hls_upload.py
```

### VPS Tests
Run the test script to validate endpoints:

```bash
cd weather-api-container-new
python test_hls_endpoints.py
```

## Monitoring

### Upload Statistics
The uploader provides statistics via `get_stats()`:
- `uploads_attempted`: Total upload attempts
- `uploads_successful`: Successful uploads
- `uploads_failed`: Failed uploads
- `connection_attempts`: WebSocket connection attempts
- `last_upload_time`: Timestamp of last successful upload
- `last_error`: Last error message
- `connected`: Current connection status

### Logging
Both containers provide detailed logging for monitoring:
- Connection status changes
- Upload success/failure
- Error conditions and recovery
- Performance metrics

## Deployment

### Building Containers

#### Raspberry Pi Container
```bash
cd sound-player-container
docker build -t hlswos:latest .
```

#### VPS Container
```bash
cd weather-api-container-new
docker build -t weather-api:latest .
```

### Running Containers

Use the updated commands in:
- `sound-player-container/commands.txt` for Raspberry Pi
- Standard deployment for VPS (Coolify, Docker Compose, etc.)

## Future Enhancements

1. **Authentication**: Add token-based authentication for uploads
2. **Compression**: Implement file compression for bandwidth optimization
3. **Batch Uploads**: Support for uploading multiple files in one request
4. **CDN Integration**: Direct upload to CDN for global distribution
5. **Metrics Dashboard**: Web interface for monitoring upload statistics
