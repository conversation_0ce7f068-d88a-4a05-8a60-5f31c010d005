import numpy as np
import sounddevice as sd
from scipy.signal import welch
import queue
import sys

# --- 1. Configuration Constants ---
# --- You can adjust these values based on your needs ---

# Audio Stream Settings
DEVICE_NAME = 'hw:0,0'      # The recording device name from `arecord -l`
SAMPLE_RATE = 48000         # Must match the device's supported sample rate
CHANNELS = 1                # Mono audio
CHUNK_SIZE_SAMPLES = 1024   # The number of samples per chunk (block size)

# Analysis Settings
FREQ_MIN_HZ = 200           # Minimum frequency of interest
FREQ_MAX_HZ = 3500          # Maximum frequency of interest

# Hysteresis Thresholding Settings (based on power units)
# You will likely need to re-calibrate these by observing the printed power values.
THIGH = 50000               # High threshold to start signal detection
TLOW = 10000                # Low threshold to stop signal detection


# A queue to pass audio chunks from the callback to the main processing loop
audio_queue = queue.Queue()

def audio_callback(indata, frames, time, status):
    """
    This function is called by the sounddevice stream for each new audio chunk.
    It's designed to be very fast to avoid blocking the audio stream.
    """
    if status:
        # Print any status messages, especially for under/overflows
        print(status, file=sys.stderr)
    # Add the incoming audio data (as a NumPy array) to the queue
    audio_queue.put(indata.copy())

class SignalDetector:
    """
    Manages the state of signal detection using hysteresis.
    """
    def __init__(self, high_threshold, low_threshold):
        self.thigh = high_threshold
        self.tlow = low_threshold
        self.in_signal = False
        self.start_time = None

    def process_chunk(self, chunk, sample_rate):
        """
        Analyzes a single audio chunk and updates the detection state.
        """
        # --- Power Calculation (same logic as the original script) ---
        
        # Use Welch's method for a smoothed power spectrum.
        # 'spectrum' scaling makes power independent of chunk size.
        freqs, ps = welch(chunk, fs=sample_rate, nperseg=len(chunk), scaling='spectrum')

        # Find indices for the frequency range of interest
        freq_indices = np.where((freqs >= FREQ_MIN_HZ) & (freqs <= FREQ_MAX_HZ))

        # Sum the power spectrum over the frequency range
        power_in_range = np.sum(ps[freq_indices])

        # --- Hysteresis Logic ---
        if not self.in_signal and power_in_range > self.thigh:
            self.in_signal = True
            print(f"Signal DETECTED. Power: {power_in_range:.2f}")

        elif self.in_signal and power_in_range < self.tlow:
            self.in_signal = False
            print(f"Signal ENDED.      Power: {power_in_range:.2f}")
        
        # Provide continuous feedback on the current power level
        # This is useful for calibrating THIGH and TLOW.
        # Use carriage return `\r` to overwrite the same line in the terminal.
        print(f"Current Power: {power_in_range:<15.2f} | Signal Active: {'YES' if self.in_signal else 'NO '}", end='\r')


def main():
    """
    Sets up the audio stream and runs the main processing loop.
    """
    print("--- Real-Time Signal Analyser ---")
    
    # Instantiate the detector
    detector = SignalDetector(THIGH, TLOW)

    try:
        # --- List devices to help the user find the correct one ---
        print("\nAvailable audio devices:")
        print(sd.query_devices())
        print("------------------------------------")
        
        # --- Set up and start the audio stream ---
        # The `with` statement ensures the stream is properly closed.
        with sd.InputStream(
            device=DEVICE_NAME,
            channels=CHANNELS,
            samplerate=SAMPLE_RATE,
            blocksize=CHUNK_SIZE_SAMPLES,
            callback=audio_callback,
            dtype='int16'  # Matches the format from your arecord command
        ):
            print(f"\nListening on device '{DEVICE_NAME}'... Press Ctrl+C to stop.")
            
            # --- Main Processing Loop ---
            while True:
                # Get a chunk of audio from the queue
                chunk_mono = audio_queue.get()[:, 0]
                
                # Process the chunk
                detector.process_chunk(chunk_mono, SAMPLE_RATE)

    except KeyboardInterrupt:
        print("\nStopping analysis.")
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        print("Please check the following:")
        print(f"1. Is the device name '{DEVICE_NAME}' correct? Use `arecord -l` on your Pi to verify.")
        print(f"2. Is the sample rate {SAMPLE_RATE} Hz supported by the device?")
        print("3. Are the required libraries installed? (pip install sounddevice numpy scipy)")

if __name__ == '__main__':
    main()