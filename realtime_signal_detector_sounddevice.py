#!/usr/bin/env python3
"""
Real-Time Signal Detector using SoundDevice

Alternative implementation using sounddevice library instead of PyAudio.
SoundDevice is often easier to install and has better cross-platform support.
"""

import numpy as np
import sounddevice as sd
import threading
import time
import queue
from collections import deque
from scipy.signal import welch
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from datetime import datetime
import argparse
import sys

class RealTimeSignalDetectorSD:
    def __init__(self, 
                 device=None,
                 sample_rate=48000,
                 chunk_size=1024,
                 freq_min=200,
                 freq_max=3500,
                 thigh=50000,
                 tlow=100,
                 buffer_duration=10.0,
                 enable_plot=True):
        """
        Initialize real-time signal detector using SoundDevice
        
        Args:
            device: Audio device name or index (None for default)
            sample_rate: Sample rate in Hz (48000 to match your arecord command)
            chunk_size: Chunk size in samples (1024 to match signalanalyser.py)
            freq_min: Minimum frequency of interest in Hz
            freq_max: Maximum frequency of interest in Hz
            thigh: High threshold for hysteresis
            tlow: Low threshold for hysteresis
            buffer_duration: Duration of audio buffer to keep in seconds
            enable_plot: Whether to show real-time plot
        """
        # Audio parameters
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.device = device
        
        # Detection parameters (from signalanalyser.py)
        self.freq_min = freq_min
        self.freq_max = freq_max
        self.thigh = thigh
        self.tlow = tlow
        
        # Buffer management
        self.buffer_duration = buffer_duration
        self.max_buffer_chunks = int(buffer_duration * sample_rate / chunk_size)
        
        # Data storage
        self.power_buffer = deque(maxlen=self.max_buffer_chunks)
        self.detection_buffer = deque(maxlen=self.max_buffer_chunks)
        self.time_buffer = deque(maxlen=self.max_buffer_chunks)
        
        # Detection state
        self.current_detection_state = False
        self.signal_start_time = None
        self.detected_signals = []
        
        # Plotting
        self.enable_plot = enable_plot
        self.fig = None
        self.ax1 = None
        self.ax2 = None
        
        # Stream
        self.stream = None
        self.running = False
        
        print(f"Initialized Real-Time Signal Detector (SoundDevice):")
        print(f"  Sample Rate: {self.sample_rate} Hz")
        print(f"  Chunk Size: {self.chunk_size} samples")
        print(f"  Frequency Range: {self.freq_min}-{self.freq_max} Hz")
        print(f"  Thresholds: High={self.thigh}, Low={self.tlow}")
        print(f"  Buffer Duration: {self.buffer_duration}s")

    def list_audio_devices(self):
        """List available audio input devices"""
        print("\nAvailable Audio Input Devices:")
        print("-" * 50)
        devices = sd.query_devices()
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                print(f"Device {i}: {device['name']}")
                print(f"  Max Input Channels: {device['max_input_channels']}")
                print(f"  Default Sample Rate: {device['default_samplerate']}")
                print()

    def audio_callback(self, indata, frames, time_info, status):
        """SoundDevice callback function"""
        if status:
            print(f"Audio callback status: {status}")
        
        # Convert to 1D array and scale to int16 range for consistency
        audio_data = (indata[:, 0] * 32767).astype(np.int16)
        
        # Process immediately in callback (SoundDevice runs in separate thread)
        timestamp = time.time()
        self.process_audio_chunk(audio_data, timestamp)

    def process_audio_chunk(self, chunk, timestamp):
        """
        Process a single audio chunk using the same logic as signalanalyser.py
        """
        # Calculate power spectrum using Welch's method (same as signalanalyser.py)
        freqs, ps = welch(chunk, fs=self.sample_rate, nperseg=self.chunk_size, scaling='spectrum')
        
        # Find frequency indices for our range of interest
        freq_indices = np.where((freqs >= self.freq_min) & (freqs <= self.freq_max))
        
        # Sum power in frequency range
        power_in_range = np.sum(ps[freq_indices])
        
        # Store in buffers
        self.power_buffer.append(power_in_range)
        self.time_buffer.append(timestamp)
        
        # Perform hysteresis detection (same logic as signalanalyser.py)
        detection = self.update_detection_state(power_in_range, timestamp)
        self.detection_buffer.append(detection)
        
        return power_in_range, detection

    def update_detection_state(self, power, timestamp):
        """
        Update detection state using hysteresis thresholding
        """
        detection = False
        
        if power > self.thigh:
            # Signal above high threshold - start detection
            if not self.current_detection_state:
                self.signal_start_time = timestamp
                print(f"Signal START detected at {datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]} (Power: {power:.0f})")
            detection = True
            self.current_detection_state = True
            
        elif power > self.tlow and self.current_detection_state:
            # Signal above low threshold and we're already detecting - continue detection
            detection = True
            
        else:
            # Signal below low threshold - stop detection
            if self.current_detection_state:
                duration = timestamp - self.signal_start_time
                self.detected_signals.append({
                    'start_time': self.signal_start_time,
                    'end_time': timestamp,
                    'duration': duration
                })
                print(f"Signal END detected at {datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]} (Duration: {duration:.3f}s)")
            detection = False
            self.current_detection_state = False
            
        return detection

    def start_detection(self):
        """Start real-time detection"""
        try:
            self.running = True
            
            # Create input stream
            self.stream = sd.InputStream(
                device=self.device,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32,
                callback=self.audio_callback
            )
            
            # Start stream
            self.stream.start()
            
            print(f"\nReal-time detection started!")
            print(f"Using audio device: {self.device if self.device else 'default'}")
            print("Press Ctrl+C to stop detection\n")
            
            # Setup plotting if enabled
            if self.enable_plot:
                self.setup_plot()
                plt.show()
            else:
                # Keep running until interrupted
                try:
                    while self.running:
                        time.sleep(0.1)
                except KeyboardInterrupt:
                    pass
                    
        except Exception as e:
            print(f"Error starting detection: {e}")
            self.stop_detection()

    def stop_detection(self):
        """Stop real-time detection"""
        print("\nStopping detection...")
        
        self.running = False
        
        if self.stream:
            self.stream.stop()
            self.stream.close()
        
        # Print summary
        print(f"\nDetection Summary:")
        print(f"Total signals detected: {len(self.detected_signals)}")
        for i, signal in enumerate(self.detected_signals):
            start_str = datetime.fromtimestamp(signal['start_time']).strftime('%H:%M:%S.%f')[:-3]
            print(f"  Signal {i+1}: {start_str} - Duration: {signal['duration']:.3f}s")

    def setup_plot(self):
        """Setup real-time plotting"""
        plt.style.use('seaborn-v0_8-whitegrid')
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # Animation
        ani = FuncAnimation(self.fig, self.update_plot, interval=100, blit=False)
        
        # Handle window close
        self.fig.canvas.mpl_connect('close_event', lambda evt: self.stop_detection())

    def update_plot(self, frame):
        """Update real-time plot"""
        if len(self.power_buffer) < 2:
            return
            
        # Convert to numpy arrays
        powers = np.array(list(self.power_buffer))
        detections = np.array(list(self.detection_buffer))
        times = np.array(list(self.time_buffer))
        
        # Calculate relative time (seconds ago)
        current_time = time.time()
        relative_times = times - current_time
        
        # Clear and plot power
        self.ax1.clear()
        self.ax1.plot(relative_times, powers, 'coral', linewidth=1, label='Power')
        self.ax1.axhline(y=self.thigh, color='r', linestyle='--', linewidth=1, label=f'Thigh = {self.thigh}')
        self.ax1.axhline(y=self.tlow, color='b', linestyle='--', linewidth=1, label=f'Tlow = {self.tlow}')
        
        # Highlight detected regions
        if np.any(detections):
            self.ax1.fill_between(relative_times, self.ax1.get_ylim()[0], self.ax1.get_ylim()[1], 
                                where=detections, facecolor='springgreen', alpha=0.4, label='Detected Signal')
        
        self.ax1.set_title('Real-Time Signal Detection', fontsize=16, fontweight='bold')
        self.ax1.set_ylabel('Power (Log Scale)', fontsize=12)
        self.ax1.set_yscale('log')
        self.ax1.legend()
        self.ax1.grid(True, which='both', linestyle='--', linewidth=0.5)
        
        # Plot detection state
        self.ax2.clear()
        self.ax2.plot(relative_times, detections.astype(float), 'g-', linewidth=2, label='Detection State')
        self.ax2.fill_between(relative_times, 0, detections.astype(float), alpha=0.3, color='green')
        self.ax2.set_title('Detection State', fontsize=14)
        self.ax2.set_xlabel('Time (seconds ago)', fontsize=12)
        self.ax2.set_ylabel('Detection', fontsize=12)
        self.ax2.set_ylim(-0.1, 1.1)
        self.ax2.legend()
        self.ax2.grid(True, linestyle='--', linewidth=0.5)


def main():
    parser = argparse.ArgumentParser(description='Real-Time Signal Detector (SoundDevice)')
    parser.add_argument('--device', help='Audio device name or index (use --list-devices to see options)')
    parser.add_argument('--list-devices', action='store_true', help='List available audio devices')
    parser.add_argument('--no-plot', action='store_true', help='Disable real-time plotting')
    parser.add_argument('--thigh', type=float, default=50000, help='High threshold for detection')
    parser.add_argument('--tlow', type=float, default=100, help='Low threshold for detection')
    
    args = parser.parse_args()
    
    # Convert device argument to int if it's numeric
    device = args.device
    if device and device.isdigit():
        device = int(device)
    
    # Create detector
    detector = RealTimeSignalDetectorSD(
        device=device,
        thigh=args.thigh,
        tlow=args.tlow,
        enable_plot=not args.no_plot
    )
    
    if args.list_devices:
        detector.list_audio_devices()
        return
    
    try:
        detector.start_detection()
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    finally:
        detector.stop_detection()


if __name__ == '__main__':
    main()
