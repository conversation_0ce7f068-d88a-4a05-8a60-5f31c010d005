import matplotlib.pyplot as plt
import numpy as np
from scipy.io import wavfile
from scipy import signal # Imported for more flexible spectrogram generation

def analyze_wav(file_path, nfft, noverlap, freq_range_display, starttime=None, endtime=None):
    """
    Reads a .wav file and plots its spectrogram for a specified time range.

    This function helps in visually identifying audio events by displaying
    frequency content over time.

    Args:
        file_path (str): The full path to the .wav file.
        nfft (int): The number of data points used in each block for the FFT.
                    Affects frequency resolution. A power of 2 is efficient.
        noverlap (int): The number of points of overlap between blocks.
                        Should be less than nfft.
        freq_range_display (tuple): A tuple (min_hz, max_hz) to set the y-axis
                                    of the spectrogram.
        starttime (float, optional): The start time in seconds of the segment to analyze.
                                     Defaults to None (start of file).
        endtime (float, optional): The end time in seconds of the segment to analyze.
                                   Defaults to None (end of file).
    """
    try:
        # --- 1. Read the WAV file ---
        samplerate, data = wavfile.read(file_path)
        duration = len(data) / samplerate
        print(f"Successfully loaded '{file_path}'")
        print(f"Sample Rate: {samplerate} Hz")
        print(f"Original Duration: {duration:.2f} seconds")

        # --- 2. Prepare the data ---
        if data.ndim > 1:
            print("Stereo audio detected. Using the left channel for analysis.")
            data = data[:, 0]

        # --- Slice audio data if a time range is specified ---
        if starttime is not None and endtime is not None:
            # Validate time range
            if starttime < 0 or endtime <= starttime or endtime > duration:
                print(f"Error: Invalid time range. Start must be < End, and End must be <= total duration ({duration:.2f}s).")
                return

            print(f"Slicing audio from {starttime:.2f}s to {endtime:.2f}s.")
            start_sample = int(starttime * samplerate)
            end_sample = int(endtime * samplerate)
            data_to_plot = data[start_sample:end_sample]
        else:
            print("Analyzing full audio file.")
            data_to_plot = data
            starttime = 0  # Set to 0 to correctly offset the time axis later

        # --- 3. Create the plot ---
        fig, ax = plt.subplots(figsize=(12, 6))
        fig.suptitle(f'Spectrogram of {file_path}', fontsize=16)

        # --- Plot Spectrogram (Frequency vs. Time) ---
        print("\n--- Spectrogram Parameters ---")
        print(f"NFFT (Chunk Size): {nfft}")
        print(f"Overlap: {noverlap}")
        if freq_range_display:
            print(f"Frequency Display Range: {freq_range_display[0]}-{freq_range_display[1]} Hz")

        # Use scipy.signal.spectrogram for more control over the plot
        # 'nperseg' is the equivalent of NFFT in this context
        freqs, times, Sxx = signal.spectrogram(data_to_plot, fs=samplerate, nperseg=nfft, noverlap=noverlap)

        # Adjust the time axis to reflect the slice's position in the original file
        times += starttime

        # Plot the spectrogram using pcolormesh for accurate axis representation
        # Convert power (Sxx) to decibels (dB) for a better visual scale
        im = ax.pcolormesh(times, freqs, 10 * np.log10(Sxx + 1e-9), shading='gouraud', cmap=plt.cm.viridis)

        # Set the y-axis to the desired frequency range
        if freq_range_display:
            ax.set_ylim(freq_range_display)

        ax.set_title("Frequency over Time")
        ax.set_ylabel("Frequency (Hz)")
        ax.set_xlabel("Time (s)")

        fig.colorbar(im, ax=ax).set_label('Intensity (dB)')

        # --- 4. Display the plot ---
        plt.tight_layout(rect=[0, 0, 1, 0.94])
        plt.show()

    except FileNotFoundError:
        print(f"Error: The file '{file_path}' was not found.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

# --- Main execution block ---
if __name__ == "__main__":
    # --- CONFIGURATION ---
    filename = 'apopkus_test_x02444.wav'

    # (1) Spectrogram Frequency Range to Display (min_hz, max_hz)
    display_frequency_range = (100, 8000)

    # (2) NFFT - Chunk size for FFT analysis.
    nfft_size = 256

    # (3) Overlap - How many samples the chunks overlap.
    noverlap_size = 128

    # (4) Time Range to Analyze (in seconds)
    # Set both to None to analyze the full file duration.
    start_time_s = 0
    end_time_s = 39.8
    if noverlap_size >= nfft_size:
        print(f"Error: noverlap_size ({noverlap_size}) must be less than nfft_size ({nfft_size}).")
    else:
        analyze_wav(
            file_path=filename,
            nfft=nfft_size,
            noverlap=noverlap_size,
            freq_range_display=display_frequency_range,
            starttime=start_time_s,
            endtime=end_time_s
        )